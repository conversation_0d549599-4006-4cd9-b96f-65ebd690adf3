import Foundation

struct PlansRetentionResult: Sendable, Equatable {
    let plan: MealPlan
    let removedWeeks: Int
}

enum PlansRetentionManager {
    /// Apply retention to a merged plan: keep only the most recent N calendar weeks.
    /// Returns a new plan and the count of weeks removed.
    static func applyRetention(to mergedPlan: MealPlan,
                               keepWeeks: Int = RetentionPolicy.plansWeeksToKeep,
                               calendar: Calendar = .current) -> PlansRetentionResult {
        let (keptDays, removed) = RetentionPolicy.trimToRecentWeeks(mergedPlan.days, keepWeeks: keepWeeks, calendar: calendar)
        // Sort by date ascending and re-normalize dayIndex inside slot recipes
        let sorted = keptDays.sorted { $0.date < $1.date }
        var normalized: [DayPlan] = []
        normalized.reserveCapacity(sorted.count)

        for (idx, day) in sorted.enumerated() {
            let newMeals: [MealSlot] = day.meals.map { slot in
                let r = RecipeUIModel(
                    id: slot.recipe.id,
                    title: slot.recipe.title,
                    subtitle: slot.recipe.subtitle,
                    estimatedTime: slot.recipe.estimatedTime,
                    imageURL: slot.recipe.imageURL,
                    ingredientsFromPantry: slot.recipe.ingredientsFromPantry,
                    additionalIngredients: slot.recipe.additionalIngredients,
                    difficulty: slot.recipe.difficulty,
                    mealType: slot.mealType,
                    dayIndex: idx,
                    servings: slot.recipe.servings,
                    cuisine: slot.recipe.cuisine,
                    scheduledDate: day.date
                )
                return MealSlot(slotId: slot.slotId, dayIndex: idx, mealType: slot.mealType, recipe: r)
            }
            normalized.append(DayPlan(date: day.date, meals: newMeals))
        }

        return PlansRetentionResult(plan: MealPlan(days: normalized), removedWeeks: removed)
    }
}
