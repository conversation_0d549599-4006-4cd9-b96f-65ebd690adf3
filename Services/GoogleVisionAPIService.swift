import Foundation
import UIKit




actor GoogleVisionAPIService {
    private let apiKey = APIKeys.googleVisionAPIKey
    private let baseURL = "https://vision.googleapis.com/v1/images:annotate"

    // MARK: - Data Structures

    struct VisionResult {
        let detectedText: String
        let detectedLabels: [String]

        var combinedContent: String {
            var content = detectedText
            if !detectedLabels.isEmpty {
                content += "\n\nDetected Items: " + detectedLabels.joined(separator: ", ")
            }
            return content
        }
    }

    // New VisionOutput struct for batch processing
    struct VisionOutput: Equatable {
        let ocrText: String
        let labels: [String]

        init(ocrText: String, labels: [String]) {
            self.ocrText = ocrText
            self.labels = labels
        }

        static func == (lhs: VisionOutput, rhs: VisionOutput) -> Bool {
            return lhs.ocrText == rhs.ocrText && lhs.labels == rhs.labels
        }
    }

    // MARK: - Batch Analysis Methods

    // MARK: - Async Image Preparation (moved to AsyncImagePreparationService)
    // All image preparation logic has been consolidated into AsyncImagePreparationService.

    // MARK: - True Batch Processing (iOS 17+ canonical)

    /// Single API call with multiple images using Google Vision API native batching
    func batchAnalyze(preparedImages: [PreparedImage]) async throws -> [VisionOutput] {
        guard !preparedImages.isEmpty else {
            return []
        }

        // Build single request with multiple image objects
        let requests = preparedImages.map { preparedImage in
            return [
                "image": ["content": preparedImage.base64String],
                "features": [
                    ["type": "TEXT_DETECTION", "maxResults": 10],
                    ["type": "LABEL_DETECTION", "maxResults": 10]
                ]
            ]
        }

        let requestBody: [String: Any] = [
            "requests": requests  // Multiple images in ONE request
        ]

        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw VisionError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 45.0 // Increased timeout for batch requests

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw VisionError.encodingError
        }

        // Single API call for all images
        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw VisionError.networkError
            }

            // Parse multiple responses
            return try parseMultipleVisionResponses(data)

        } catch {
            throw VisionError.networkError
        }
    }

    // MARK: - Per-image Analysis (replicate beforeios17 fast path)

    /// Analyze a single image with lightweight features (TEXT_DETECTION:1, LABEL_DETECTION:20)
    func analyzeSingle(preparedImageBase64: String) async throws -> VisionOutput {
        guard let url = URL(string: "\(baseURL)?key=\(apiKey)") else {
            throw VisionError.invalidURL
        }

        let requestBody: [String: Any] = [
            "requests": [[
                "image": ["content": preparedImageBase64],
                "features": [
                    ["type": "TEXT_DETECTION", "maxResults": 1],
                    ["type": "LABEL_DETECTION", "maxResults": 20]
                ]
            ]]
        ]

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        // Per-image requests should not need the long timeout used for batches
        request.timeoutInterval = 20.0

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        } catch {
            throw VisionError.encodingError
        }

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                throw VisionError.networkError
            }
            guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let responses = json["responses"] as? [[String: Any]],
                  let responseObject = responses.first else {
                throw VisionError.invalidResponse
            }
            return try parseVisionResponse(responseObject)
        } catch {
            throw VisionError.networkError
        }
    }


    /// Parse multiple Vision API responses from a single batch request
    private func parseMultipleVisionResponses(_ data: Data) throws -> [VisionOutput] {
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let responses = json["responses"] as? [[String: Any]] else {
            throw VisionError.invalidResponse
        }

        var outputs: [VisionOutput] = []

        for response in responses {
            // Parse each response using the same logic as detectTextAndLabels
            let visionOutput = try parseVisionResponse(response)
            outputs.append(visionOutput)
        }

        return outputs
    }

    /// Parse a single Vision API response into VisionOutput
    private func parseVisionResponse(_ response: [String: Any]) throws -> VisionOutput {
        // Extract text (same logic as detectTextAndLabels)
        var detectedText = ""
        if let textAnnotations = response["textAnnotations"] as? [[String: Any]],
           let fullText = textAnnotations.first?["description"] as? String {
            detectedText = fullText
        }

        // Extract labels (same logic as detectTextAndLabels)
        var detectedLabels: [String] = []
        if let labelAnnotations = response["labelAnnotations"] as? [[String: Any]] {
            detectedLabels = labelAnnotations.compactMap { annotation in
                guard let description = annotation["description"] as? String,
                      let score = annotation["score"] as? Double,
                      score > 0.7 else {
                    return nil
                }
                return description
            }
        }

        return VisionOutput(ocrText: detectedText, labels: detectedLabels)
    }

    // MARK: - Error Types

    enum VisionError: Error, LocalizedError {
        case invalidImage
        case invalidURL
        case encodingError
        case networkError
        case invalidResponse

        var errorDescription: String? {
            switch self {
            case .invalidImage:
                return "Invalid image data"
            case .invalidURL:
                return "Invalid API URL"
            case .encodingError:
                return "Failed to encode request"
            case .networkError:
                return "Network request failed"
            case .invalidResponse:
                return "Invalid API response"
            }
        }
    }
}