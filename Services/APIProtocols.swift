import Foundation

// MARK: - Request DTO (legacy placeholder for P0) — renamed to avoid conflict with v3 model
struct LegacyRecipeGenerationRequest: Sendable {
    var mode: UIMode
    var pantryItemCount: Int
}

// MARK: - API Service Protocol (P0 injection only)
protocol APIServiceProtocol {
    func generateRecipes(_ request: LegacyRecipeGenerationRequest) async throws -> [Recipe]
}

// MARK: - Recipe Generation Service Protocol (for DI/testing)
protocol RecipeGenerationServiceProtocol: Sendable {
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea]
    func generateStructuredMealPlan(_ planRequest: MealPlanGenerationRequest,
                                    pantryService: PantryService,
                                    authService: AuthenticationService) async throws -> MealPlan
}

// Provide a default implementation so existing test mocks don't need to implement it unless required
extension RecipeGenerationServiceProtocol {
    func generateStructuredMealPlan(_ planRequest: MealPlanGenerationRequest,
                                    pantryService: PantryService,
                                    authService: AuthenticationService) async throws -> MealPlan {
        // Default fallback for mocks that don't override this method
        throw RecipeGenerationError.invalidRequest
    }
}

// MARK: - Pantry Service Protocol (state adapter)
@MainActor
protocol PantryServiceProtocol {
    var currentState: PantryState { get }
    func checkPantryState() async -> PantryState
    func observePantryChanges() -> AsyncStream<PantryState>
}

// Default pantry protocol adapter backed by PantryStateProvider
@MainActor
final class DefaultPantryServiceAdapter: PantryServiceProtocol {
    private let provider: PantryStateProvider
    init(provider: PantryStateProvider) { self.provider = provider }
    var currentState: PantryState { provider.currentState }
    func checkPantryState() async -> PantryState { await provider.checkPantryState() }
    func observePantryChanges() -> AsyncStream<PantryState> { provider.observePantryChanges() }
}
