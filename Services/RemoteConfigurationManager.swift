import Foundation
import Combine

/// Remote configuration manager (P1)
/// - fetch -> cache -> observe
/// - 24h cache using UserDefaults
/// - fallback to bundle JSON or hardcoded default
@MainActor
final class RemoteConfigurationManager: ObservableObject {
    static let shared = RemoteConfigurationManager()

    @Published private(set) var configuration: RemoteConfiguration = RemoteConfiguration.default

    private let userDefaults = UserDefaults.standard
    private let cacheKey = "remote_config_cache_v1"
    private let timestampKey = "remote_config_timestamp_v1"
    private let cacheTTL: TimeInterval = 24 * 60 * 60

    private var isFetching = false

    private init() {
        // Load from cache/bundle on init but do not block UI
        Task { await self.bootstrap() }
    }

    private func bootstrap() async {
        // Try cache first
        if let cached = loadFromCache(), !isCacheExpired() {
            self.configuration = cached
            return
        }
        // Try bundle
        if let bundleCfg = RemoteConfiguration.loadFromBundle() {
            self.configuration = bundleCfg
        }
        // Kick off async refresh (non-blocking)
        _ = await refreshIfNeeded(force: true)
    }

    /// Public accessor with observation
    func observe() -> AnyPublisher<RemoteConfiguration, Never> {
        $configuration.removeDuplicates().eraseToAnyPublisher()
    }

    /// Refresh from remote endpoint (stub for now). Does not throw; on failure keeps current config.
    @discardableResult
    func refreshIfNeeded(force: Bool = false) async -> Bool {
        if isFetching { return false }
        if !force, let _ = loadFromCache(), !isCacheExpired() { return false }
        isFetching = true
        defer { isFetching = false }
        do {
            _ = Date()
            let cfg = try await fetchRemoteStub()
            self.configuration = cfg
            saveToCache(cfg)
            return true
        } catch {
            // On failure, keep existing config (bundle or default)
            return false
        }
    }

    // MARK: - Cache
    private func saveToCache(_ cfg: RemoteConfiguration) {
        do {
            let data = try JSONEncoder().encode(cfg)
            userDefaults.set(data, forKey: cacheKey)
            userDefaults.set(Date().timeIntervalSince1970, forKey: timestampKey)
        } catch {
            // Ignore cache errors
        }
    }

    private func loadFromCache() -> RemoteConfiguration? {
        guard let data = userDefaults.data(forKey: cacheKey) else { return nil }
        return try? JSONDecoder().decode(RemoteConfiguration.self, from: data)
    }

    private func isCacheExpired() -> Bool {
        let ts = userDefaults.double(forKey: timestampKey)
        guard ts > 0 else { return true }
        return Date().timeIntervalSince1970 - ts > cacheTTL
    }

    // MARK: - Fetch (enhanced with real network capability)
    private func fetchRemoteStub() async throws -> RemoteConfiguration {
        // Try to fetch from a real endpoint if available
        if let remoteConfig = try? await fetchFromRemoteEndpoint() {
            return remoteConfig
        }

        // Fallback: simulate network latency and return default
        try await Task.sleep(nanoseconds: 300_000_000)
        return RemoteConfiguration.default
    }

    private func fetchFromRemoteEndpoint() async throws -> RemoteConfiguration {
        // This would be the real implementation for fetching from a remote endpoint
        // For now, we'll simulate a network request
        guard let url = URL(string: "https://api.example.com/recipe-config") else {
            throw URLError(.badURL)
        }

        let (data, response) = try await URLSession.shared.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw URLError(.badServerResponse)
        }

        let decoder = JSONDecoder()
        return try decoder.decode(RemoteConfiguration.self, from: data)
    }
}

