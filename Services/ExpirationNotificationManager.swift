import Foundation
@preconcurrency import UserNotifications
import SwiftUI

/// Local-only expiration notification manager
/// - Stores nothing in cloud; reads toggle from UserDefaults via @AppStorage key
@MainActor
final class ExpirationNotificationManager: NSObject {
    static let shared = ExpirationNotificationManager()
    private let notificationDelegate = ExpirationNotificationDelegate()
    private override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = notificationDelegate
    }

    // The six categories to monitor
    private let monitoredCategories: Set<PantryCategory> = [
        .proteins, .seafood, .dairy, .produce, .bakery, .plantBasedAlternatives
    ]

    private let reminderKey = "foodExpirationReminderEnabled"
    private let notificationCategoryIdentifier = "EXPIRY_ALERT"

    // Request notification permission if not granted
    func requestAuthorizationIfNeeded() async {
        let settings = await UNUserNotificationCenter.current().notificationSettings()
        if settings.authorizationStatus == .notDetermined {
            do {
                let granted = try await UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound])
                if !granted {
                    print("⚠️ Notifications permission not granted")
                }
            } catch {
                print("❌ Failed to request notification authorization: \(error)")
            }
        }
        // Register category (for potential future actions)
        let category = UNNotificationCategory(identifier: notificationCategoryIdentifier, actions: [], intentIdentifiers: [], options: [.customDismissAction])
        UNUserNotificationCenter.current().setNotificationCategories([category])
    }

    // Public entry to check and schedule alerts
    func checkAndScheduleAlerts(pantryItems: [Ingredient]) async {
        let enabled = UserDefaults.standard.bool(forKey: reminderKey)
        guard enabled else { return }
        await requestAuthorizationIfNeeded()

        _ = UNUserNotificationCenter.current()
        // Phase 4: do not clear all; we'll selectively update per-item identifiers

        let now = Date()
        let sevenDays: TimeInterval = 7 * 24 * 60 * 60

        // Buffer ingredients that require a new-cycle notification this run
        var toNotify: [(item: Ingredient, currentCycle: Int)] = []

        for item in pantryItems where monitoredCategories.contains(item.category) {
            let elapsed = now.timeIntervalSince(item.purchaseDate)
            guard elapsed >= sevenDays else { continue }
            let currentCycle = max(1, Int(elapsed / sevenDays)) // number of completed 7-day periods
            if currentCycle > item.notificationCycle {
                toNotify.append((item, currentCycle))
            }
        }

        guard !toNotify.isEmpty else { return }

        // Schedule per-item noon notifications for the due cycle
        for entry in toNotify {
            await scheduleNoonNotification(for: entry.item, cycle: entry.currentCycle)
        }

        // Persist updated notificationCycle for notified items
        var updates: [UUID: Int] = [:]
        for entry in toNotify { updates[entry.item.id] = entry.currentCycle }
        await ServiceContainer.shared.pantryService.updateNotificationCycles(updates)
    }

    private func scheduleNoonNotification(for item: Ingredient, cycle: Int) async {
        let cal = Calendar.current

        let content = UNMutableNotificationContent()
        content.title = "食材过期提醒"
        content.body = "您的\(item.name)可能快过期了，请及时检查。"
        content.sound = .default
        content.userInfo = ["deepLink": "ingredientscanner://pantry"]
        content.categoryIdentifier = notificationCategoryIdentifier

        // Compute next due day boundary at 12:00 local time
        let start = cal.startOfDay(for: item.purchaseDate)
        let dueDays = cycle * 7
        guard let dueDate = cal.date(byAdding: Calendar.Component.day, value: dueDays, to: start) else { return }
        // If suppression flag exists for today and due date is today, skip
        if let suppress = UserDefaults.standard.object(forKey: "expirationSuppressNoonDate") as? Date,
           cal.isDate(suppress, inSameDayAs: Date()),
           cal.isDate(dueDate, inSameDayAs: Date()) {
            return
        }
        var components = cal.dateComponents([Calendar.Component.year, Calendar.Component.month, Calendar.Component.day], from: dueDate)
        components.hour = 12
        components.minute = 0

        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        let identifier = "expiry_\(item.id.uuidString)_cycle_\(cycle)"

        // Remove any previous request for this item-cycle before adding
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])

        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("🔔 Scheduled noon expiry notification for: \(item.name) cycle=\(cycle)")
        } catch {
            print("❌ Failed to schedule noon notification: \(error)")
        }
    }
}

final class ExpirationNotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([.banner, .sound])
    }

    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        if let urlString = response.notification.request.content.userInfo["deepLink"] as? String {
            NotificationCenter.default.post(name: Notification.Name("openDeepLink"), object: nil, userInfo: ["url": urlString])
        }
        completionHandler()
    }
}




