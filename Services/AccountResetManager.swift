@preconcurrency import ObjectiveC
import Foundation
import SwiftUI
@preconcurrency import <PERSON>base<PERSON>uth
@preconcurrency import FirebaseFirestore

@MainActor
final class AccountResetManager: ObservableObject {
    static let shared = AccountResetManager()

    @Published var isResetting = false
    @Published var resetProgress: ResetProgress = .idle
    @Published var resetError: Error?

    enum ResetStage: CaseIterable, Equatable {
        case clearingSwiftData
        case clearingFavorites
        case clearingPlans
        case clearingHistory
        case clearingCache
        case clearingUserDefaults
        case restoringDefaults
        case syncingToCloud

        var description: String {
            switch self {
            case .clearingSwiftData:
                return "Clearing local SwiftData..."
            case .clearingFavorites:
                return "Clearing favorites..."
            case .clearingPlans:
                return "Clearing meal plans..."
            case .clearingHistory:
                return "Clearing quick history..."
            case .clearingCache:
                return "Clearing cached files..."
            case .clearingUserDefaults:
                return "Resetting preferences..."
            case .restoringDefaults:
                return "Restoring defaults..."
            case .syncingToCloud:
                return "Syncing with Firebase..."
            }
        }

        var progressValue: Double {
            switch self {
            case .clearingSwiftData: return 0.1
            case .clearingFavorites: return 0.2
            case .clearingPlans: return 0.3
            case .clearingHistory: return 0.4
            case .clearingCache: return 0.5
            case .clearingUserDefaults: return 0.6
            case .restoringDefaults: return 0.8
            case .syncingToCloud: return 0.9
            }
        }

        var failureLabel: String { description }
    }

    enum ResetProgress: Equatable {
        case idle
        case stage(ResetStage)
        case completed

        var description: String {
            switch self {
            case .idle:
                return "Ready"
            case .stage(let stage):
                return stage.description
            case .completed:
                return "Reset complete"
            }
        }

        var progressValue: Double {
            switch self {
            case .idle:
                return 0.0
            case .stage(let stage):
                return stage.progressValue
            case .completed:
                return 1.0
            }
        }
    }

    enum ResetError: LocalizedError {
        case userNotFound
        case swiftDataError(Error)
        case networkError(Error)
        case partialFailure([String])

        var errorDescription: String? {
            switch self {
            case .userNotFound:
                return "No signed-in Firebase user was found."
            case .swiftDataError(let error):
                return "Database error: \(error.localizedDescription)"
            case .networkError(let error):
                return "Network error: \(error.localizedDescription)"
            case .partialFailure(let failures):
                return "Some reset steps failed:\n" + failures.joined(separator: "\n")
            }
        }
    }

    private enum Constants {
        static let pendingCleanupKey = "account.reset.pendingRemoteCleanup"
    }

    private let authService: AuthenticationService
    private let swiftData: SwiftDataStorageService
    private let favoritesStore: FavoritesStore
    private let planStore: PlanStore
    private let historyService: HistoryStorageService
    private let diskManager: DiskStorageManager
    private let userProfileService: UserProfileService
    private let networkService: OptimizedNetworkService
    private let firestore: Firestore

    // Observe network reconnection via selector-based API to avoid non-Sendable token in deinit

    private init(
        authService: AuthenticationService = ServiceContainer.shared.authenticationService,
        swiftData: SwiftDataStorageService = .shared,
        favoritesStore: FavoritesStore = .shared,
        planStore: PlanStore = .shared,
        historyService: HistoryStorageService = .shared,
        diskManager: DiskStorageManager = .shared,
        userProfileService: UserProfileService = .shared,
        networkService: OptimizedNetworkService = .shared,
        firestore: Firestore = Firestore.firestore()
    ) {
        self.authService = authService
        self.swiftData = swiftData
        self.favoritesStore = favoritesStore
        self.planStore = planStore
        self.historyService = historyService
        self.diskManager = diskManager
        self.userProfileService = userProfileService
        self.networkService = networkService
        self.firestore = firestore

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNetworkReconnectedNotification(_:)),
            name: .networkReconnected,
            object: nil
        )

        Task { [weak self] in
            await self?.processPendingRemoteCleanupIfNeeded()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func handleNetworkReconnectedNotification(_ notification: Notification) {
        Task { [weak self] in
            await self?.processPendingRemoteCleanupIfNeeded()
        }
    }


    func resetAccount() async throws {
        guard !isResetting else { return }
        guard let user = Auth.auth().currentUser else {
            let error = ResetError.userNotFound
            resetError = error
            throw error
        }

        isResetting = true
        resetError = nil
        resetProgress = .idle

        var failures: [String] = []
        var restoredPreferences: UserPreferences?

        defer { isResetting = false }

        for stage in ResetStage.allCases {
            resetProgress = .stage(stage)
            do {
                switch stage {
                case .clearingSwiftData:
                    try await clearSwiftData()
                case .clearingFavorites:
                    clearFavorites()
                case .clearingPlans:
                    clearPlans()
                case .clearingHistory:
                    clearHistory()
                case .clearingCache:
                    clearCaches()
                case .clearingUserDefaults:
                    clearUserDefaults()
                case .restoringDefaults:
                    restoredPreferences = try await restoreDefaultPreferences(for: user)
                case .syncingToCloud:
                    try await syncToCloud(for: user, preferences: restoredPreferences)
                }
            } catch {
                failures.append("\(stage.failureLabel): \(error.localizedDescription)")
                print("❌ Account reset step failed [\(stage.failureLabel)]: \(error)")
            }
        }

        if failures.isEmpty {
            resetProgress = .completed
            return
        }

        let error = ResetError.partialFailure(failures)
        resetError = error
        resetProgress = .idle
        throw error
    }
}

// MARK: - Step Implementations

private extension AccountResetManager {
    func clearSwiftData() async throws {
        do {
            try await swiftData.clearAllUserData()
        } catch {
            throw ResetError.swiftDataError(error)
        }
    }

    func clearFavorites() {
        favoritesStore.clearAll()
    }

    func clearPlans() {
        planStore.clearAll()
    }

    func clearHistory() {
        historyService.clearAllQuick()
        NotificationCenter.default.post(name: .quickHistoryDidChange, object: nil)
    }

    func clearCaches() {
        diskManager.purgeAllUserDataFolders()
    }

    func clearUserDefaults() {
        let defaults = UserDefaults.standard
        defaults.removeObject(forKey: "favorites.recipes.v1")
        defaults.removeObject(forKey: "favorites.snapshots.v1")
        defaults.removeObject(forKey: "planstore.lastQuick.v1")
        defaults.removeObject(forKey: "planstore.lastMealPrep.v1")
        defaults.removeObject(forKey: "planstore.retentionNotice.v1")
        defaults.removeObject(forKey: StorageConfiguration.quickHistoryIndexKey)
        defaults.removeObject(forKey: StorageConfiguration.legacyQuickHistoryKey)
        defaults.removeObject(forKey: "remote_config_cache_v1")
        defaults.removeObject(forKey: "remote_config_timestamp_v1")
        defaults.removeObject(forKey: "user_preferences_v2")
        defaults.removeObject(forKey: "expirationSuppressNoonDate")
        defaults.removeObject(forKey: Constants.pendingCleanupKey)
        defaults.removeObject(forKey: "has_cleared_pantry_on_first_launch_v1")
        defaults.set(false, forKey: "telemetry_opt_in")
        defaults.set(false, forKey: "foodExpirationReminderEnabled")
    }

    func restoreDefaultPreferences(for user: User) async throws -> UserPreferences {
        let preferences = UserPreferences.createDefault(for: user.uid)
        await authService.updatePreferences(preferences)
        do {
            try await swiftData.saveUserPreferences(preferences)
        } catch {
            throw ResetError.swiftDataError(error)
        }
        return preferences
    }

    func syncToCloud(for user: User, preferences: UserPreferences?) async throws {
        if let preferences {
            do {
                try await userProfileService.savePreferences(preferences, for: user.uid)
            } catch {
                throw ResetError.networkError(error)
            }
        }

        do {
            try await cleanupRemoteData(for: user)
        } catch let resetError as ResetError {
            throw resetError
        } catch {
            throw ResetError.networkError(error)
        }

        if networkService.isOnline {
            do {
                try await authService.reloadUser()
            } catch {
                print("⚠️ Failed to reload Firebase user after reset: \(error)")
            }
        }
    }

    func cleanupRemoteData(for user: User) async throws {
        if !networkService.isOnline {
            scheduleRemoteCleanup(for: user.uid)
            print("⚠️ Remote cleanup deferred until network is available")
            return
        }

        do {
            let userDocument = firestore.collection("users").document(user.uid)
            try await deleteCollection(userDocument.collection("quickHistory"), label: "quickHistory")
            try await deleteCollection(userDocument.collection("favorites"), label: "favorites")
            removePendingCleanup(for: user.uid)
            print("✅ Remote account data cleared for user \(user.uid)")
        } catch {
            scheduleRemoteCleanup(for: user.uid)
            throw ResetError.networkError(error)
        }
    }

    func deleteCollection(_ collection: CollectionReference, label: String, batchSize: Int = 50) async throws {
        var shouldContinue = true
        while shouldContinue {
            let snapshot = try await collection.limit(to: batchSize).getDocuments()
            guard !snapshot.documents.isEmpty else { break }

            let batch = firestore.batch()
            for document in snapshot.documents {
                batch.deleteDocument(document.reference)
            }
            try await batch.commit()

            shouldContinue = snapshot.documents.count >= batchSize
        }
        print("🧹 Cleared Firestore collection: \(label)")
    }

    func scheduleRemoteCleanup(for userId: String) {
        var pending = UserDefaults.standard.stringArray(forKey: Constants.pendingCleanupKey) ?? []
        if !pending.contains(userId) {
            pending.append(userId)
            UserDefaults.standard.set(pending, forKey: Constants.pendingCleanupKey)
        }
    }

    func removePendingCleanup(for userId: String) {
        var pending = UserDefaults.standard.stringArray(forKey: Constants.pendingCleanupKey) ?? []
        if let index = pending.firstIndex(of: userId) {
            pending.remove(at: index)
            UserDefaults.standard.set(pending, forKey: Constants.pendingCleanupKey)
        }
    }

    func processPendingRemoteCleanupIfNeeded() async {
        guard let currentUser = Auth.auth().currentUser else { return }

        let pending = UserDefaults.standard.stringArray(forKey: Constants.pendingCleanupKey) ?? []
        guard pending.contains(currentUser.uid) else { return }

        do {
            try await cleanupRemoteData(for: currentUser)
        } catch {
            print("⚠️ Pending remote cleanup failed: \(error)")
        }
    }


}
