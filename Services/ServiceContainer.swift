import Foundation
import SwiftUI

@Observable
@MainActor
class ServiceContainer {
    static let shared = ServiceContainer()

    // Core services
    let pantryService: PantryService
    let authenticationService: AuthenticationService
    let swiftDataStorage: SwiftDataStorageService
    let concurrencyManager: ConcurrencyManager

    // API services (actors)
    let googleVisionService: GoogleVisionAPIService
    let geminiService: GeminiAPIService
    let recipeGenerationService: RecipeGenerationServiceProtocol

    // Image preparation (actor)
    let imagePreparationService: AsyncImagePreparationService

    // Telemetry service (opt-in)
    let telemetryService: TelemetryService

    private init() {
        let authService = AuthenticationService()
        authenticationService = authService

        pantryService = PantryService()
        swiftDataStorage = SwiftDataStorageService.shared
        concurrencyManager = ConcurrencyManager.shared
        googleVisionService = GoogleVisionAPIService()
        geminiService = GeminiAPIService()
        recipeGenerationService = RecipeGenerationService()
        imagePreparationService = AsyncImagePreparationService()
        telemetryService = TelemetryService()
    }

}


// MARK: - App Config (Feature Flags)
enum ProcessingFlowMode { case automated }

@MainActor
struct AppConfig {
    static var processingFlowMode: ProcessingFlowMode = .automated
}
