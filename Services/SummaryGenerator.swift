import Foundation
import Combine

/// Generates human-readable configuration summaries for the UI (P1 4.2)
@MainActor
final class SummaryGenerator {
    static let shared = SummaryGenerator()
    private init() {}

    func summaryText(days: Int, meals: Set<MealType>) -> String {
        let dayPartFormat = NSLocalizedString("summary_day_part_format", comment: "e.g., for 3 days")
        let dayPluralSuffix = days == 1 ? "" : "s"
        let dayPart = String(format: dayPartFormat, days, dayPluralSuffix)
        let mealNames = meals.sorted { $0.rawValue < $1.rawValue }.map { $0.displayName }
        let mealPart: String
        if mealNames.isEmpty {
            mealPart = NSLocalizedString("summary_meals_none", comment: "prompt to select meals")
        } else {
            let joiner = NSLocalizedString("summary_meals_joiner", comment: "joiner between meal names")
            mealPart = mealNames.joined(separator: joiner)
        }
        let fullFormat = NSLocalizedString("summary_full_format", comment: "full summary format")
        return String(format: fullFormat, dayPart, mealPart)
    }
}

