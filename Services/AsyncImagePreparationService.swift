import Foundation
import UIKit

/// Service for preparing images asynchronously in background threads
/// Optimized for Vision API processing with improved compression and sizing
actor AsyncImagePreparationService {

    // MARK: - Configuration

    private let maxDimension: CGFloat = 1280  // Reduced from 2000px for better performance
    private let compressionQuality: CGFloat = 0.7  // Reduced from 0.8 for smaller payloads

    // MARK: - Public Methods

    /// Prepare multiple images concurrently in background threads
    /// - Parameter images: Array of UIImages to process
    /// - Returns: Array of per-image results preserving input order
    ///             Successes contain PreparedImage; failures carry indexed PreparationError.
    func prepareImagesForVisionAPI(_ images: [UIImage]) async -> [Result<PreparedImage, PreparationError>] {
        guard !images.isEmpty else { return [] }

        return await withTaskGroup(of: (Int, Result<PreparedImage, PreparationError>).self) { group in
            var results: [Result<PreparedImage, PreparationError>?] = Array(repeating: nil, count: images.count)

            for (index, image) in images.enumerated() {
                group.addTask {
                    let result = await self.prepareImage(image, index: index)
                    return (index, result)
                }
            }

            for await (index, result) in group {
                results[index] = result
            }

            return results.compactMap { $0 }
        }
    }

    // MARK: - Private Methods

    /// Prepare a single image in background thread
    private func prepareImage(_ image: UIImage, index: Int) async -> Result<PreparedImage, PreparationError> {
        // Move CPU-intensive work to background thread
        return await Task.detached { [compressionQuality] in
            let originalSize = image.size

            // Skip resize entirely; compress original to JPEG at 0.7 like older app
            guard let imageData = image.jpegData(compressionQuality: compressionQuality) else {
                return .failure(.compressionFailed(index: index))
            }
            let processedSize = originalSize

            // Convert to base64 for API
            let base64String = imageData.base64EncodedString()

            let prepared = PreparedImage(
                originalIndex: index,
                base64String: base64String,
                originalSize: originalSize,
                processedSize: processedSize
            )
            return .success(prepared)
        }.value
    }

    /// Resize image if its longest dimension exceeds maxDimension
    /// Uses the same logic as GoogleVisionAPIService but with configurable max dimension
    private static func resizeImage(_ image: UIImage, maxDimension: CGFloat) -> UIImage {
        let width = image.size.width
        let height = image.size.height

        // Check if resizing is needed
        if width <= maxDimension && height <= maxDimension {
            return image
        }

        // Calculate new dimensions while maintaining aspect ratio
        let aspectRatio = width / height
        var newWidth: CGFloat
        var newHeight: CGFloat

        if width > height {
            newWidth = maxDimension
            newHeight = newWidth / aspectRatio
        } else {
            newHeight = maxDimension
            newWidth = newHeight * aspectRatio
        }

        // Resize image using UIGraphicsImageRenderer (iOS 10+), preferred for iOS 17+
        let targetSize = CGSize(width: newWidth, height: newHeight)
        let format = UIGraphicsImageRendererFormat.default()
        format.scale = 1.0
        format.opaque = false
        let renderer = UIGraphicsImageRenderer(size: targetSize, format: format)
        let rendered = renderer.image { ctx in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
        return rendered
    }
}

// MARK: - Preparation Error Type (indexed)

enum PreparationError: Error, LocalizedError, Sendable {
    case compressionFailed(index: Int)
    case resizeFailed(index: Int)
    case invalidImage(index: Int)
    case unknown(index: Int)

    var errorDescription: String? {
        switch self {
        case .compressionFailed(let i): return "Compression failed for image #\(i+1)"
        case .resizeFailed(let i): return "Resize failed for image #\(i+1)"
        case .invalidImage(let i): return "Invalid image #\(i+1)"
        case .unknown(let i): return "Unknown error for image #\(i+1)"
        }
    }
}

