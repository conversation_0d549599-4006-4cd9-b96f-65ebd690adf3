import Foundation
import UIKit
import os.log

@MainActor
class ConcurrencyManager {
    static let shared = ConcurrencyManager()
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "IngredientScanner", category: "Concurrency")
    
    private init() {}
    
    // MARK: - Batch Image Processing
    
    func processBatchImages(
        _ images: [UIImage],
        using visionService: GoogleVisionAPIService,
        progressCallback: @escaping @MainActor (Int, Int) -> Void = { _, _ in }
    ) async throws -> [String] {
        logger.info("Starting batch processing of \(images.count) images")

        // Use true batching path: prepare once, single API call
        let prepResults = await ServiceContainer.shared.imagePreparationService.prepareImagesForVisionAPI(images)
        let prepared = prepResults.compactMap { try? $0.get() }
        let outputs = try await visionService.batchAnalyze(preparedImages: prepared)

        // Update progress to complete
        progressCallback(images.count, images.count)

        // Map outputs to combined content-compatible strings
        return outputs.map { out in
            if out.labels.isEmpty { return out.ocrText }
            return out.ocrText + "\n\nDetected Items: " + out.labels.joined(separator: ", ")
        }
    }
    
    // MARK: - Simple Async Operations
    
    func processImagePair(
        image1: UIImage,
        image2: UIImage,
        using visionService: GoogleVisionAPIService,
        progressCallback: @escaping @MainActor (Int, Int) -> Void
    ) async throws -> [String] {
        logger.info("Processing image pair")
        
        // Update progress (no await needed since we're already on MainActor)
        progressCallback(1, 2)
        
        let prepResults = await ServiceContainer.shared.imagePreparationService.prepareImagesForVisionAPI([image1, image2])
        let prepared = prepResults.compactMap { try? $0.get() }
        let outputs = try await visionService.batchAnalyze(preparedImages: prepared)

        progressCallback(2, 2)

        return outputs.map { out in
            if out.labels.isEmpty { return out.ocrText }
            return out.ocrText + "\n\nDetected Items: " + out.labels.joined(separator: ", ")
        }
    }
}

// MARK: - Supporting Types

private struct IndexedResult<T: Sendable>: Sendable {
    let index: Int
    let value: T
} 