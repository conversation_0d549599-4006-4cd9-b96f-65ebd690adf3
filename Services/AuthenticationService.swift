import Foundation
@preconcurrency import <PERSON><PERSON><PERSON>uth
@preconcurrency import <PERSON>base<PERSON>ore
@preconcurrency import FirebaseFirestore
@preconcurrency import GoogleSignIn
import AuthenticationServices
import UIKit
import SwiftUI
import CryptoKit
@preconcurrency import ObjectiveC

/// Firebase-powered authentication service for ingredient scanner app
///
/// Features:
/// - Firebase authentication (Apple, Google, Email)
/// - Anonymous usage support (full app functionality)
/// - Local preferences storage with UserDefaults
/// - Cloud sync with Firestore for authenticated users
@Observable
@MainActor
class AuthenticationService {

    // MARK: - Authentication State Enum
    
    enum AuthState {
        case initializing
        case authenticated
        case unauthenticated
    }

    enum SecureOperation: Equatable {
        case updateProfile
        case updateEmail
        case updatePassword
        case deleteAccount
        case reloadUser
        case sendVerificationEmail
        case sendPasswordReset
        case custom(String)

        var label: String {
            switch self {
            case .updateProfile: return "update_profile"
            case .updateEmail: return "update_email"
            case .updatePassword: return "update_password"
            case .deleteAccount: return "delete_account"
            case .reloadUser: return "reload_user"
            case .sendVerificationEmail: return "send_verification_email"
            case .sendPasswordReset: return "send_password_reset"
            case .custom(let value): return value
            }
        }
    }

    // MARK: - Authentication Error Enum

    enum AuthError: Error, Identifiable {
        case invalidEmail
        case weakPassword
        case emailAlreadyInUse
        case userNotFound
        case wrongPassword
        case networkError
        case credentialAlreadyInUse
        case invalidCredential
        case operationNotAllowed
        case tooManyRequests
        case requiresRecentLogin
        case userDisabled
        case userTokenExpired
        case userCancelled
        case appleSignInFailed
        case googleSignInFailed
        case unknown(message: String)

        var id: String { localizedDescription }
        
        var localizedDescription: String {
            switch self {
            case .invalidEmail: 
                return "The email address is badly formatted."
            case .weakPassword: 
                return "Password should be at least 6 characters."
            case .emailAlreadyInUse: 
                return "The email address is already in use by another account."
            case .userNotFound: 
                return "No user found with this email address."
            case .wrongPassword: 
                return "The password is invalid."
            case .networkError: 
                return "Network error. Please check your connection."
            case .credentialAlreadyInUse: 
                return "This credential is already associated with a different user account."
            case .invalidCredential: 
                return "The credential is malformed or has expired."
            case .operationNotAllowed: 
                return "This operation is not allowed."
            case .tooManyRequests: 
                return "Too many requests. Please try again later."
            case .requiresRecentLogin:
                return "Please reauthenticate to continue."
            case .userDisabled:
                return "This account has been disabled. Contact support for help."
            case .userTokenExpired:
                return "Your session has expired. Please sign in again."
            case .userCancelled:
                return "" // Empty string for user cancellation - no error shown
            case .appleSignInFailed:
                return "Apple Sign-In failed. Please try again."
            case .googleSignInFailed:
                return "Google Sign-In failed. Please try again."
            case .unknown(let message): 
                return message
            }
        }
        
        static func from(_ error: Error) -> AuthError {
            // Handle Firebase Auth errors
            let nsError = error as NSError
            if let authErrorCode = AuthErrorCode(_bridgedNSError: nsError) {
                let errorCode = authErrorCode.code
                
                switch errorCode {
                case .invalidEmail: 
                    return .invalidEmail
                case .weakPassword: 
                    return .weakPassword
                case .emailAlreadyInUse: 
                    return .emailAlreadyInUse
                case .userNotFound: 
                    return .userNotFound
                case .wrongPassword: 
                    return .wrongPassword
                case .networkError: 
                    return .networkError
                case .credentialAlreadyInUse: 
                    return .credentialAlreadyInUse
                case .invalidCredential: 
                    return .invalidCredential
                case .operationNotAllowed: 
                    return .operationNotAllowed
                case .tooManyRequests: 
                    return .tooManyRequests
                case .requiresRecentLogin:
                    return .requiresRecentLogin
                case .userDisabled:
                    return .userDisabled
                case .userTokenExpired:
                    return .userTokenExpired
                default: 
                    return .unknown(message: nsError.localizedDescription)
                }
            }
            
            // Handle Apple Sign-In errors
            if let appleError = error as? ASAuthorizationError {
                switch appleError.code {
                case .canceled:
                    return .userCancelled
                case .failed, .invalidResponse, .notHandled, .unknown, .notInteractive, 
                     .matchedExcludedCredential, .credentialImport, .credentialExport:
                    return .appleSignInFailed
                @unknown default:
                    return .appleSignInFailed
                }
            }
            
            // Handle Google Sign-In errors
            if let gidError = error as? GIDSignInError {
                switch gidError.code {
                case .canceled:
                    return .userCancelled
                default:
                    return .googleSignInFailed
                }
            }
            
            // Fallback to generic error message
            return .unknown(message: error.localizedDescription)
        }
    }

    // MARK: - Published Properties

    var authState: AuthState = .initializing
    var isAuthenticated = false
    var currentUser: User?
    var isLoading = false
    var authError: String?
    var lastAuthError: AuthError?
    @ObservationIgnored var lastFirebaseNSError: NSError?
    @ObservationIgnored private(set) var lastOperationRequiredReauth = false
    @ObservationIgnored private(set) var reauthAttemptCount = 0

    typealias ReauthenticationHandler = (SecureOperation) async throws -> Void
    @ObservationIgnored var reauthenticationHandler: ReauthenticationHandler?

    /// User preferences (local-first, cloud sync when authenticated)
    var userPreferences: UserPreferences?

    // Loading state for preferences (Task 20)
    enum LoadingState {
        case idle
        case loading
        case success
        case error(FirestoreError)
    }
    var preferencesLoadingState: LoadingState = .idle
    
    // MARK: - Authentication Provider Properties
    
    /// Icon name for the current authentication provider
    var authenticationProviderIcon: String {
        guard let user = currentUser else { return "person.circle" }
        
        // Check provider data
        if let providerData = user.providerData.first {
            switch providerData.providerID {
            case "apple.com":
                return "applelogo"
            case "google.com":
                return "globe"
            case "password":
                return "envelope"
            default:
                return "person.circle"
            }
        }
        
        return user.isAnonymous ? "person.circle.dashed" : "person.circle"
    }
    
    /// Display name for the current authentication provider
    var authenticationProviderDisplayName: String {
        guard let user = currentUser else { return "Not signed in" }
        
        // Check provider data
        if let providerData = user.providerData.first {
            switch providerData.providerID {
            case "apple.com":
                return "Apple"
            case "google.com":
                return "Google"
            case "password":
                return "Email"
            default:
                return "Unknown"
            }
        }
        
        return user.isAnonymous ? "Anonymous" : "Unknown"
    }
    
    /// Color for the current authentication provider
    var authenticationProviderColor: Color {
        guard let user = currentUser else { return .gray }
        
        // Check provider data
        if let providerData = user.providerData.first {
            switch providerData.providerID {
            case "apple.com":
                return .black
            case "google.com":
                return .blue
            case "password":
                return .green
            default:
                return .gray
            }
        }
        
        return user.isAnonymous ? .orange : .gray
    }

    // MARK: - Private Properties

    private let userDefaultsKey = "UserPreferences"
    @ObservationIgnored private nonisolated(unsafe) var authStateListener: AuthStateDidChangeListenerHandle?
    @ObservationIgnored private var _db: Firestore?
    private var db: Firestore {
        if let _db = _db {
            return _db
        }
        let firestore = Firestore.firestore()
        _db = firestore
        return firestore
    }
    
    // Property to store the current nonce for Apple Sign In
    private var currentNonce: String?

    // MARK: - Initialization

    init() {
        registerAuthStateHandler()
        // Load initial preferences
        loadLocalPreferences()
    }
    
    deinit {
        // Note: Must be synchronous and nonisolated for deinit
        if let authStateListener = authStateListener {
            Auth.auth().removeStateDidChangeListener(authStateListener)
            print("🔐 Auth state listener removed in deinit")
        }
    }
    
    // MARK: - Error Handling
    
    /// Handle authentication errors consistently
    func handleAuthError(_ error: Error) {
        if let nsError = error as NSError?, !(error is AuthError) {
            lastFirebaseNSError = nsError
            print("⚠️ Firebase NSError captured: domain=\(nsError.domain) code=\(nsError.code) userInfo=\(nsError.userInfo)")
        }

        // If it's already our domain error, use it directly to avoid losing context
        if let domainError = error as? AuthError {
            lastAuthError = domainError
            if case .userCancelled = domainError {
                authError = nil
            } else {
                authError = domainError.localizedDescription
            }
            print("🚨 Auth Error: \(domainError)")
            return
        }
        
        let authErr = AuthError.from(error)
        lastAuthError = authErr
        
        // Update string property for backward compatibility
        // Don't show error message for user cancellation
        if case .userCancelled = authErr {
            authError = nil
        } else {
            authError = authErr.localizedDescription
        }
        
        print("🚨 Auth Error: \(authErr)")
    }

    private func prepareSecureOperationContext() {
        lastOperationRequiredReauth = false
        reauthAttemptCount = 0
    }

    func recordReauthAttempt() {
        reauthAttemptCount += 1
    }

    func consumeReauthContext() -> (required: Bool, attempts: Int) {
        let context = (lastOperationRequiredReauth, reauthAttemptCount)
        lastOperationRequiredReauth = false
        reauthAttemptCount = 0
        return context
    }
    
    /// Clear authentication errors
    private func clearAuthError() {
        authError = nil
        lastAuthError = nil
    }

    // MARK: - Secure Operation Helpers

    private func refreshCurrentUserCache() {
        currentUser = Auth.auth().currentUser
        isAuthenticated = currentUser != nil
        authState = currentUser == nil ? .unauthenticated : .authenticated
    }

    private func requireCurrentUser() throws -> User {
        guard let user = Auth.auth().currentUser else {
            throw AuthError.userNotFound
        }
        return user
    }

    private func logFirebaseNSError(_ error: NSError, context: SecureOperation) {
        lastFirebaseNSError = error
        print("⚠️ Firebase NSError during \(context.label): domain=\(error.domain) code=\(error.code) userInfo=\(error.userInfo)")
    }

    private func requestReauthentication(for operation: SecureOperation) async throws {
        guard let reauthenticationHandler else {
            throw AuthError.requiresRecentLogin
        }
        try await reauthenticationHandler(operation)
    }

    @discardableResult
    private func performSecureOperation<T: Sendable>(
        _ operation: SecureOperation,
        reauthenticate: (() async throws -> Void)? = nil,
        action: @MainActor @Sendable () async throws -> T
    ) async throws -> T {
        do {
            return try await action()
        } catch {
            if !(error is AuthError) {
                let nsError = error as NSError
                logFirebaseNSError(nsError, context: operation)
            }
            let mappedError = AuthError.from(error)

            if case .requiresRecentLogin = mappedError {
                lastOperationRequiredReauth = true
                reauthAttemptCount = 0
                let reauthAction = reauthenticate ?? { [weak self] in
                    guard let self else { throw AuthError.requiresRecentLogin }
                    try await self.requestReauthentication(for: operation)
                }

                do {
                    try await reauthAction()
                    print("🔄 Retrying secure operation: \(operation.label)")
                    return try await action()
                } catch {
                    if !(error is AuthError) {
                        let retryNSError = error as NSError
                        logFirebaseNSError(retryNSError, context: operation)
                    }
                    let retryError = AuthError.from(error)
                    throw retryError
                }
            }

            throw mappedError
        }
    }
    // MARK: - Auth State Management
    
    private func registerAuthStateHandler() {
        if authStateListener == nil {
            authStateListener = Auth.auth().addStateDidChangeListener { [weak self] _, user in
                Task { @MainActor in
                    guard let self = self else { return }
                    
                    self.currentUser = user
                    
                    // Update both the enum state and boolean for backward compatibility
                    if let user = user {
                        self.authState = .authenticated
                        self.isAuthenticated = true
                        // Fetch or create user preferences via service (Task 20)
                        self.loadUserPreferences(for: user.uid)
                        // Also load from cloud (authoritative) after auth state changes
                        Task { await self.syncPreferencesFromCloud() }
                    } else {
                        self.authState = .unauthenticated
                        self.isAuthenticated = false
                        // User signed out - clear preferences
                        self.userPreferences = nil
                        self.preferencesLoadingState = .idle
                    }
                    
                    print("🔐 Auth state changed: \(self.authState), User: \(user?.uid ?? "nil")")
                }
            }
        }
    }
    
    private func removeAuthStateHandler() {
        if let authStateListener = authStateListener {
            Auth.auth().removeStateDidChangeListener(authStateListener)
            self.authStateListener = nil
            print("🔐 Auth state listener removed")
        }
    }

    // MARK: - Authentication Methods

    func startSignInWithApple() -> ASAuthorizationAppleIDRequest {
        print("🍎 Preparing Apple Sign-In request")
        
        let nonce = randomNonceString()
        currentNonce = nonce
        print("🍎 Generated nonce: \(nonce.prefix(8))...")
        
        let appleIDProvider = ASAuthorizationAppleIDProvider()
        let request = appleIDProvider.createRequest()
        request.requestedScopes = [.fullName, .email]
        request.nonce = sha256(nonce)
        
        print("🍎 Apple Sign-In request prepared with scopes: \(request.requestedScopes?.map { $0.rawValue } ?? [])")
        
        return request
    }

    func handleSignInWithApple(authorization: ASAuthorization) async throws -> User {
        print("🍎 Starting Apple Sign-In process")
        
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            print("❌ Failed to get Apple ID credential")
            throw AuthError.invalidCredential
        }
        
        guard let nonce = currentNonce else {
            print("❌ Invalid state: A login callback was received, but no login request was sent")
            throw AuthError.invalidCredential
        }
        
        guard let appleIDToken = appleIDCredential.identityToken else {
            print("❌ Unable to fetch identity token")
            throw AuthError.invalidCredential
        }
        
        guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
            print("❌ Unable to serialize token string from data: \(appleIDToken.debugDescription)")
            throw AuthError.invalidCredential
        }
        
        print("🍎 Successfully obtained Apple ID token and nonce")
        
        // Create OAuth credential for Firebase using latest API (avoids deprecation)
        let credential = OAuthProvider.credential(
            providerID: AuthProviderID.apple,
            idToken: idTokenString,
            rawNonce: nonce,
            accessToken: nil
        )
        
        print("🍎 Created Firebase credential, attempting sign-in")
        
        do {
            let authResult = try await Auth.auth().signIn(with: credential)
            print("🍎 Firebase sign-in successful for user: \(authResult.user.uid)")
            
            // Handle user profile information for new users (best-effort only)
            if let fullName = appleIDCredential.fullName,
               let givenName = fullName.givenName,
               let familyName = fullName.familyName,
               !givenName.isEmpty && !familyName.isEmpty {
                
                print("🍎 Updating user profile with name: \(givenName) \(familyName)")
                let changeRequest = authResult.user.createProfileChangeRequest()
                changeRequest.displayName = "\(givenName) \(familyName)"
                do {
                    try await changeRequest.commitChanges()
                    print("🍎 Successfully updated user profile")
                } catch {
                    print("⚠️ Failed to update user profile: \(error.localizedDescription)")
                    // Non-fatal; sign-in already succeeded
                }
            }
            
            // Post-sign-in preferences sync (best-effort; do not fail sign-in on Firestore permission issues)
            do {
                let preferences = try await fetchOrCreateUserProfile(for: authResult.user)
                await MainActor.run {
                    self.userPreferences = preferences
                    self.saveLocalPreferences()
                }
            } catch {
                print("⚠️ Preferences sync after Apple sign-in failed (non-fatal): \(error.localizedDescription)")
            }
            
            return authResult.user
            
        } catch {
            print("❌ Firebase sign-in failed: \(error.localizedDescription)")
            if let authError = error as? AuthErrorCode {
                print("❌ Auth error code: \(authError.rawValue)")
            }
            throw AuthError.from(error)
        }
    }


    /// Sign in with Google using GoogleSignIn SDK and Firebase Auth
    /// Follows Firebase official documentation and task_007.txt requirements
    /// Note: Google Sign-In configuration is set in App.swift during app initialization
    func signInWithGoogle(presenting viewController: UIViewController? = nil) async throws -> User {
        print("🔍 Starting Google Sign-In")
        isLoading = true
        clearAuthError()
        
        defer {
            isLoading = false
        }
        
        // Verify Google Sign-In is configured (configuration set in App.swift)
        guard GIDSignIn.sharedInstance.configuration != nil else {
            print("❌ Google Sign-In not configured")
            throw AuthError.googleSignInFailed
        }
        
        // Get presenting view controller (use provided or auto-detect)
        let presentingViewController: UIViewController
        if let viewController = viewController {
            presentingViewController = viewController
        } else {
            // Find the top-most view controller
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first,
                  let rootViewController = window.rootViewController else {
                print("❌ Could not find root view controller for Google Sign-In")
                throw AuthError.googleSignInFailed
            }
            
            // Get the topmost presented view controller
            var topController = rootViewController
            while let presented = topController.presentedViewController {
                topController = presented
            }
            presentingViewController = topController
            print("🔍 Found presenting view controller: \(type(of: presentingViewController))")
        }
        
        do {
            print("🔍 Starting Google Sign-In flow with presenter: \(type(of: presentingViewController))")
            
            // Start the sign in flow
            let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: presentingViewController)
            print("🔍 Google Sign-In result received")
            
            guard let idToken = result.user.idToken?.tokenString else {
                print("❌ Failed to get Google ID token")
                throw AuthError.invalidCredential
            }
            print("🔍 Google ID token obtained")
            
            // Create a Firebase credential with the Google ID token
            let credential = GoogleAuthProvider.credential(withIDToken: idToken,
                                                         accessToken: result.user.accessToken.tokenString)
            print("🔍 Firebase credential created from Google tokens")
            
            // Sign in with Firebase using the Google credential
            let authResult = try await Auth.auth().signIn(with: credential)
            print("✅ Google Sign-In completed for user: \(authResult.user.uid)")
            
            // Best-effort preferences sync; do not fail sign-in on Firestore issues
            do {
                let preferences = try await fetchOrCreateUserProfile(for: authResult.user)
                await MainActor.run {
                    self.userPreferences = preferences
                    self.saveLocalPreferences()
                }
            } catch {
                print("⚠️ Preferences sync after Google sign-in failed (non-fatal): \(error.localizedDescription)")
            }
            
            return authResult.user
        } catch {
            print("❌ Google Sign-In failed with error: \(error)")
            if let gidError = error as? GIDSignInError {
                print("❌ GIDSignInError code: \(gidError.code.rawValue)")
                print("❌ GIDSignInError description: \(gidError.localizedDescription)")
            }
            if let nsError = error as NSError? {
                print("❌ NSError domain: \(nsError.domain)")
                print("❌ NSError code: \(nsError.code)")
                print("❌ NSError userInfo: \(nsError.userInfo)")
            }
            handleAuthError(error)
            throw AuthError.from(error)
        }
    }

    /// Sign in with email and password using Firebase Auth
    func signInWithEmail(_ email: String, password: String) async throws -> User {
        print("📧 Starting Email Sign-In for: \(email)")
        isLoading = true
        clearAuthError()
        
        // Ensure loading is always reset
        defer {
            isLoading = false
        }

        do {
            // Validate email format
            guard isValidEmail(email) else {
                print("❌ Invalid email format: \(email)")
                throw AuthError.invalidEmail
            }
            print("📧 Email format validated, attempting Firebase sign-in")
            
            let authResult = try await Auth.auth().signIn(withEmail: email, password: password)
            print("✅ Email Sign-In completed for user: \(authResult.user.uid)")
            
            // Best-effort preferences sync; do not fail sign-in on Firestore issues
            do {
                let preferences = try await fetchOrCreateUserProfile(for: authResult.user)
                await MainActor.run {
                    self.userPreferences = preferences
                    self.saveLocalPreferences()
                }
            } catch {
                print("⚠️ Preferences sync after Email sign-in failed (non-fatal): \(error.localizedDescription)")
            }
            
            return authResult.user
        } catch {
            print("❌ Email Sign-In failed with error: \(error)")
            if let authError = error as NSError? {
                print("❌ NSError domain: \(authError.domain)")
                print("❌ NSError code: \(authError.code)")
                print("❌ NSError description: \(authError.localizedDescription)")
            }
            handleAuthError(error)
            throw AuthError.from(error)
        }
    }

    /// Create account with email and password using Firebase Auth
    func createAccount(email: String, password: String) async throws -> User {
        print("📝 Creating account for: \(email)")
        isLoading = true
        clearAuthError()
        
        // Ensure loading is always reset
        defer {
            isLoading = false
        }

        do {
            // Validate email format
            guard isValidEmail(email) else {
                print("❌ Invalid email format: \(email)")
                throw AuthError.invalidEmail
            }
            
            // Validate password strength
            guard isValidPassword(password) else {
                print("❌ Password too weak (length: \(password.count))")
                throw AuthError.weakPassword
            }
            
            print("📝 Validation passed, attempting to create Firebase account")
            let authResult = try await Auth.auth().createUser(withEmail: email, password: password)
            print("✅ Account created for user: \(authResult.user.uid)")
            
            // Best-effort preferences initialization; do not fail account creation on Firestore issues
            do {
                let preferences = try await fetchOrCreateUserProfile(for: authResult.user)
                await MainActor.run {
                    self.userPreferences = preferences
                    self.saveLocalPreferences()
                }
            } catch {
                print("⚠️ Preferences initialization after account creation failed (non-fatal): \(error.localizedDescription)")
            }
            
            return authResult.user
        } catch {
            print("❌ Account creation failed with error: \(error)")
            if let authError = error as NSError? {
                print("❌ NSError domain: \(authError.domain)")
                print("❌ NSError code: \(authError.code)")
                print("❌ NSError description: \(authError.localizedDescription)")
            }
            handleAuthError(error)
            throw AuthError.from(error)
        }
    }
    
    /// Send password reset email, keeping Firebase Auth language configuration consistent
    func sendPasswordReset(email: String) async throws {
        print("🔄 Sending password reset for: \(email)")
        isLoading = true
        clearAuthError()
        prepareSecureOperationContext()

        defer { isLoading = false }

        guard isValidEmail(email) else {
            throw AuthError.invalidEmail
        }

        do {
            Auth.auth().useAppLanguage()
            try await performSecureOperation(.sendPasswordReset) {
                try await Auth.auth().sendPasswordReset(withEmail: email)
            }
            print("✅ Password reset email sent to: \(email)")
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    /// Reset password using Firebase Auth
    func resetPassword(email: String) async throws {
        try await sendPasswordReset(email: email)
    }

    // MARK: - Firebase User Management

    func updateUserProfile(displayName: String?, photoURL: URL?) async throws {
        clearAuthError()
        prepareSecureOperationContext()

        do {
            let user = try requireCurrentUser()
            try await performSecureOperation(.updateProfile) {
                let changeRequest = user.createProfileChangeRequest()
                changeRequest.displayName = displayName
                changeRequest.photoURL = photoURL
                try await changeRequest.commitChanges()
                try await user.reload()
                self.refreshCurrentUserCache()
            }
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func updateUserEmail(to newEmail: String) async throws {
        clearAuthError()
        prepareSecureOperationContext()

        guard isValidEmail(newEmail) else {
            throw AuthError.invalidEmail
        }

        do {
            let user = try requireCurrentUser()
            try await performSecureOperation(.updateEmail) {
                Auth.auth().useAppLanguage()
                // Per Firebase iOS Manage Users: send verification before updating email
                try await user.sendEmailVerification(beforeUpdatingEmail: newEmail)
                try await user.reload()
                self.refreshCurrentUserCache()
            }
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func safeUpdateEmail(to newEmail: String) async throws {
        try await updateUserEmail(to: newEmail)
    }

    func updateUserPassword(to newPassword: String) async throws {
        clearAuthError()
        prepareSecureOperationContext()

        guard isValidPassword(newPassword) else {
            throw AuthError.weakPassword
        }

        do {
            let user = try requireCurrentUser()
            try await performSecureOperation(.updatePassword) {
                try await user.updatePassword(to: newPassword)
                try await user.reload()
                self.refreshCurrentUserCache()
            }
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func safeUpdatePassword(to newPassword: String) async throws {
        try await updateUserPassword(to: newPassword)
    }

    func sendEmailVerification() async throws {
        clearAuthError()
        prepareSecureOperationContext()

        do {
            Auth.auth().useAppLanguage()
            let user = try requireCurrentUser()
            try await performSecureOperation(.sendVerificationEmail) {
                try await user.sendEmailVerification()
            }
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func deleteCurrentUser() async throws {
        clearAuthError()
        prepareSecureOperationContext()

        do {
            let user = try requireCurrentUser()
            try await performSecureOperation(.deleteAccount) {
                try await user.delete()
            }
            refreshCurrentUserCache()
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func reauthenticateUser(with credential: AuthCredential) async throws {
        clearAuthError()

        do {
            let user = try requireCurrentUser()
            try await user.reauthenticate(with: credential)
            try await user.reload()
            refreshCurrentUserCache()
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func reloadUser() async throws {
        clearAuthError()
        prepareSecureOperationContext()

        do {
            let user = try requireCurrentUser()
            try await performSecureOperation(.reloadUser) {
                try await user.reload()
                self.refreshCurrentUserCache()
            }
        } catch let authError as AuthError {
            handleAuthError(authError)
            throw authError
        } catch {
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }

    func emailCredential(email: String, password: String) throws -> AuthCredential {
        guard isValidEmail(email) else {
            throw AuthError.invalidEmail
        }
        guard isValidPassword(password) else {
            throw AuthError.weakPassword
        }
        return EmailAuthProvider.credential(withEmail: email, password: password)
    }

    func appleCredential(idToken: String, rawNonce: String) -> AuthCredential {
        OAuthProvider.credential(
            providerID: AuthProviderID.apple,
            idToken: idToken,
            rawNonce: rawNonce,
            accessToken: nil
        )
    }

    func googleCredential(idToken: String, accessToken: String) -> AuthCredential {
        GoogleAuthProvider.credential(withIDToken: idToken, accessToken: accessToken)
    }

    func appleCredential(from authorization: ASAuthorization) throws -> AuthCredential {
        guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
            throw AuthError.invalidCredential
        }

        guard let nonce = currentNonce else {
            throw AuthError.invalidCredential
        }

        guard let appleIDToken = appleIDCredential.identityToken,
              let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
            throw AuthError.invalidCredential
        }

        return appleCredential(idToken: idTokenString, rawNonce: nonce)
    }

    func googleCredential(from result: GIDSignInResult) throws -> AuthCredential {
        guard let idToken = result.user.idToken?.tokenString else {
            throw AuthError.invalidCredential
        }
        let accessToken = result.user.accessToken.tokenString
        return googleCredential(idToken: idToken, accessToken: accessToken)
    }
    
    // MARK: - Validation Methods
    
    /// Validate email format using regex
    private func isValidEmail(_ email: String) -> Bool {
        // Basic checks first
        guard !email.isEmpty,
              email.contains("@"),
              email.count <= 254, // RFC 5321 limit
              !email.hasPrefix("@"),
              !email.hasSuffix("@") else {
            return false
        }
        
        // More permissive regex that handles international domains and edge cases
        let emailRegEx = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPredicate.evaluate(with: email)
    }
    
    /// Validate password strength (minimum 6 characters as per Firebase docs)
    private func isValidPassword(_ password: String) -> Bool {
        return password.count >= 6
    }

    /// Sign out current user
    func signOut() throws {
        isLoading = true
        clearAuthError() // Clear errors upfront for consistent state
        
        defer { 
            isLoading = false 
        }
        
        do {
            try Auth.auth().signOut()
            // The auth state listener will automatically handle:
            // - currentUser = nil
            // - authState = .unauthenticated  
            // - isAuthenticated = false
            // - loadLocalPreferences()
            
            print("✅ User signed out successfully")
        } catch {
            print("❌ Sign out failed with error: \(error)")
            let mappedError = AuthError.from(error)
            handleAuthError(mappedError)
            throw mappedError
        }
    }
    
    /// Sign out current user (UI-friendly version that handles errors internally)
    func signOutSilently() {
        do {
            try signOut()
        } catch {
            handleAuthError(error)
        }
    }

    // MARK: - User Preferences Management
    
    /// Fetch or create user profile as per Firestore_Implementation_Directive.md
    /// This ensures every authenticated user has preferences in Firestore
    private func fetchOrCreateUserProfile(for user: User) async throws -> UserPreferences {
        let userId = user.uid
        
        if let preferences = try await UserProfileService.shared.fetchPreferences(for: userId) {
            // 老用户：成功获取到偏好设置，直接返回
            print("🔐 Fetched existing preferences for user: \(userId)")
            return preferences
        } else {
            // 新用户：未找到偏好设置，创建一份默认设置
            var defaultPreferences = UserPreferences.default
            defaultPreferences.userId = userId
            
            // 将默认设置立即保存回 Firestore，为用户完成初始化
            try await UserProfileService.shared.savePreferences(defaultPreferences, for: userId)
            print("🔐 Created new preferences for user: \(userId)")
            
            return defaultPreferences
        }
    }

    private func loadUserPreferences(for userId: String) {
        preferencesLoadingState = .loading
        
        Task {
            do {
                let preferences = try await UserProfileService.shared.fetchOrCreatePreferences(for: userId)
                await MainActor.run {
                    self.userPreferences = preferences
                    self.preferencesLoadingState = .success
                }
                // Persist locally so logout/login retains preferences
                self.saveLocalPreferences()
            } catch let error as FirestoreError {
                await MainActor.run {
                    self.preferencesLoadingState = .error(error)
                }
            } catch {
                await MainActor.run {
                    self.preferencesLoadingState = .error(.unknownError(error))
                }
            }
        }
    }

    /// Save user preferences to Firestore via service (Task 20)
    func saveUserPreferences(_ preferences: UserPreferences) async throws {
        guard let userId = currentUser?.uid else { throw AuthError.userNotFound }
        
        preferencesLoadingState = .loading
        
        do {
            try await UserProfileService.shared.savePreferences(preferences, for: userId)
            await MainActor.run {
                self.userPreferences = preferences
                self.preferencesLoadingState = .success
            }
        } catch let error as FirestoreError {
            await MainActor.run {
                self.preferencesLoadingState = .error(error)
            }
            throw error
        } catch {
            let mappedError = FirestoreError.unknownError(error)
            await MainActor.run {
                self.preferencesLoadingState = .error(mappedError)
            }
            throw mappedError
        }
    }

    /// Update user preferences (saves locally and syncs to cloud if authenticated)
    func updatePreferences(_ preferences: UserPreferences) async {
        userPreferences = preferences
        saveLocalPreferences()

        if isAuthenticated {
            await syncPreferencesToCloud()
        }
    }

    /// Load preferences from SwiftData storage
    private func loadLocalPreferences() {
        Task {
            do {
                let storageService = SwiftDataStorageService.shared
                if let savedPreferences = try storageService.fetchUserPreferences() {
                    await MainActor.run {
                        userPreferences = savedPreferences.toUserPreferences()
                        let total = (userPreferences?.numberOfAdults ?? 1) + (userPreferences?.numberOfKids ?? max(0, (userPreferences?.familySize ?? 2) - 1))
                        print("📱 Loaded local preferences from SwiftData: \(userPreferences?.restrictionsCount ?? 0) restrictions, family members: \(total) (adults: \(userPreferences?.numberOfAdults ?? 1), kids: \(userPreferences?.numberOfKids ?? max(0, (userPreferences?.familySize ?? 2) - 1)))")
                    }
                } else {
                    await MainActor.run {
                        userPreferences = .default
                        print("📱 Using default preferences")
                    }
                }
            } catch {
                await MainActor.run {
                    userPreferences = .default
                    print("❌ Failed to load preferences from SwiftData: \(error)")
                }
            }
        }
    }

    /// Save preferences to SwiftData storage
    private func saveLocalPreferences() {
        guard let preferences = userPreferences else { return }
        
        Task {
            do {
                let storageService = SwiftDataStorageService.shared
                try await storageService.saveUserPreferences(preferences)
                print("💾 Saved local preferences to SwiftData")
            } catch {
                print("❌ Failed to save preferences to SwiftData: \(error)")
                // Fallback to UserDefaults for backward compatibility
                if let data = try? JSONEncoder().encode(preferences) {
                    UserDefaults.standard.set(data, forKey: userDefaultsKey)
                    print("💾 Saved preferences to UserDefaults as fallback")
                }
            }
        }
    }



    /// Sync preferences to Firestore (authenticated users only)
    private nonisolated func syncPreferencesToCloud() async {
        guard let userId = await currentUser?.uid else { return }
        do {
            let prefs = await userPreferences ?? UserPreferences.createDefault(for: userId)
            try await UserProfileService.shared.savePreferences(prefs, for: userId)
            print("☁️ Synced preferences to cloud")
        } catch {
            print("❌ Failed to sync preferences to cloud: \(error.localizedDescription)")
        }
    }

    /// Load preferences from Firestore (authenticated users only)
    private nonisolated func syncPreferencesFromCloud() async {
        guard let userId = await currentUser?.uid else { return }
        do {
            let prefs = try await UserProfileService.shared.fetchOrCreatePreferences(for: userId)
            await MainActor.run {
                userPreferences = prefs
                saveLocalPreferences()
            }
            print("☁️ Loaded preferences from cloud: \(prefs.restrictionsCount) restrictions")
        } catch {
            print("❌ Failed to load preferences from cloud: \(error.localizedDescription)")
        }
    }

    // MARK: - Computed Properties

    /// User display name for UI
    var userDisplayName: String {
        if let user = currentUser {
            return user.displayName ?? user.email ?? "User"
        }
        return "Anonymous User"
    }

    /// User email for UI
    var userEmail: String? {
        return currentUser?.email
    }

    /// Returns true if user is anonymous
    var isAnonymous: Bool {
        return currentUser?.isAnonymous ?? true
    }
    
    // MARK: - Developer Methods
    
    /// Mock developer login for testing purposes
    func mockDeveloperLogin() {
        // 模拟开发者登录状态
        isAuthenticated = true
        authError = nil
        
        // 设置示例用户偏好
        userPreferences = createDevModePreferences()
    }
    
    /// Mock developer preferences for testing
    var mockDeveloperPreferences: UserPreferences? {
        return createDevModePreferences()
    }
    
    /// Creates sample user preferences for developer mode
    private func createDevModePreferences() -> UserPreferences {
        var devPreferences = UserPreferences.createDefault(for: "dev_user")
        devPreferences.numberOfAdults = 2
        devPreferences.numberOfKids = 2
        devPreferences.familySize = 4
        devPreferences.familyMembers = [
            FamilyMember(name: "Alice", age: 32, specialDiets: [.vegetarian]),
            FamilyMember(name: "Bob", age: 35, specialDiets: []),
            FamilyMember(name: "Charlie", age: 8, specialDiets: []),
            FamilyMember(name: "Diana", age: 6, specialDiets: [.lowSodium])
        ]
        devPreferences.dietaryRestrictions = [.vegetarian, .lowSodium]
        devPreferences.strictExclusions = [.pork, .alcohol]
        devPreferences.allergiesIntolerances = [.treeNuts, .milk, .wheat]
        devPreferences.respectRestrictions = true
        devPreferences.theme = "system"
        devPreferences.notifications = true
        return devPreferences
    }

    // MARK: - Apple Sign-In Helper Methods

    // Generate a random nonce for authentication
    private func randomNonceString(length: Int = 32) -> String {
        precondition(length > 0)
        let charset: [Character] = Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
        var result = ""
        var remainingLength = length
        
        while remainingLength > 0 {
            let randoms: [UInt8] = (0 ..< 16).map { _ in
                var random: UInt8 = 0
                let errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random)
                if errorCode != errSecSuccess {
                    fatalError("Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)")
                }
                return random
            }
            
            randoms.forEach { random in
                if remainingLength == 0 {
                    return
                }
                
                if random < charset.count {
                    result.append(charset[Int(random)])
                    remainingLength -= 1
                }
            }
        }
        
        return result
    }

    // Compute the SHA256 hash of a string
    private func sha256(_ input: String) -> String {
        let inputData = Data(input.utf8)
        let hashedData = SHA256.hash(data: inputData)
        let hashString = hashedData.compactMap {
            String(format: "%02x", $0)
        }.joined()
        
        return hashString
    }
}

// Structured meal plan generation dispatches work via TaskGroups. Declare unchecked
// Sendable conformance so references can hop concurrency domains safely.
extension AuthenticationService: @unchecked Sendable {}
