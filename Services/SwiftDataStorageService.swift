import Foundation
import SwiftData
import SwiftUI
import UIKit

/// SwiftData storage service for local data persistence
/// Replaces UserDefaults-based storage with modern SwiftData architecture
@Observable
@MainActor
final class SwiftDataStorageService {
    nonisolated static let shared = SwiftDataStorageService()

    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?

    // MARK: - Initialization

    nonisolated init() {
        Task { @MainActor in
            setupModelContainer()
        }
    }

    private func setupModelContainer() {
        do {
            let schema = Schema([
                SavedRecipe.self,
                SavedIngredient.self,
                SavedUserPreferences.self,
                ShoppingListItem.self,
                RecipeHistory.self
            ])

            let modelConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: false,
                allowsSave: true
            )

            modelContainer = try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )

            modelContext = ModelContext(modelContainer!)
            print("✅ SwiftData ModelContainer initialized successfully")

            // Perform initial data migration if needed
            Task {
                await migrateFromUserDefaults()
            }

        } catch {
            print("❌ Failed to create SwiftData ModelContainer: \(error)")
            // Fallback to in-memory storage for development
            setupInMemoryContainer()
        }
    }

    private func setupInMemoryContainer() {
        do {
            let schema = Schema([
                SavedRecipe.self,
                SavedIngredient.self,
                SavedUserPreferences.self,
                ShoppingListItem.self,
                RecipeHistory.self
            ])

            let modelConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )

            modelContainer = try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )

            modelContext = ModelContext(modelContainer!)
            print("⚠️ Using in-memory SwiftData storage as fallback")

        } catch {
            print("❌ Failed to create in-memory ModelContainer: \(error)")
        }
    }

    // MARK: - Container Access

    var container: ModelContainer? {
        return modelContainer
    }

    var context: ModelContext? {
        return modelContext
    }
    // MARK: - Readiness
    /// Returns true when the SwiftData model context is initialized
    var isReady: Bool { modelContext != nil }

    /// Wait until the SwiftData storage is ready (modelContext initialized)
    func waitUntilReady() async {
        while modelContext == nil {
            try? await Task.sleep(nanoseconds: 50_000_000) // 50ms
        }
    }

    // MARK: - Transactions
    /// Perform a set of storage operations atomically using an isolated context.
    /// - Creates a fresh ModelContext with autosave disabled, applies all operations,
    ///   and persists with a single save. If any error is thrown, no changes are saved
    ///   and the main context remains unaffected (full rollback).
    /// - On success, replaces the main context with a fresh one to reflect persisted state.
    func performTransaction<T>(_ operations: (ModelContext) throws -> T) async throws -> T {
        guard let container = modelContainer else {
            throw StorageError.contextNotAvailable
        }

        // Use an isolated transaction context to prevent leaking unsaved changes
        let txContext = ModelContext(container)
        #if swift(>=5.9)
        // Disable autosave to avoid intermediate persistence
        txContext.autosaveEnabled = false
        #endif

        do {
            let result = try operations(txContext)
            try txContext.save()
            // Refresh the main context to pick up committed changes
            self.modelContext = ModelContext(container)
            return result
        } catch {
            // No save occurred; txContext changes are discarded by deallocation
            throw StorageError.updateFailed(error)
        }
    }


    // MARK: - Recipe Management

    /// Remove all user-specific data persisted in SwiftData.
    func clearAllUserData() async throws {
        await waitUntilReady()
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        do {
            try deleteAll(SavedRecipe.self, from: context)
            try deleteAll(SavedIngredient.self, from: context)
            try deleteAll(ShoppingListItem.self, from: context)
            try deleteAll(RecipeHistory.self, from: context)
            try deleteAll(SavedUserPreferences.self, from: context)
            try context.save()
            print("🧹 Cleared all SwiftData user data")
        } catch {
            print("❌ Failed to clear SwiftData user data: \(error)")
            throw StorageError.deleteFailed(error)
        }
    }

    func saveRecipe(_ recipe: Recipe, image: UIImage? = nil) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let imageData = image?.jpegData(compressionQuality: 0.8)

        let savedRecipe = SavedRecipe(
            name: recipe.recipeTitle,
            ingredients: recipe.ingredients,
            instructions: recipe.instructions.joined(separator: "\n"),
            imageData: imageData,
            cookingTime: 30, // Default cooking time since Recipe doesn't have this field
            servings: 2,     // Default servings since Recipe doesn't have this field
            difficulty: "medium", // Default difficulty since Recipe doesn't have this field
            tags: []         // Default tags since Recipe doesn't have this field
        )

        context.insert(savedRecipe)

        do {
            try context.save()
            print("✅ Recipe saved: \(recipe.recipeTitle)")
        } catch {
            print("❌ Failed to save recipe: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    func fetchAllRecipes() async throws -> [SavedRecipe] {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let descriptor = FetchDescriptor<SavedRecipe>()

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ Failed to fetch recipes: \(error)")
            throw StorageError.fetchFailed(error)
        }
    }

    func deleteRecipe(_ recipe: SavedRecipe) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        context.delete(recipe)

        do {
            try context.save()
            print("✅ Recipe deleted: \(recipe.name)")
        } catch {
            print("❌ Failed to delete recipe: \(error)")
            throw StorageError.deleteFailed(error)
        }
    }

    // MARK: - Ingredient Management (Pantry)

    func saveIngredient(_ ingredient: Ingredient) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let savedIngredient = SavedIngredient(
            id: ingredient.id,
            name: ingredient.name,
            category: ingredient.category.rawValue,
            dateAdded: ingredient.dateAdded,
            expiryDate: nil,
            quantity: nil,
            notes: nil,
            isRecent: false,
            notificationCycle: ingredient.notificationCycle
        )

        context.insert(savedIngredient)

        do {
            try context.save()
            print("✅ Ingredient saved: \(ingredient.name)")
        } catch {
            print("❌ Failed to save ingredient: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    func saveIngredients(_ ingredients: [Ingredient]) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        for ingredient in ingredients {
            // Append new ingredients (no duplicate checks here by design)
            let savedIngredient = SavedIngredient(
                id: ingredient.id,
                name: ingredient.name,
                category: ingredient.category.rawValue,
                dateAdded: ingredient.dateAdded,
                expiryDate: nil,
                quantity: nil,
                notes: nil,
                isRecent: true,
                notificationCycle: ingredient.notificationCycle
            )
            context.insert(savedIngredient)
        }

        do {
            try context.save()
            print("✅ Ingredients saved (append): \(ingredients.count) items")
        } catch {
            print("❌ Failed to save ingredients: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    /// Replace all saved ingredients with the provided list (authoritative write)
    func replaceAllIngredients(with ingredients: [Ingredient]) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        // Delete all existing SavedIngredient records
        let fetch = FetchDescriptor<SavedIngredient>()
        let existing = try context.fetch(fetch)
        for item in existing { context.delete(item) }

        // Insert the new authoritative set
        for ingredient in ingredients {
            let savedIngredient = SavedIngredient(
                id: ingredient.id,
                name: ingredient.name,
                category: ingredient.category.rawValue,
                dateAdded: ingredient.dateAdded,
                expiryDate: nil,
                quantity: nil,
                notes: nil,
                isRecent: false,
                notificationCycle: ingredient.notificationCycle
            )
            context.insert(savedIngredient)
        }

        do {
            try context.save()
            print("✅ Replaced all ingredients with \(ingredients.count) items")
        } catch {
            print("❌ Failed to replace ingredients: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    /// Clear all saved ingredients (used for first-launch reset)
    func clearAllIngredients() async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }
        let fetch = FetchDescriptor<SavedIngredient>()
        do {
            let existing = try context.fetch(fetch)
            for item in existing { context.delete(item) }
            try context.save()
            print("✅ Cleared all saved ingredients")
        } catch {
            print("❌ Failed to clear ingredients: \(error)")
            throw StorageError.deleteFailed(error)
        }
    }

    func fetchAllIngredients() throws -> [SavedIngredient] {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let descriptor = FetchDescriptor<SavedIngredient>()

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ Failed to fetch ingredients: \(error)")
            throw StorageError.fetchFailed(error)
        }
    }

    func deleteIngredient(_ ingredient: SavedIngredient) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        context.delete(ingredient)

        do {
            try context.save()
            print("✅ Ingredient deleted: \(ingredient.name)")
        } catch {
            print("❌ Failed to delete ingredient: \(error)")
            throw StorageError.deleteFailed(error)
        }
    }

    func clearRecentIngredients() async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let descriptor = FetchDescriptor<SavedIngredient>()

        do {
            let allIngredients = try context.fetch(descriptor)
            let recentIngredients = allIngredients.filter { $0.isRecent == true }
            for ingredient in recentIngredients {
                ingredient.isRecent = false
            }
            try context.save()
            print("✅ Cleared recent ingredient flags")
        } catch {
            print("❌ Failed to clear recent ingredients: \(error)")
            throw StorageError.updateFailed(error)
        }
    }

    // MARK: - User Preferences Management

    func saveUserPreferences(_ preferences: UserPreferences) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        // Delete existing preferences first (single user app)
        let existingPrefs = try fetchUserPreferences()
        if let existing = existingPrefs {
            context.delete(existing)
        }

        let savedPreferences = SavedUserPreferences.from(preferences)
        context.insert(savedPreferences)

        do {
            try context.save()
            print("✅ User preferences saved")
        } catch {
            print("❌ Failed to save user preferences: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    func fetchUserPreferences() throws -> SavedUserPreferences? {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let descriptor = FetchDescriptor<SavedUserPreferences>()

        do {
            let preferences = try context.fetch(descriptor)
            return preferences.first
        } catch {
            print("❌ Failed to fetch user preferences: \(error)")
            throw StorageError.fetchFailed(error)
        }
    }

    // MARK: - Recipe History Management

    func saveRecipeHistory(_ recipeName: String, ingredients: [String]) async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        let history = RecipeHistory(
            recipeName: recipeName,
            ingredients: ingredients
        )

        context.insert(history)

        do {
            try context.save()
            print("✅ Recipe history saved: \(recipeName)")
        } catch {
            print("❌ Failed to save recipe history: \(error)")
            throw StorageError.saveFailed(error)
        }
    }

    func fetchRecipeHistory(limit: Int = 10) throws -> [RecipeHistory] {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }

        var descriptor = FetchDescriptor<RecipeHistory>()
        descriptor.fetchLimit = limit

        do {
            return try context.fetch(descriptor)
        } catch {
            print("❌ Failed to fetch recipe history: \(error)")
            throw StorageError.fetchFailed(error)
        }
    }

    // MARK: - Data Migration

    private func migrateFromUserDefaults() async {
        print("🔄 Starting data migration from UserDefaults...")

        // Migrate user preferences
        let userDefaultsKey = "user_preferences_v2"
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let preferences = try? JSONDecoder().decode(UserPreferences.self, from: data) {

            do {
                try await saveUserPreferences(preferences)
                print("✅ Migrated user preferences from UserDefaults")

                // Remove old UserDefaults data after successful migration
                UserDefaults.standard.removeObject(forKey: userDefaultsKey)
            } catch {
                print("❌ Failed to migrate user preferences: \(error)")
            }
        }

        print("✅ Data migration completed")
    }

    // MARK: - Storage Errors

    enum StorageError: Error, LocalizedError {
        case contextNotAvailable
        case saveFailed(Error)
        case fetchFailed(Error)
        case deleteFailed(Error)
        case updateFailed(Error)

        var errorDescription: String? {
            switch self {
            case .contextNotAvailable:
                return "Storage context is not available"
            case .saveFailed(let error):
                return "Failed to save data: \(error.localizedDescription)"
            case .fetchFailed(let error):
                return "Failed to fetch data: \(error.localizedDescription)"
            case .deleteFailed(let error):
                return "Failed to delete data: \(error.localizedDescription)"
            case .updateFailed(let error):
                return "Failed to update data: \(error.localizedDescription)"
            }
        }
    }
}

// MARK: - SwiftUI Environment Integration

private extension SwiftDataStorageService {
    func deleteAll<M: PersistentModel>(_ type: M.Type, from context: ModelContext) throws {
        let descriptor = FetchDescriptor<M>()
        let items = try context.fetch(descriptor)
        for item in items {
            context.delete(item)
        }
    }
}

// MARK: - SwiftUI Environment Integration

extension EnvironmentValues {
    @Entry var swiftDataStorage: SwiftDataStorageService = SwiftDataStorageService.shared
}
