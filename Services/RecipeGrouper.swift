import Foundation

// Grouping service: flat UI models -> Day/Meal sections with simple heuristic
// Note: DaySection, MealSection, PantryUsage, and GenerationSummary are now defined in RecipeUIModel.swift

enum RecipeGrouper {
    static func group(_ items: [RecipeUIModel], days: Int, selectedMeals: Set<MealType>) -> (sections: [DaySection], summary: GenerationSummary) {
        let days = max(1, days)
        let meals = selectedMeals.isEmpty ? Set(MealType.allCases) : selectedMeals

        // Enhanced grouping: use item metadata when available, fallback to distribution
        var buckets: [[MealType: [RecipeUIModel]]] = Array(repeating: [:], count: days)

        for item in items {
            let dayIndex = item.dayIndex ?? (items.firstIndex(of: item) ?? 0) % days
            let mealType = item.mealType ?? meals.sorted { $0.rawValue < $1.rawValue }.first ?? .dinner
            buckets[dayIndex][mealType, default: []].append(item)
        }

        // Phase 1: Do not create placeholder items; leave empty slots empty

        // Build sections
        var sections: [DaySection] = []
        let today = Date()
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"

        for i in 0..<days {
            let date = Calendar.current.date(byAdding: .day, value: i, to: today) ?? today
            let dayStr = formatter.string(from: date)
            let displayStr = displayDateString(for: date, dayIndex: i)
            var mealSections: [MealSection] = []

            for meal in meals.sorted(by: { $0.rawValue < $1.rawValue }) {
                let dishes = buckets[i][meal] ?? []
                let totalTime = dishes.compactMap { $0.estimatedTime }.reduce(0, +)
                let pantryUsage = calculatePantryUsage(for: dishes)
                let section = MealSection(
                    mealType: meal,
                    displayName: meal.displayName,
                    dishes: dishes,
                    totalTime: totalTime,
                    pantryUsage: pantryUsage
                )
                if !dishes.isEmpty { mealSections.append(section) }
            }
            sections.append(DaySection(dayIndex: i, date: dayStr, displayDate: displayStr, meals: mealSections))
        }

        // Enhanced summary
        let summary = generateSummary(from: sections)
        return (sections, summary)
    }


    private static func calculatePantryUsage(for dishes: [RecipeUIModel]) -> PantryUsage {
        let used = dishes.compactMap { $0.ingredientsFromPantry?.count }.reduce(0, +)
        let add = dishes.compactMap { $0.additionalIngredients?.count }.reduce(0, +)
        let total = used + add
        let util = total > 0 ? Double(used) / Double(total) : 0.0
        return PantryUsage(itemsUsed: used, itemsTotal: total, utilizationRate: util)
    }

    private static func generateSummary(from sections: [DaySection]) -> GenerationSummary {
        let allDishes = sections.flatMap { $0.meals.flatMap { $0.dishes } }
        let totalRecipes = allDishes.count
        let totalDays = sections.count
        let avgTime = totalRecipes > 0 ? (allDishes.compactMap { $0.estimatedTime }.reduce(0, +) / max(1, allDishes.compactMap { $0.estimatedTime }.count)) : 0
        let usedTotal = allDishes.compactMap { $0.ingredientsFromPantry?.count }.reduce(0, +)
        let addTotal = allDishes.compactMap { $0.additionalIngredients?.count }.reduce(0, +)
        let pantryUtil = (usedTotal + addTotal) > 0 ? Double(usedTotal) / Double(usedTotal + addTotal) : 0.0

        let cuisineDist = allDishes
            .compactMap { $0.cuisine }
            .reduce(into: [String: Int]()) { counts, cuisine in
                counts[cuisine, default: 0] += 1
            }

        return GenerationSummary(
            totalRecipes: totalRecipes,
            totalDays: totalDays,
            avgCookingTime: avgTime,
            pantryUtilization: pantryUtil,
            cuisineDistribution: cuisineDist
        )
    }

    private static func displayDateString(for date: Date, dayIndex: Int) -> String {
        let calendar = Calendar.current
        let today = Date()

        if calendar.isDate(date, inSameDayAs: today) {
            return NSLocalizedString("date_today", comment: "Today")
        } else if calendar.isDate(date, inSameDayAs: calendar.date(byAdding: .day, value: 1, to: today) ?? today) {
            return NSLocalizedString("date_tomorrow", comment: "Tomorrow")
        } else {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return formatter.string(from: date)
        }
    }
}

