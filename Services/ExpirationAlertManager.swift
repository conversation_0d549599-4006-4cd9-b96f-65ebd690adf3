import Foundation
import SwiftUI

@Observable
@MainActor
final class ExpirationAlertManager {
    static let shared = ExpirationAlertManager()

    // UI state
    var showModal: Bool = false

    // Snooze/organize tracking (reset daily)
    private var snoozesToday: Int = 0
    private var lastSnoozeDay: Date? = nil
    private var lastOrganizeDay: Date? = nil
    private var didShowOnPantryEntryToday: Bool = false

    // Categories we consider perishable
    private let monitored: Set<PantryCategory> = [.proteins, .seafood, .dairy, .produce, .bakery, .plantBasedAlternatives]

    private init() {}

    func resetDailyIfNeeded(now: Date = Date()) {
        let today = Calendar.current.startOfDay(for: now)
        if let s = lastSnoozeDay, Calendar.current.startOfDay(for: s) != today {
            snoozesToday = 0
            lastSnoozeDay = nil
            didShowOnPantryEntryToday = false
        }
        if let o = lastOrganizeDay, Calendar.current.startOfDay(for: o) != today {
            lastOrganizeDay = nil
        }
    }

    func shouldSuppressNoonToday(now: Date = Date()) -> Bool {
        if let o = lastOrganizeDay {
            return Calendar.current.isDate(o, inSameDayAs: now)
        }
        return false
    }

    func dueItems(from items: [Ingredient], now: Date = Date()) -> [Ingredient] {
        let today = Calendar.current.startOfDay(for: now)
        return items.filter { ing in
            guard monitored.contains(ing.category) else { return false }
            let start = Calendar.current.startOfDay(for: ing.purchaseDate)
            let days = max(0, Calendar.current.dateComponents([.day], from: start, to: today).day ?? 0)
            let currentCycle = days / 7
            return currentCycle > ing.notificationCycle
        }
    }

    func evaluateOnForeground(pantryItems: [Ingredient]) {
        resetDailyIfNeeded()
        let due = dueItems(from: pantryItems)
        // Show only if there are due items and user hasn't snoozed twice and hasn't organized today
        if !due.isEmpty && snoozesToday < 2 && !shouldSuppressNoonToday() {
            showModal = true
        } else {
            showModal = false
        }
    }

    func onPantryAppear(pantryItems: [Ingredient]) {
        resetDailyIfNeeded()
        let due = dueItems(from: pantryItems)
        if !due.isEmpty && snoozesToday < 2 && !shouldSuppressNoonToday() && !didShowOnPantryEntryToday {
            didShowOnPantryEntryToday = true
            showModal = true
        }
    }

    func remindMeLater() {
        resetDailyIfNeeded()
        snoozesToday += 1
        lastSnoozeDay = Date()
        showModal = false
    }

    func organizeNow() {
        lastOrganizeDay = Date()
        // Persist suppress-noon flag for today
        UserDefaults.standard.set(lastOrganizeDay, forKey: "expirationSuppressNoonDate")
        showModal = false
        // Navigate to Pantry via deep link
        NotificationCenter.default.post(name: Notification.Name("openDeepLink"), object: nil, userInfo: ["url": "ingredientscanner://pantry"])
    }
}

struct ExpirationAlertView: View {
    @Environment(ExpirationAlertManager.self) private var alertMgr: ExpirationAlertManager

    let dueCount: Int

    var body: some View {
        VStack(spacing: 16) {
            Text("You have items that may be expiring")
                .font(.headline)
            Text("Needs Attention: \(dueCount)")
                .font(.subheadline)
                .foregroundStyle(.secondary)
            HStack(spacing: 12) {
                Button("Remind Me Later") { alertMgr.remindMeLater() }
                    .buttonStyle(.bordered)
                Button("Organize Now") { alertMgr.organizeNow() }
                    .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .padding()
    }
}

