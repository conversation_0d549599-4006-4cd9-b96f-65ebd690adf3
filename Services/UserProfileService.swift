
import Foundation
@preconcurrency import FirebaseFirestore
@preconcurrency import FirebaseAnalytics

/// A service that manages user preference synchronization with Firestore.
///
/// Provides core operations to save, fetch, and initialize user preferences
/// using Firebase Firestore with Codable support and offline awareness.
@MainActor
final class UserProfileService {
    
    // MARK: - Singleton
    static let shared = UserProfileService()
    private init() {}
    
    // MARK: - Dependencies
    private let db = Firestore.firestore()
    private let networkService = OptimizedNetworkService.shared
    
    // MARK: - Sync Status
    @Published var syncStatus: SyncStatusView.SyncStatus = .idle
    
    /// Get the current sync status
    func getCurrentSyncStatus() -> SyncStatusView.SyncStatus {
        return syncStatus
    }
    
    private func userDocument(for userId: String) -> DocumentReference {
        return db.collection("users").document(userId)
    }
    
    // MARK: - Core Methods with Offline Support
    
    /// Save user preferences to Firestore with offline support
    /// Uses setData(from:) as per Firebase official documentation
    func savePreferences(_ preferences: UserPreferences, for userId: String) async throws {
        syncStatus = .syncing
        
        var prefsToSave = preferences
        prefsToSave.userId = userId
        
        do {
            // Use Codable setData(from:) as per Firestore_Implementation_Directive.md
            try userDocument(for: userId).setData(from: prefsToSave)
            syncStatus = .success
            print("✅ User preferences saved successfully for user: \(userId)")
        } catch {
            syncStatus = .failure(error)
            print("❌ Failed to save user preferences to Firestore: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// Save preferences with offline awareness and UI feedback
    /// - Parameters:
    ///   - preferences: The preferences to persist
    ///   - userId: The Firebase Auth UID for the user
    ///   - showOfflineMessage: Whether to show offline-specific messaging
    func savePreferencesOfflineAware(_ preferences: UserPreferences, for userId: String, showOfflineMessage: Bool = true) async throws {
        
        if !networkService.isOnline && showOfflineMessage {
            syncStatus = .offline
            
            // Still save locally via Firestore's offline cache
            do {
                var prefsToSave = preferences
                prefsToSave.userId = userId
                
                // Use Codable setData(from:) even when offline - Firestore will cache it
                try userDocument(for: userId).setData(from: prefsToSave)
                
                // Will sync when online
                print("📱 Preferences saved offline - will sync when connection restored")
                
            } catch {
                logError(error, operation: "savePreferencesOfflineAware")
                syncStatus = .failure(mapUnknown(error))
                throw mapUnknown(error)
            }
        } else {
            // Online - use regular save
            try await savePreferences(preferences, for: userId)
        }
    }
    
    /// Fetch user preferences from Firestore with retry logic
    /// Uses data(as:) as per Firebase official documentation
    func fetchPreferences(for userId: String) async throws -> UserPreferences? {
        do {
            // Use Codable data(as:) to decode the document directly
            return try await userDocument(for: userId).getDocument(as: UserPreferences.self)
        } catch {
            // If document doesn't exist, return nil instead of throwing
            if let nsError = error as NSError?, 
               nsError.domain == FirestoreErrorDomain,
               let code = FirestoreErrorCode.Code(rawValue: nsError.code),
               code == .notFound {
                return nil
            }
            print("❌ Failed to fetch or decode user preferences: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// Fetch existing preferences or create default ones for new users
    /// Implements the logic from Firestore_Implementation_Directive.md
    func fetchOrCreatePreferences(for userId: String) async throws -> UserPreferences {
        // Try to fetch existing preferences
        if let preferences = try await fetchPreferences(for: userId) {
            // 老用户：成功获取到偏好设置，直接返回
            print("✅ Fetched existing preferences for user: \(userId)")
            return preferences
        } else {
            // 新用户：未找到偏好设置，创建一份默认设置
            var defaultPreferences = UserPreferences.default
            defaultPreferences.userId = userId
            
            // 将默认设置立即保存回 Firestore，为用户完成初始化
            try await savePreferences(defaultPreferences, for: userId)
            
            // Track new user creation
            trackNewUserCreation(userId: userId)
            
            print("✅ Created default preferences for new user: \(userId)")
            return defaultPreferences
        }
    }
    
    // MARK: - Batch Operations with Offline Support
    
    /// Perform multiple preference operations as a batch with offline awareness
    /// - Parameter operations: Array of operations to perform
    func performBatchOperations(_ operations: [() async throws -> Void]) async throws {
        guard networkService.shouldAttemptOperation() else {
            throw OfflineError.operationUnavailableOffline
        }
        
        syncStatus = .syncing
        
        do {
            // Execute all operations
            for operation in operations {
                try await operation()
            }
            syncStatus = .success
        } catch {
            syncStatus = .failure(error)
            throw error
        }
    }
    
    // MARK: - Network Status Integration
    
    /// Check if sync operations should be attempted
    var canPerformSyncOperations: Bool {
        return networkService.shouldAttemptOperation()
    }
    
    /// Get current network status for UI display
    var networkStatus: OptimizedNetworkService.NetworkStatus {
        return networkService.networkStatus
    }
    
    // MARK: - Errors & Logging
    
    enum UserProfileError: Error, LocalizedError {
        case userNotFound
        case decodingError(DecodingError)
        case networkError
        case unknownError(Error)
        case saveFailed(Error)
        case fetchFailed(Error)
        
        var errorDescription: String? {
            switch self {
            case .userNotFound:
                return "User preferences not found."
            case .decodingError(let underlying):
                return "Failed to decode user preferences: \(underlying.localizedDescription)"
            case .networkError:
                return "Network error while communicating with Firestore."
            case .unknownError(let error):
                return "Unexpected error: \(error.localizedDescription)"
            case .saveFailed(let error):
                return "Failed to save user preferences: \(error.localizedDescription)"
            case .fetchFailed(let error):
                return "Failed to fetch user preferences: \(error.localizedDescription)"
            }
        }
    }
    
    private func logError(_ error: Error, operation: String) {
        print("UserProfileService error during \(operation): \(error.localizedDescription)")
    }
    
    private func mapUnknown(_ error: Error) -> UserProfileError {
        let nsError = error as NSError
        if nsError.domain == FirestoreErrorDomain {
            if let code = FirestoreErrorCode.Code(rawValue: nsError.code) {
                switch code {
                case .unavailable:
                    return .networkError
                default:
                    return .unknownError(error)
                }
            }
        }
        if let decodingError = error as? DecodingError {
            return .decodingError(decodingError)
        }
        return .unknownError(error)
    }
    
    // Map raw Firestore NSError to app-specific FirestoreError (Task 19)
    private func mapFirestoreError(_ error: Error) -> FirestoreError {
        if let nsError = error as NSError? {
            switch nsError.domain {
            case FirestoreErrorDomain:
                if let code = FirestoreErrorCode.Code(rawValue: nsError.code) {
                    switch code {
                    case .notFound:
                        return .documentNotFound
                    case .permissionDenied:
                        return .permissionDenied
                    case .unavailable:
                        return .networkError
                    default:
                        return .unknownError(error)
                    }
                }
                return .unknownError(error)
            default:
                return .unknownError(error)
            }
        }
        return .unknownError(error)
    }
    
    // MARK: - Analytics (Task 23)
    private func trackNewUserCreation(userId: String) {
        Analytics.logEvent("new_user_preferences_created", parameters: [
            "user_id": userId,
            "timestamp_ms": Int(Date().timeIntervalSince1970 * 1000),
            "network_status": networkService.isOnline ? "online" : "offline"
        ])
    }
    
    // MARK: - Retry with Offline Awareness (Task 19)
    func fetchPreferencesWithRetry(for userId: String, maxRetries: Int = 3) async throws -> UserPreferences {
        
        // If offline, don't retry - just use cached data
        if !networkService.isOnline {
            if let preferences = try await fetchPreferences(for: userId) {
                return preferences
            } else {
                throw UserProfileError.userNotFound
            }
        }
        
        var retryCount = 0
        var lastError: Error? = nil
        
        while retryCount < maxRetries {
            do {
                if let preferences = try await fetchPreferences(for: userId) {
                    return preferences
                } else {
                    throw UserProfileError.userNotFound
                }
            } catch let error as NSError {
                lastError = error
                if error.domain == FirestoreErrorDomain,
                   let code = FirestoreErrorCode.Code(rawValue: error.code),
                   code == .unavailable {
                    retryCount += 1
                    if retryCount < maxRetries {
                        let delaySeconds = pow(Double(2), Double(retryCount))
                        try await Task.sleep(nanoseconds: UInt64(delaySeconds * 1_000_000_000))
                        continue
                    }
                } else {
                    throw mapUnknown(error)
                }
            }
        }
        throw mapUnknown(lastError ?? NSError(domain: "UserProfileService", code: -1))
    }
}

// MARK: - Published Property Support

extension UserProfileService: ObservableObject {
    // This allows UserProfileService to be used with @StateObject or @ObservedObject
    // while maintaining @MainActor compliance
}
