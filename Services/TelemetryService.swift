import Foundation
import FirebaseAnalytics

// Protocol for analytics providers
protocol AnalyticsProvider {
    func logEvent(_ name: String, parameters: [String: Any]?)
}

// Default Firebase Analytics provider
struct FirebaseAnalyticsProvider: AnalyticsProvider {
    func logEvent(_ name: String, parameters: [String: Any]?) {
        Analytics.logEvent(name, parameters: parameters)
    }
}

/// Telemetry service responsible for privacy-respecting analytics.
///
/// - Opt-in only: disabled by default. Enable via TelemetryService.setUserConsent(true).
/// - Never logs PII. Parameters should be counts, durations, booleans, or coarse-grained info.
@MainActor
final class TelemetryService {
    private let analyticsProvider: AnalyticsProvider
    private var isEnabled: Bool { Self.isOptedIn }

    // MARK: - Consent Management
    private static let consentKey = "telemetry_opt_in"
    static var isOptedIn: Bool {
        UserDefaults.standard.bool(forKey: consentKey)
    }
    static func setUserConsent(_ enabled: Bool) {
        UserDefaults.standard.set(enabled, forKey: consentKey)
    }

    // MARK: - Init
    init(analyticsProvider: AnalyticsProvider = FirebaseAnalyticsProvider()) {
        self.analyticsProvider = analyticsProvider
    }

    // MARK: - Scan Events
    func trackScanStarted(imageCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("scan_started", parameters: [
            "image_count": imageCount
        ])
    }

    func trackScanCompleted(imageCount: Int, successCount: Int, duration: TimeInterval, ingredientCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("scan_completed", parameters: [
            "image_count": imageCount,
            "success_count": successCount,
            "duration_seconds": Int(duration),
            "ingredient_count": ingredientCount
        ])
    }

    func trackScanError(imageCount: Int, errorType: String) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("scan_error", parameters: [
            "image_count": imageCount,
            "error_type": errorType
        ])
    }

    func trackAddToPantry(ingredientCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("add_to_pantry", parameters: [
            "ingredient_count": ingredientCount
        ])
    }

    // MARK: - Organizer Events
    func trackOrganizerStarted(itemCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("organizer_started", parameters: [
            "item_count": itemCount
        ])
    }

    func trackOrganizerCompleted(itemCount: Int, updatedCount: Int, mergedCount: Int, removedCount: Int, duration: TimeInterval) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("organizer_completed", parameters: [
            "item_count": itemCount,
            "updated_count": updatedCount,
            "merged_count": mergedCount,
            "removed_count": removedCount,
            "duration_seconds": Int(duration)
        ])
    }

    func trackOrganizerError(itemCount: Int, errorType: String) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("organizer_error", parameters: [
            "item_count": itemCount,
            "error_type": errorType
        ])
    }

    // MARK: - Performance Metrics
    func trackVisionPerformance(imageCount: Int, duration: TimeInterval) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("vision_performance", parameters: [
            "image_count": imageCount,
            "duration_seconds": Int(duration)
        ])
    }

    func trackGeminiPerformance(inputSize: Int, duration: TimeInterval) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("gemini_performance", parameters: [
            "input_size": inputSize,
            "duration_seconds": Int(duration)
        ])
    }

    // MARK: - Generic P0 Events (Task 1.6)
    func trackGenerateClick(mode: String, pantryCount: Int) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.generate_click(mode: mode, pantryCount: pantryCount)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    func trackGenerateResult(mode: String, resultCount: Int, durationMs: Int, request: [String: Any]) {
        guard isEnabled else { return }
        let requestJSON = TelemetryEventBuilder.encodeJSON(request)
        let e = TelemetryEventBuilder.generate_result(mode: mode, resultCount: resultCount, durationMs: durationMs, requestJSON: requestJSON)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    func trackCancelGenerate(mode: String, reason: String) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.cancel_generate(mode: mode, reason: reason)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    func trackRemoteConfigFetch(source: String, success: Bool, durationMs: Int) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.remote_config_fetch(source: source, success: success, durationMs: durationMs)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    func trackPantryStateCheck(state: String, count: Int) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.pantry_state_check(state: state, count: count)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    func trackUIPerformance(screen: String, avgFps: Int, firstPaintMs: Int) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.ui_performance(screen: screen, avgFps: avgFps, firstPaintMs: firstPaintMs)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    // MARK: - Do Not Eat Preferences

    func trackDoNotEatView(allergyCount: Int, strictCount: Int, customCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("do_not_eat_view", parameters: [
            "allergy_count": allergyCount,
            "strict_count": strictCount,
            "custom_count": customCount
        ])
    }

    func trackDoNotEatCustomAdded(length: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("do_not_eat_custom_added", parameters: [
            "length": max(0, length)
        ])
    }

    func trackDoNotEatCustomRemoved(length: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("do_not_eat_custom_removed", parameters: [
            "length": max(0, length)
        ])
    }

    // MARK: - NEW: Batch Processing Performance Metrics

    /// Track async image preparation performance (result-based)
    func trackImagePreparation(imageCount: Int, duration: TimeInterval, failedCount: Int) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("image_preparation", parameters: [
            "image_count": imageCount,
            "duration_seconds": Int(duration),
            "failed_count": failedCount,
            "images_per_second": imageCount > 0 ? Int(Double(imageCount) / duration) : 0
        ])
    }

    /// Track true batch processing performance vs sequential processing
    func trackBatchProcessingPerformance(imageCount: Int, batchDuration: TimeInterval, method: String) {
        guard isEnabled else { return }
        analyticsProvider.logEvent("batch_processing_performance", parameters: [
            "image_count": imageCount,
            "batch_duration_seconds": Int(batchDuration),
            "processing_method": method, // "true_batch" or "sequential"
            "images_per_second": imageCount > 0 ? Int(Double(imageCount) / batchDuration) : 0
        ])
    }

    /// Track overall Vision API efficiency improvements
    func trackVisionEfficiencyGains(imageCount: Int, oldDuration: TimeInterval?, newDuration: TimeInterval) {
        guard isEnabled else { return }
        var parameters: [String: Any] = [
            "image_count": imageCount,
            "new_duration_seconds": Int(newDuration)
        ]

        if let oldDuration = oldDuration {
            parameters["old_duration_seconds"] = Int(oldDuration)
            parameters["efficiency_gain_percent"] = Int(((oldDuration - newDuration) / oldDuration) * 100)
        }

        analyticsProvider.logEvent("vision_efficiency_gains", parameters: parameters)
    }

    // MARK: - Feedback (P2 optional)
    func trackUserFeedback(like: Bool?, rating: Int?) {
        guard isEnabled else { return }
        let e = TelemetryEventBuilder.user_feedback(like: like, rating: rating)
        analyticsProvider.logEvent(e.name, parameters: e.params)
    }

    // MARK: - Account Management Events

    struct AccountEventPayload {
        let provider: String
        let result: String
        let errorCode: String?
        let requiresRecentLogin: Bool
        let reauthAttempts: Int
        let languageCode: String?
        let durationMs: Int
    }

    enum AccountEvent: String {
        case profileUpdated = "account_profile_updated"
        case emailUpdated = "account_email_updated"
        case emailVerificationSent = "account_email_verification_sent"
        case passwordResetEmailSent = "account_password_reset_email_sent"
        case changePassword = "account_change_password_success"
        case deleteConfirmed = "account_delete_confirmed"
        case resetConfirmed = "account_reset_confirmed"
        case signOut = "account_sign_out"
    }

    func trackAccountEvent(_ event: AccountEvent, payload: AccountEventPayload) {
        guard isEnabled else { return }

        var parameters: [String: Any] = [
            "provider": payload.provider,
            "result": payload.result,
            "requires_recent_login": payload.requiresRecentLogin,
            "reauth_attempts": payload.reauthAttempts,
            "duration_ms": payload.durationMs
        ]

        if let errorCode = payload.errorCode {
            parameters["error_code"] = errorCode
        }

        if let languageCode = payload.languageCode {
            parameters["language_code"] = languageCode
        }

        analyticsProvider.logEvent(event.rawValue, parameters: parameters)
    }
}
