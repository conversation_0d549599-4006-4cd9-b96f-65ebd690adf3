import Foundation
import Network
import Combine
@preconcurrency import FirebaseFirestore

/// Optimized network service with comprehensive offline support
/// 
/// Features:
/// - Network status monitoring using NWPathMonitor
/// - Firestore offline persistence configuration
/// - Optimistic UI updates for offline scenarios
/// - Automatic sync when connection is restored
@MainActor
final class OptimizedNetworkService: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = OptimizedNetworkService()
    
    // MARK: - Network Status
    
    enum NetworkStatus {
        case online
        case offline
        case reconnecting
        
        var isConnected: Bool {
            return self == .online
        }
    }
    
    // MARK: - Properties
    
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor", qos: .utility)
    private var cancellables = Set<AnyCancellable>()
    
    // Observable properties
    @Published var networkStatus: NetworkStatus = .offline
    var isOnline: Bool { networkStatus.isConnected }
    var hasEverBeenOnline = false
    
    // Firestore instance
    private let db = Firestore.firestore()
    
    // MARK: - Initialization
    
    private init() {
        setupFirestoreOfflineSettings()
        startNetworkMonitoring()
    }
    
    deinit {
        Task { @MainActor [weak self] in
            self?.stopNetworkMonitoring()
        }
    }
    
    // MARK: - Firestore Offline Configuration
    
    private func setupFirestoreOfflineSettings() {
        // Enable offline persistence for Firestore
        let settings = FirestoreSettings()
        
        // Use memory and persistent cache settings for iOS 17+
        if #available(iOS 17.0, *) {
            settings.cacheSettings = PersistentCacheSettings()
        } else {
            // Fallback for older iOS versions
            settings.isPersistenceEnabled = true
        }
        
        db.settings = settings
        
        print("✅ Firestore offline persistence enabled")
    }
    
    // MARK: - Network Monitoring
    
    private func startNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            Task { @MainActor in
                self?.updateNetworkStatus(from: path)
            }
        }
        monitor.start(queue: monitorQueue)
        print("🌐 Network monitoring started")
    }
    
    private func stopNetworkMonitoring() {
        monitor.cancel()
        print("🌐 Network monitoring stopped")
    }
    
    @MainActor
    private func updateNetworkStatus(from path: NWPath) {
        let previousStatus = networkStatus
        
        switch path.status {
        case .satisfied:
            if !hasEverBeenOnline {
                hasEverBeenOnline = true
            }
            
            if previousStatus == .offline {
                networkStatus = .reconnecting
                
                // Brief reconnecting state before going online
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.networkStatus = .online
                    self.handleReconnection()
                }
            } else {
                networkStatus = .online
            }
            
        case .requiresConnection, .unsatisfied:
            networkStatus = .offline
            
        @unknown default:
            networkStatus = .offline
        }
        
        // Log network status changes
        if previousStatus != networkStatus {
            print("🌐 Network status changed: \(previousStatus) → \(networkStatus)")
        }
    }
    
    // MARK: - Reconnection Handling
    
    private func handleReconnection() {
        print("🔄 Connection restored - Firestore will auto-sync pending changes")
        
        // Firestore automatically handles sync when connection is restored
        // We can add custom logic here if needed for specific sync operations
        
        // Notify other services about reconnection
        NotificationCenter.default.post(name: .networkReconnected, object: nil)
    }
    
    // MARK: - Offline-Aware Operations
    
    /// Perform a Firestore operation with offline awareness
    /// - Parameter operation: The Firestore operation to perform
    /// - Returns: Result of the operation or cached/optimistic result if offline
    func performFirestoreOperation<T: Sendable>(
        operation: @escaping @Sendable () async throws -> T,
        fallback: @escaping @Sendable () -> T? = { nil }
    ) async -> Result<T, Error> {
        
        do {
            let result = try await operation()
            return .success(result)
        } catch {
            // If offline, try fallback or return appropriate offline error
            if !isOnline {
                if let fallbackResult = fallback() {
                    print("📱 Using offline fallback for operation")
                    return .success(fallbackResult)
                } else {
                    return .failure(OfflineError.operationUnavailableOffline)
                }
            } else {
                return .failure(error)
            }
        }
    }
    
    /// Check if a Firestore operation should be attempted based on network status
    /// - Parameter requiresNetwork: Whether the operation absolutely requires network
    /// - Returns: True if operation should proceed
    func shouldAttemptOperation(requiresNetwork: Bool = false) -> Bool {
        if requiresNetwork {
            return isOnline
        }
        
        // Most Firestore operations can work offline due to local cache
        return true
    }
    
    // MARK: - Optimistic Updates
    
    /// Perform an optimistic update that will sync when online
    /// - Parameters:
    ///   - localUpdate: Local state update to perform immediately
    ///   - remoteUpdate: Remote update to perform when online
    func performOptimisticUpdate<T: Sendable>(
        localUpdate: @escaping @Sendable () -> T,
        remoteUpdate: @escaping @Sendable () async throws -> Void
    ) async -> T {
        
        // Always perform local update immediately for responsive UI
        let result = localUpdate()
        
        // Queue remote update
        Task {
            do {
                try await remoteUpdate()
                print("✅ Optimistic update synced to server")
            } catch {
                print("⚠️ Failed to sync optimistic update: \(error)")
                
                // Could implement retry logic or conflict resolution here
                if !isOnline {
                    print("📱 Update will retry when connection is restored")
                }
            }
        }
        
        return result
    }
    
    // MARK: - Connection Quality
    
    var connectionQuality: ConnectionQuality {
        guard isOnline else { return .offline }
        
        // Could implement more sophisticated connection quality detection
        // based on latency, bandwidth, etc.
        return .good
    }
    
    enum ConnectionQuality {
        case offline
        case poor
        case good
        case excellent
    }
}

// MARK: - Offline Error Types

enum OfflineError: LocalizedError {
    case operationUnavailableOffline
    case syncPending
    case conflictResolutionRequired
    
    var errorDescription: String? {
        switch self {
        case .operationUnavailableOffline:
            return "This operation requires an internet connection"
        case .syncPending:
            return "Changes will sync when connection is restored"
        case .conflictResolutionRequired:
            return "Conflict resolution required - please review changes"
        }
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let networkReconnected = Notification.Name("networkReconnected")
    static let networkDisconnected = Notification.Name("networkDisconnected")
}

// MARK: - Preview Helper

#if DEBUG
extension OptimizedNetworkService {
    /// Force network status for testing/preview purposes
    func setNetworkStatusForTesting(_ status: NetworkStatus) {
        networkStatus = status
    }
}
#endif 