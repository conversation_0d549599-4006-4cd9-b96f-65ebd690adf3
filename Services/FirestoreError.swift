import Foundation
import FirebaseFirestore

enum FirestoreError: <PERSON><PERSON><PERSON>, LocalizedError {
    case networkError
    case permissionDenied
    case documentNotFound
    case serializationError
    case encodingFailed
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .networkError:
            return NSLocalizedString("Network connection issue. Please check your internet connection.", comment: "")
        case .permissionDenied:
            return NSLocalizedString("You don't have permission to access this data.", comment: "")
        case .documentNotFound:
            return NSLocalizedString("The requested data could not be found.", comment: "")
        case .serializationError:
            return NSLocalizedString("There was a problem processing the data.", comment: "")
        case .encodingFailed:
            return NSLocalizedString("Failed to encode data for storage.", comment: "")
        case .unknownError(let error):
            return NSLocalizedString("An unexpected error occurred: \(error.localizedDescription)", comment: "")
        }
    }
} 