import Foundation

/// Service responsible for persisting history data with an in-memory cache and disk-backed store.
/// - Index (lightweight) in UserDefaults
/// - Full payloads on disk per entry (JSON)
public final class HistoryStorageService: @unchecked Sendable {
    public static let shared = HistoryStorageService()

    private let defaults = UserDefaults.standard
    private let disk: DiskStorageProtocol
    private let queue = DispatchQueue(label: StorageConfiguration.storageQueueLabel, qos: .utility)

    // In-memory cache for quick results (up to capacity)
    private var quickCache: [QuickResultHistory] = []
    private var cacheLoaded = false

    private init(disk: DiskStorageProtocol = DiskStorageManager.shared) {
        self.disk = disk
    }

    /// Clear all cached quick history entries from memory, UserDefaults, and disk.
    public func clearAllQuick() {
        quickCache.removeAll()
        cacheLoaded = false
        defaults.removeObject(forKey: StorageConfiguration.quickHistoryIndexKey)
        defaults.removeObject(forKey: StorageConfiguration.legacyQuickHistoryKey)

        queue.async { [weak self] in
            guard let disk = self?.disk else { return }
            disk.purgeFolder(name: StorageConfiguration.quickHistoryFolderName)
        }

        print("🧹 Quick history cleared")
    }

    // MARK: - Public API (Quick)

    /// Load all quick results from in-memory cache or disk using progressive loading.
    /// Returns lightweight preview entries constructed from the index (titles only),
    /// and defers loading of full payloads to `loadQuick(id:)` when needed.
    public func loadAllQuick() -> [QuickResultHistory] {
        if cacheLoaded { return quickCache }

        let index = loadQuickIndex()
        var results: [QuickResultHistory] = []
        results.reserveCapacity(index.count)
        for entry in index {
            // Construct a lightweight preview model from index only (titles only).
            let minimalRecipes: [RecipeUIModel] = entry.recipeTitles.map {
                RecipeUIModel(id: UUID().uuidString, title: $0)
            }
            let preview = QuickResultHistory(
                id: entry.id,
                mealType: entry.mealType,
                numberOfDishes: minimalRecipes.count,
                totalCookTime: 0,
                cuisines: [],
                additionalRequest: nil,
                generatedAt: entry.generatedAt,
                recipes: minimalRecipes
            )
            results.append(preview)
        }
        cacheLoaded = true
        quickCache = results
        return results
    }

    /// Save a quick result: update cache, update index, and persist full payload to disk in background.
    public func saveQuick(_ item: QuickResultHistory) {
        // Memory
        quickCache.insert(item, at: 0)
        if quickCache.count > StorageConfiguration.quickHistoryCapacity {
            quickCache = Array(quickCache.prefix(StorageConfiguration.quickHistoryCapacity))
        }
        cacheLoaded = true

        // Index (lightweight)
        var index = loadQuickIndex()
        let key = quickDataKey(for: item.id)
        let preview = QuickHistoryIndexEntry(
            id: item.id,
            mealType: item.mealType,
            generatedAt: item.generatedAt,
            recipeCount: item.recipes.count,
            recipeTitles: item.recipes.map { $0.title },
            dataKey: key
        )
        index.insert(preview, at: 0)
        if index.count > StorageConfiguration.quickHistoryCapacity {
            index = Array(index.prefix(StorageConfiguration.quickHistoryCapacity))
        }
        saveQuickIndex(index)

        // Full payload (disk) in background
        queue.async { [disk, item, key] in
            try? disk.writeJSON(item, forKey: key, inFolder: StorageConfiguration.quickHistoryFolderName)
        }
    }

    /// Replace all quick results atomically.
    public func replaceAllQuick(_ items: [QuickResultHistory]) {
        let trimmed = Array(items.prefix(StorageConfiguration.quickHistoryCapacity))
        quickCache = trimmed
        cacheLoaded = true

        // Rebuild index
        var index: [QuickHistoryIndexEntry] = []
        index.reserveCapacity(trimmed.count)
        for it in trimmed {
            index.append(
                QuickHistoryIndexEntry(
                    id: it.id,
                    mealType: it.mealType,
                    generatedAt: it.generatedAt,
                    recipeCount: it.recipes.count,
                    recipeTitles: it.recipes.map { $0.title },
                    dataKey: quickDataKey(for: it.id)
                )
            )
        }
        saveQuickIndex(index)

        // Persist full payloads in background
        queue.async { [disk, trimmed] in
            for it in trimmed {
                let key = "quick_\(it.id.uuidString.lowercased())"
                try? disk.writeJSON(it, forKey: key, inFolder: StorageConfiguration.quickHistoryFolderName)
            }
        }
    }

    /// Delete a quick entry by ID.
    public func deleteQuick(id: UUID) {
        if cacheLoaded {
            quickCache.removeAll { $0.id == id }
        }
        var index = loadQuickIndex()
        if let found = index.firstIndex(where: { $0.id == id }) {
            let key = index[found].dataKey
            index.remove(at: found)
            saveQuickIndex(index)
            queue.async { [disk, key] in
                disk.delete(forKey: key, inFolder: StorageConfiguration.quickHistoryFolderName)
            }
        }
    }

    /// Load a full quick result from disk by ID if available.
    /// - Returns: Full `QuickResultHistory` or nil if missing/corrupted.
    public func loadQuick(id: UUID) -> QuickResultHistory? {
        // Look up data key from index (future-proof if key format changes)
        let index = loadQuickIndex()
        if let entry = index.first(where: { $0.id == id }) {
            if let full: QuickResultHistory = try? disk.readJSON(forKey: entry.dataKey, inFolder: StorageConfiguration.quickHistoryFolderName, as: QuickResultHistory.self) {
                return full
            }
        }
        // Fallback: compute key directly
        let key = quickDataKey(for: id)
        if let full: QuickResultHistory = try? disk.readJSON(forKey: key, inFolder: StorageConfiguration.quickHistoryFolderName, as: QuickResultHistory.self) {
            return full
        }
        return nil
    }

    /// Expose lightweight index for callers that want finer control over loading.
    public func quickIndex() -> [QuickHistoryIndexEntry] {
        loadQuickIndex()
    }

    /// Migrate legacy UserDefaults array into new (index + per-file) storage.
    public func migrateLegacyIfNeeded() {
        guard let data = defaults.data(forKey: StorageConfiguration.legacyQuickHistoryKey) else { return }
        guard let legacyItems = try? StorageConfiguration.jsonDecoder.decode([QuickResultHistory].self, from: data) else { return }
        // If new index is non-empty, skip migration (already migrated)
        if !loadQuickIndex().isEmpty { return }

        // Save all
        replaceAllQuick(legacyItems)
        // Remove legacy key
        defaults.removeObject(forKey: StorageConfiguration.legacyQuickHistoryKey)
    }

    // MARK: - Image Cache

    /// Save raw image data for URL string key with TTL.
    func cacheImageData(_ data: Data, urlString: String) {
        let key = imageKey(for: urlString)
        disk.saveData(data, forKey: key, inFolder: StorageConfiguration.imageCacheFolderName, ttl: StorageConfiguration.imageCacheTTL)
    }

    /// Load raw image data if not expired.
    func loadCachedImageData(urlString: String) -> Data? {
        let key = imageKey(for: urlString)
        return disk.loadData(forKey: key, inFolder: StorageConfiguration.imageCacheFolderName, ttl: StorageConfiguration.imageCacheTTL)
    }

    func cleanupExpiredImages() {
        disk.cleanupExpired(inFolder: StorageConfiguration.imageCacheFolderName, ttl: StorageConfiguration.imageCacheTTL)
    }

    // MARK: - Internals
    private func quickDataKey(for id: UUID) -> String { "quick_\(id.uuidString.lowercased())" }
    private func imageKey(for url: String) -> String {
        // simple hash to get a stable filesystem-friendly key
        String(url.hashValue)
    }

    private func loadQuickIndex() -> [QuickHistoryIndexEntry] {
        guard let data = defaults.data(forKey: StorageConfiguration.quickHistoryIndexKey) else { return [] }
        return (try? StorageConfiguration.jsonDecoder.decode([QuickHistoryIndexEntry].self, from: data)) ?? []
    }

    private func saveQuickIndex(_ entries: [QuickHistoryIndexEntry]) {
        if let data = try? StorageConfiguration.jsonEncoder.encode(entries) {
            defaults.set(data, forKey: StorageConfiguration.quickHistoryIndexKey)
        }
    }
}
