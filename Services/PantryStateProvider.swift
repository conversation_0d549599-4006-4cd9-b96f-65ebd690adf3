import Foundation
@preconcurrency import Combine

// MARK: - Pantry State & Provider (P0 01-1.3)

enum PantryState: Equatable {
    case empty
    case hasItems(count: Int)
    case loading
    case error(String)

    var isEmpty: Bool {
        if case .empty = self { return true }
        return false
    }

    var hasItems: Bool {
        if case .hasItems = self { return true }
        return false
    }
}

@MainActor
protocol PantryStateProvider {
    var currentState: PantryState { get }
    func checkPantryState() async -> PantryState
    func observePantryChanges() -> AsyncStream<PantryState>
}

// Enhanced adapter with better performance and reactive updates
@MainActor
final class DefaultPantryStateProvider: PantryStateProvider {
    private let pantryService: PantryService
    private var lastCount: Int = -1
    private var stateSubject = CurrentValueSubject<PantryState, Never>(.loading)
    private var cancellables = Set<AnyCancellable>()
    // Store per-stream subscriptions to avoid capturing non-Sendable AnyCancellable in @Sendable closures
    @MainActor private static var subscriptionStore: [UUID: AnyCancellable] = [:]
    @MainActor private static func storeSubscription(_ subscription: AnyCancellable, id: UUID) {
        subscriptionStore[id] = subscription
    }
    @MainActor private static func cancelSubscription(_ id: UUID) {
        subscriptionStore[id]?.cancel()
        subscriptionStore[id] = nil
    }


    init(pantryService: PantryService) {
        self.pantryService = pantryService
        setupInitialState()
        setupReactiveUpdates()
    }

    private func setupInitialState() {
        let count = pantryService.pantryItems.count
        let initialState: PantryState = count == 0 ? .empty : .hasItems(count: count)
        stateSubject.send(initialState)
        lastCount = count
    }

    private func setupReactiveUpdates() {
        // Use a more efficient timer-based approach with reduced frequency
        Timer.publish(every: 1.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.checkForChanges()
            }
            .store(in: &cancellables)
    }

    private func checkForChanges() {
        let newCount = pantryService.pantryItems.count
        if newCount != lastCount {
            lastCount = newCount
            let newState: PantryState = newCount == 0 ? .empty : .hasItems(count: newCount)
            stateSubject.send(newState)
        }
    }

    var currentState: PantryState {
        stateSubject.value
    }

    func checkPantryState() async -> PantryState {
        checkForChanges() // Force immediate check
        return currentState
    }

    func observePantryChanges() -> AsyncStream<PantryState> {
        AsyncStream { continuation in
            let id = UUID()
            // Subscribe to the subject and store by id to avoid capturing in @Sendable closure
            let subscription = stateSubject
                .removeDuplicates()
                .sink { state in
                    continuation.yield(state)
                }

            Task { @MainActor in
                Self.storeSubscription(subscription, id: id)
            }

            continuation.onTermination = { _ in
                Task { @MainActor in
                    Self.cancelSubscription(id)
                }
            }
        }
    }
}

