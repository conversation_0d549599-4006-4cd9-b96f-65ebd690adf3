import Foundation

enum RequestBuildError: Error, Equatable {
    case pantryEmpty
    case invalidConfiguration(String)
}

enum RecipeRequestBuilder {
    static func build(mode: UIMode, custom: CustomConfiguration, quick: QuickConfiguration, pantryState: PantryState, userEquipment: [String] = []) -> Result<RecipeGenerationRequest, RequestBuildError> {
        switch pantryState {
        case .empty:
            return .failure(.pantryEmpty)
        case .hasItems(let count):
            switch mode {
            case .quick:
                // Validate quick bounds
                guard (5...120).contains(quick.totalTimeMinutes) else { return .failure(.invalidConfiguration("invalid_quick_time")) }
                guard (1...6).contains(quick.numberOfDishes) else { return .failure(.invalidConfiguration("invalid_quick_dishes")) }
                let details = RequestDetails.build(from: quick, equipmentOwned: userEquipment)
                return .success(.init(mode: .quick, pantryItemCount: count, requestDetails: details))
            case .custom:
                // Validate per PRD v3 (no ingredientStrategy)
                guard !custom.selectedMeals.isEmpty else { return .failure(.invalidConfiguration("no_meals")) }
                guard (1...7).contains(custom.days) else { return .failure(.invalidConfiguration("invalid_days")) }
                for meal in custom.selectedMeals {
                    guard let cfg = custom.mealConfigurations[meal] else { return .failure(.invalidConfiguration("missing_meal_cfg_\(meal.rawValue)")) }
                    guard (5...120).contains(cfg.cookingTimeMinutes) else { return .failure(.invalidConfiguration("invalid_time_\(meal.rawValue)")) }
                    guard (1...6).contains(cfg.numberOfDishes) else { return .failure(.invalidConfiguration("invalid_dishes_\(meal.rawValue)")) }
                }
                let details = RequestDetails.build(from: custom, equipmentOwned: userEquipment)
                return .success(.init(mode: .custom, pantryItemCount: count, requestDetails: details))
            }
        case .loading:
            return .failure(.invalidConfiguration("pantry_loading"))
        case .error:
            return .failure(.invalidConfiguration("pantry_error"))
        }
    }
}

// MARK: - V6 Structured Plan Builder
extension RecipeRequestBuilder {
    /// Build a V6 MealPlanGenerationRequest with guarded constraints from CustomConfiguration.
    static func buildMealPlanRequest(from custom: CustomConfiguration,
                                     startDate: Date = Date(),
                                     userEquipment: [String] = []) -> Result<MealPlanGenerationRequest, RequestBuildError> {
        // Validate selection and day range (enforce 1...7 per V6)
        guard !custom.selectedMeals.isEmpty else { return .failure(.invalidConfiguration("no_meals")) }
        guard (1...7).contains(custom.days) else { return .failure(.invalidConfiguration("invalid_days")) }

        // Clamp startDate within [today, today+7]
        let cal = Calendar.current
        let today = cal.startOfDay(for: Date())
        guard let windowEnd = cal.date(byAdding: .day, value: 7, to: today) else {
            return .failure(.invalidConfiguration("date_window_error"))
        }
        let start = cal.startOfDay(for: startDate)
        let clampedStart: Date
        if start < today { clampedStart = today }
        else if start > windowEnd { return .failure(.invalidConfiguration("invalid_date_range")) }
        else { clampedStart = start }

        // Ensure days do not exceed window end
        let remainingDays = max(1, (cal.dateComponents([.day], from: clampedStart, to: windowEnd).day ?? 0) + 1)
        let boundedDays = min(custom.days, remainingDays)

        // Ensure slot configs exist for selected meals
        var slotConfigs: [MealType: MealConfig] = [:]
        for meal in custom.selectedMeals {
            let cfg = custom.mealConfigurations[meal] ?? MealConfig()
            guard (5...120).contains(cfg.cookingTimeMinutes) else { return .failure(.invalidConfiguration("invalid_time_\(meal.rawValue)")) }
            guard (1...6).contains(cfg.numberOfDishes) else { return .failure(.invalidConfiguration("invalid_dishes_\(meal.rawValue)")) }
            slotConfigs[meal] = cfg
        }

        let req = MealPlanGenerationRequest(
            startDate: clampedStart,
            days: boundedDays,
            selectedMeals: custom.selectedMeals,
            slotConfigs: slotConfigs,
            cuisines: custom.cuisines,
            additionalRequest: custom.additionalRequest.isEmpty ? nil : custom.additionalRequest,
            equipmentOwned: userEquipment
        )
        return .success(req)
    }
}
