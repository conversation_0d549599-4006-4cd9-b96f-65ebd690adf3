import Foundation

/// Disk-backed cache with TTL for recipe details.
/// Keys are stable and order-independent via `CacheKeyGenerator`.
@MainActor
class RecipeDetailCache: ObservableObject {
    static let shared = RecipeDetailCache()

    // MARK: - Types

    private struct Entry: Codable {
        let timestamp: TimeInterval // seconds since 1970
        let detail: RecipeDetail
    }

    // MARK: - Configuration

    private let ttl: TimeInterval = 48 * 60 * 60 // 48 hours
    private let inMemoryMax = 64

    // MARK: - State

    private var memoryCache: [String: (date: Date, detail: RecipeDetail)] = [:]
    private let fm = FileManager.default
    private lazy var cacheDir: URL = {
        let base = fm.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let dir = base.appendingPathComponent("RecipeDetailCache", isDirectory: true)
        try? fm.createDirectory(at: dir, withIntermediateDirectories: true)
        return dir
    }()

    private init() {}

    // MARK: - Public (legacy convenience)

    /// Legacy get by title only (kept for backward compatibility). Not stable across pantry/prefs changes.
    func getCachedDetail(for title: String) -> RecipeDetail? {
        let legacyKey = legacyKeyForTitle(title)
        return load(key: legacyKey)
    }

    /// Legacy cache by title only (kept for backward compatibility).
    func cacheDetail(_ detail: RecipeDetail, for title: String) {
        let legacyKey = legacyKeyForTitle(title)
        save(detail: detail, key: legacyKey)
    }

    // MARK: - Public (stable keys)

    func getCachedDetail(forTitle title: String, pantryNames: [String], preferences: RecipePreferences, mealSpecHash: String? = nil) -> RecipeDetail? {
        let keyGen = CacheKeyGenerator()
        let key = keyGen.detailCacheKey(title: title, pantryNames: pantryNames, preferences: preferences, mealSpecHash: mealSpecHash)
        return load(key: key)
    }

    func cacheDetail(_ detail: RecipeDetail, forTitle title: String, pantryNames: [String], preferences: RecipePreferences, mealSpecHash: String? = nil) {
        let keyGen = CacheKeyGenerator()
        let key = keyGen.detailCacheKey(title: title, pantryNames: pantryNames, preferences: preferences, mealSpecHash: mealSpecHash)
        save(detail: detail, key: key)
    }

    /// Clear all cached entries (memory + disk)
    func clearCache() {
        memoryCache.removeAll()
        if let files = try? fm.contentsOfDirectory(at: cacheDir, includingPropertiesForKeys: nil) {
            for url in files { try? fm.removeItem(at: url) }
        }
    }

    /// Opportunistic cleanup for expired entries on disk.
    func cleanupExpired() {
        if let files = try? fm.contentsOfDirectory(at: cacheDir, includingPropertiesForKeys: nil) {
            let now = Date().timeIntervalSince1970
            for url in files where url.pathExtension == "json" {
                if let data = try? Data(contentsOf: url),
                   let entry = try? JSONDecoder().decode(Entry.self, from: data) {
                    if now - entry.timestamp > ttl { try? fm.removeItem(at: url) }
                }
            }
        }
    }

    // MARK: - Private

    private func legacyKeyForTitle(_ title: String) -> String {
        let norm = title.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        return "legacy:v1:detail:" + norm.replacingOccurrences(of: " ", with: "_")
    }

    private func entryURL(for key: String) -> URL {
        // Sanitize key into filename (hex-like is expected from key generator)
        let safe = key.replacingOccurrences(of: "/", with: "_")
        return cacheDir.appendingPathComponent(safe).appendingPathExtension("json")
    }

    private func load(key: String) -> RecipeDetail? {
        // In-memory first
        if let hit = memoryCache[key] {
            if Date().timeIntervalSince(hit.date) <= ttl { return hit.detail }
            memoryCache.removeValue(forKey: key)
        }

        // Disk path
        let url = entryURL(for: key)
        guard let data = try? Data(contentsOf: url),
              let entry = try? JSONDecoder().decode(Entry.self, from: data) else {
            return nil
        }
        let now = Date().timeIntervalSince1970
        if now - entry.timestamp > ttl {
            try? fm.removeItem(at: url)
            return nil
        }
        evictIfNeeded()
        memoryCache[key] = (Date(), entry.detail)
        return entry.detail
    }

    private func save(detail: RecipeDetail, key: String) {
        evictIfNeeded()
        memoryCache[key] = (Date(), detail)
        let entry = Entry(timestamp: Date().timeIntervalSince1970, detail: detail)
        if let data = try? JSONEncoder().encode(entry) {
            let url = entryURL(for: key)
            try? data.write(to: url, options: [.atomic])
        }
    }

    private func evictIfNeeded() {
        if memoryCache.count <= inMemoryMax { return }
        // Remove the oldest entry
        if let oldest = memoryCache.min(by: { $0.value.date < $1.value.date })?.key {
            memoryCache.removeValue(forKey: oldest)
        }
    }
}
