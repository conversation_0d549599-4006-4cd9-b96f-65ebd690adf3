import Foundation

@MainActor
final class FavoritesManager {
    static let shared = FavoritesManager()

    private let store: FavoritesStore
    private let quickHistory: QuickHistoryManager
    private let planStore: PlanStore

    private init(
        store: FavoritesStore = .shared,
        quickHistory: QuickHistoryManager = .shared,
        planStore: PlanStore = .shared
    ) {
        self.store = store
        self.quickHistory = quickHistory
        self.planStore = planStore
    }

    // MARK: - Mutations

    func toggleFavorite(id: String) {
        let isFav = store.isFavorite(id: id)
        if isFav {
            // Removing favorite: clean up snapshot as well
            store.toggleFavorite(id: id)
            store.deleteSnapshot(for: id)
        } else {
            // Adding favorite: capture snapshot if possible
            if let ui = resolveUIModel(for: id), isValid(ui) {
                let snap = FavoriteSnapshot(
                    id: ui.id,
                    title: ui.title,
                    subtitle: ui.subtitle,
                    estimatedTime: ui.estimatedTime,
                    savedAt: Date()
                )
                store.saveSnapshot(snap)
            }
            store.toggleFavorite(id: id)
        }
    }

    /// Explicit remove for callers that want to ensure deletion only
    func removeFavorite(id: String) {
        if store.isFavorite(id: id) {
            store.toggleFavorite(id: id)
        }
        store.deleteSnapshot(for: id)
    }

    func isFavorite(id: String) -> Bool {
        store.isFavorite(id: id)
    }

    // MARK: - Queries

    /// Unified list of favorites built from Quick history and last saved Meal Plan.
    /// Deduplicated by recipe ID using FavoritesStore order (newest-first).
    func allItems() -> [FavoriteItem] {
        let ids = store.all() // newest-first IDs
        let quick = quickHistory.all()
        let plan = planStore.loadLastMealPrep()?.plan

        // Pre-index for faster lookup
        var quickIndex: [String: [(id: UUID, at: Date, model: RecipeUIModel)]] = [:]
        for entry in quick {
            for recipe in entry.recipes {
                quickIndex[recipe.id, default: []].append((entry.id, entry.generatedAt, recipe))
            }
        }

        var planIndex: [String: [(slotId: UUID, date: Date, meal: MealType, model: RecipeUIModel)]] = [:]
        if let plan = plan {
            for day in plan.days {
                for slot in day.meals {
                    let r = slot.recipe
                    planIndex[r.id, default: []].append((slot.slotId, day.date, slot.mealType, r))
                }
            }
        }

        var results: [FavoriteItem] = []
        var seen = Set<String>()
        for id in ids {
            guard !seen.contains(id) else { continue }
            var contexts: [FavoriteOrigin] = []
            var title: String = id
            let snapshot = store.snapshot(for: id)

            if let quickHits = quickIndex[id] {
                contexts.append(contentsOf: quickHits.map { .quick(quickId: $0.id, generatedAt: $0.at) })
                // Prefer title from a UI model
                if let first = quickHits.first { title = first.model.title }
            }

            if let planHits = planIndex[id] {
                contexts.append(contentsOf: planHits.map { .plan(slotId: $0.slotId, date: $0.date, meal: $0.meal) })
                if title == id, let first = planHits.first { title = first.model.title }
            }
            // If still using raw id as title, prefer snapshot title if available
            if title == id, let snap = snapshot { title = snap.title }

            results.append(FavoriteItem(id: id, title: title, contexts: contexts, snapshot: snapshot))
            seen.insert(id)
        }
        return results
    }

    /// Resolve a UI model for a favorite recipe ID by searching Plans first, then Quick.
    func resolveUIModel(for id: String) -> RecipeUIModel? {
        if let plan = planStore.loadLastMealPrep()?.plan {
            for day in plan.days {
                if let hit = day.meals.first(where: { $0.recipe.id == id }) {
                    let ui = hit.recipe
                    if isValid(ui) { return ui }
                }
            }
        }
        let quick = quickHistory.all()
        for entry in quick {
            if let hit = entry.recipes.first(where: { $0.id == id }) {
                if isValid(hit) { return hit }
            }
        }
        return nil
    }

    private func isValid(_ ui: RecipeUIModel) -> Bool {
        // Minimal integrity checks: stable id match and non-empty title
        guard !ui.id.isEmpty else { return false }
        let title = ui.title.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !title.isEmpty else { return false }
        return true
    }
}
