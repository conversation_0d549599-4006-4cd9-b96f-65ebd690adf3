name: IngredientScanner
options:
  bundleIdPrefix: com.kuo
  deploymentTarget:
    iOS: 17.0
  developmentLanguage: en

packages:
  Firebase:
    url: https://github.com/firebase/firebase-ios-sdk
    from: 11.0.0
  GoogleSignIn:
    url: https://github.com/google/GoogleSignIn-iOS
    from: 8.0.0
  
targets:
  IngredientScanner:
    type: application
    platform: iOS
    sources:
      - path: Application
      - path: Coordinator
      - path: Services
      - path: Models
      - path: Features
      - path: Views
        excludes:
          - Views/Custom/PreferencesPicker.swift
      - path: Utils
      - path: Utilities
      - path: Application/GoogleService-Info.plist
      - path: Application/api-keys.plist
    dependencies:
      - package: Firebase
        products:
          - FirebaseAuth
          - FirebaseFirestore
          - FirebaseAnalytics
      - package: GoogleSignIn
        products:
          - GoogleSignIn
    entitlements:
      path: Application/IngredientScanner.entitlements
    info:
      path: Application/Info.plist
      properties:
        CFBundleDisplayName: IngredientScanner
        CFBundleShortVersionString: "1.0"
        CFBundleVersion: "1"
        UILaunchStoryboardName: ""
        CFBundleURLTypes:
          - CFBundleTypeRole: Editor
            CFBundleURLSchemes:
              - com.googleusercontent.apps.435956804122-7eea3jllb7emfo5bdiilepmksmnrme2e
        UISupportedInterfaceOrientations:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        UISupportedInterfaceOrientations~ipad:
          - UIInterfaceOrientationPortrait
          - UIInterfaceOrientationPortraitUpsideDown
          - UIInterfaceOrientationLandscapeLeft
          - UIInterfaceOrientationLandscapeRight
        LSRequiresIPhoneOS: true
    settings:
      base:
        PRODUCT_BUNDLE_IDENTIFIER: com.kuo.ingredientscannertemp
        PRODUCT_NAME: IngredientScanner
        SWIFT_VERSION: 5.9
        TARGETED_DEVICE_FAMILY: 1,2
        DEVELOPMENT_TEAM: ""
        CODE_SIGN_STYLE: Automatic
        INFOPLIST_FILE: Application/Info.plist
        ENABLE_PREVIEWS: YES
        DEVELOPMENT_ASSET_PATHS: ""
        ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
        ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor
        CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION: YES
        SWIFT_STRICT_CONCURRENCY: complete
        ENABLE_USER_SCRIPT_SANDBOXING: YES
        IPHONEOS_DEPLOYMENT_TARGET: 17.0
  IngredientScannerTests:
    type: bundle.unit-test
    platform: iOS
    sources:
      - path: Tests
    dependencies:
      - target: IngredientScanner
    settings:
      GENERATE_INFOPLIST_FILE: YES
      IPHONEOS_DEPLOYMENT_TARGET: 17.0
