# 02 — ViewModel 与生成流程（P0）

与 PRD 章节：5.1/5.2/5.3、6.4、8.2、10.1

## 任务 2.1 RecipeGeneratorViewModel 基础
- 工时：6h | 依赖：01-1.1/1.3/1.4
- 验收：
  - @Published: mode, customConfiguration, viewState, pantryState, remoteConfiguration?
  - 依赖注入：APIServiceProtocol, PantryServiceProtocol
  - 订阅 PantryStateProvider.observePantryChanges()
- 产物：ViewModels/RecipeGeneratorViewModel.swift, Tests/RecipeGeneratorViewModelTests.swift

## 任务 2.2 canGenerate 计算属性（Quick-only）
- 工时：4h | 依赖：2.1
- 验收：
  - Quick: pantryState.hasItems && viewState != .loading
  - Custom：预留分支但返回 false（P1 开启）
  - 单测：空/非空 pantry、loading、边界
- 产物：ViewModels/RecipeGeneratorViewModel+CanGenerate.swift, Tests/CanGenerateTests.swift

## 任务 2.3 生成任务与取消
- 工时：8h | 依赖：2.1, 01-1.4
- 验收：
  - generateRecipes(): withAnimation 设置 loading，使用 Task 保存句柄
  - cancelGeneration(): 取消任务、回到 idle、触发 Haptics
  - performGeneration(): 调用 RequestBuilder → Adapter → Service；检查 Task.isCancelled
  - 单测（异步）：并发取消、成功、失败、取消后无回调污染
- 产物：ViewModels/RecipeGeneratorViewModel+Generation.swift, Tests/GenerationTaskTests.swift

## 任务 2.4 模式切换（保留/恢复配置）
- 工时：3h | 依赖：2.1
- 验收：
  - selectMode(.quick/.custom)：取消当前任务、恢复缓存的 Custom（P1 外壳可见）、回到 idle
  - 动画：withAnimation(.easeInOut)
  - 单测：模式切换取消、状态复位
- 产物：ViewModels/RecipeGeneratorViewModel+ModeSwitch.swift, Tests/ModeSwitchTests.swift



## 进度更新（2025-08-23）
- 已完成：
  - 2.1 基础（ViewModel 核心状态、DI、订阅 PantryStateProvider.observePantryChanges）
  - 2.2 canGenerate（Quick-only）：pantryState.hasItems && viewState != .loading；Custom 预留但返回 false（P0 禁用）
  - 2.3 生成任务与取消：generateRecipes/cancelGeneration/performGeneration；withAnimation 设置 loading；Task 句柄可取消；performGeneration 检查 Task.isCancelled；调用 RequestBuilder → Adapter → Service
  - 2.4 模式切换：selectMode(.quick/.custom) 取消当前任务、恢复缓存的 Custom 配置、withAnimation(.easeInOut) 回到 idle
- 产物提交：
  - Features/RecipeGenerator/RecipeGeneratorViewModel.swift（新增 canGenerate、可取消生成任务、模式切换）
  - Services/PantryStateProvider.swift（P0 适配器，AsyncStream + 轮询）
  - Services/APIProtocols.swift（协议桩，用于后续 DI）
  - Models/RecipeGenerationTypes.swift（占位 P0 枚举与配置）
- 运行验证：iPhone 16 模拟器 Debug 构建成功（见报告）
