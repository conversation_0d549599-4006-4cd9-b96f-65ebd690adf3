# 06 — 测试、无障碍与质量保障（跨阶段）

与 PRD 章节：8（范围）、9（UI 模型）、10（工程）、11（i18n/a11y）、12（Analytics）、15（实施）

## 任务 6.1 单元测试矩阵
- 工时：8h | 依赖：跨模块
- 验收：
  - RequestBuilder：Quick/Custom、pantryEmpty、字段映射
  - canGenerate：空/非空 pantry、loading、边界
  - ViewState：状态转换；取消后不可回调污染
  - RemoteConfig：成功/失败/回退/缓存
- 产物：Tests/*

## 任务 6.2 UI/快照测试
- 工时：8h | 依赖：03-3.3
- 验收：
  - 四态渲染（Idle/Loading/Loaded/Failed）
  - 按钮启用/禁用逻辑
  - 配置摘要卡片的文案/状态
- 产物：UITests/*, SnapshotTests/*

## 任务 6.3 无障碍（a11y）检查清单
- 工时：6h | 依赖：UI 完成度
- 验收：
  - VoiceOver：label/value/hint/traits 完整
  - 动态字体：Large/ExtraLarge 下布局可用
  - 对比度：高对比度与深色模式
- 产物：Docs/a11y-checklist.md, Tests/Accessibility/*

## 任务 6.4 性能监测与指导
- 工时：6h | 依赖：基础功能
- 验收：
  - 首屏渲染耗时、平均帧率采集（ui_performance 事件）
  - 对 List/Stack 使用 Lazy 组合、避免深层重排
  - 占位与渐进加载（redaction/placeholder）
- 产物：Utils/PerformanceMonitor.swift, Docs/performance.md

## 任务 6.5 文案与本地化
- 工时：4h | 依赖：核心文案固化
- 验收：
  - 初版仅英文；Localizable.strings 完整
  - 错误、提示、按钮、摘要模板均走本地化键
- 产物：Resources/Localizable.strings, Docs/copy-spec.md

## 任务 6.6 端到端演示与验收
- 工时：6h | 依赖：P0 完成
- 验收：
  - 设备/模拟器运行 Demo：Quick-only 完整路径
  - 取消/失败/恢复/禁用按钮 UX 正确
  - 事件上报无 PII，参数 JSON 格式
- 产物：Demo 脚本、演示视频、验收记录

