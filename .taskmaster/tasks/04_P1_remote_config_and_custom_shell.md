# 04 — 远程配置与 Custom 外壳（P1）

与 PRD 章节：4.3、5.6、6.1（Preferences）、7.6、8.1（P1）、12

## 任务 4.1 RemoteConfiguration Loader（Stub/本地 JSON + 24h 缓存）
- 工时：8h | 依赖：01-1.6
- 验收：
  - Service：fetch → cache → observe；无端点时使用 Bundle JSON
  - 缓存：UserDefaults + 时间戳；24h 过期策略
  - 降级：网络失败不阻塞 UX（使用本地 JSON）
  - 单测：成功/失败/回退/缓存过期
- 产物：Services/RemoteConfigurationManager.swift, Models/RemoteConfiguration.swift, Tests/RemoteConfigurationTests.swift

## 任务 4.2 ConfigurationSummary 卡片
- 工时：6h | 依赖：02-2.1
- 验收：
  - SwiftUI 卡片样式；动态更新；withAnimation(.easeInOut)
  - 文案使用本地化模板；未完备配置时提示下一步
  - UI 测试：启用/禁用、文案变更
- 产物：Views/Components/ConfigurationSummary.swift, Services/SummaryGenerator.swift, Tests/SummaryGeneratorTests.swift

## 任务 4.3 Custom 外壳（天数 + 餐食选择）
- 工时：6h | 依赖：02-2.4
- 验收：
  - DaysSelector（Picker .segmented 或 WheelPicker）
  - MealSelector（LazyVGrid Chips 多选）
  - 其他配置（时间/菜品/偏好/策略）隐藏或灰显
- 产物：Views/Custom/DaysSelector.swift, Views/Custom/MealSelector.swift, Tests/UI/CustomShellTests.swift

