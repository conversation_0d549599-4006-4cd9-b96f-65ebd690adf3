# 05 — 完整 Custom、分组结果与分析（P2）

与 PRD 章节：4.2（分组延后）、6.2（分组）、7.2、8.1（P2）、12

## 任务 5.1 Full Custom 配置（每餐时间/菜品数）
- 工时：10h | 依赖：04-4.3
- 验收：
  - TimeSlider（Slider 5–120，步进 5；去抖提交）
  - DishStepper（Stepper 1–6）
  - Preferences（LazyVGrid 单选）；IngredientStrategy（Picker/单选）
  - AdditionalRequest（TextEditor + 计数）
- 产物：Views/Custom/*, Tests/UI/CustomFullTests.swift

## 任务 5.2 分组结果（DaySection/MealSection）
- 工时：8h | 依赖：5.1
- 验收：
  - 将扁平 RecipeUIModel 转为 DaySection/MealSection 结构
  - List/Section 分组呈现；支持折叠/展开（可选）
  - 单测：分组逻辑与渲染
- 产物：Services/RecipeGrouper.swift, Views/Results/GroupedResultsView.swift, Tests/RecipeGrouperTests.swift

## 任务 5.3 Analytics 细化与反馈（Like/Rating 可选）
- 工时：6h | 依赖：01-1.6
- 验收：
  - 添加 user_feedback（like/dislike 或 1–5 rating）
  - 明确事件参数，JSON 字符串
  - UI 与 Telemetry 绑定
- 产物：Views/Feedback/*, Services/TelemetryEventBuilder+Feedback.swift, Tests/TelemetryFeedbackTests.swift

