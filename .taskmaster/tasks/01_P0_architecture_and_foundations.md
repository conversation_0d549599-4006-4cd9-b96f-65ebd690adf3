# 01 — 架构与基础设施（P0）

与 PRD 章节：5、6.4、7.3、8.1（P0）、10.1、10.2

## 任务 1.1 统一 ViewState 与状态机落地
- 工时：4h | 依赖：无
- 验收：
  - 定义 enum ViewState { idle, loading, loaded([RecipeUIModel]), failed(DisplayError) }
  - ViewModel 仅以 ViewState 驱动 UI（不出现 isLoading/errorMessage 等散落 flags）
  - 单测覆盖：idle→loading→loaded/failed 转换
- 产物：Models/ViewState.swift, Tests/ViewStateTests.swift

## 任务 1.2 DisplayError（本地化键）
- 工时：3h | 依赖：1.1
- 验收：
  - DisplayError 使用 NSLocalizedString 键（error_network 等）
  - 包含 recoveryAction（retry/reconfigure/gotoPantry）
  - 单测覆盖错误到文案键映射
- 产物：Models/DisplayError.swift, Resources/Localizable.strings, Tests/ErrorMappingTests.swift

## 任务 1.3 PantryStateProvider 适配器（AsyncStream）
- 工时：6h | 依赖：无
- 验收：
  - 定义 PantryState { empty, hasItems(count), loading, error }
  - 协议 PantryStateProvider：currentState、checkPantryState()、observePantryChanges()
  - 中途变空行为：正在运行允许完成；禁用新生成；Banner/Alert 文案与按钮禁用 UX（见 PRD 6.5）
  - 单测覆盖：状态推送、过渡、边界
- 产物：Services/PantryStateProvider.swift, Tests/PantryStateProviderTests.swift

## 任务 1.4 RequestBuilder 纯函数 + Service Adapter
- 工时：6h | 依赖：1.3
- 验收：
  - 纯函数 RecipeRequestBuilder.build(mode, customConfig, pantryState) -> Result<RecipeGenerationRequest, Error>
  - 新增 RecipeServiceAdapter：将 DTO → RecipeGenerationService(ingredients + RecipePreferences)
  - 文档：Custom 配置到 RecipePreferences 的映射（代码注释/README）
  - 单测：Quick/Custom、pantryEmpty、字段映射
- 产物：Services/RecipeRequestBuilder.swift, Services/RecipeServiceAdapter.swift, Tests/RequestBuilderTests.swift

## 任务 1.5 Haptics 与动画封装（SwiftUI 友好）
- 工时：2h | 依赖：无
- 验收：
  - Haptics.light() 封装 UIImpactFeedbackGenerator
  - 指南：withAnimation 与 .animation(value:) 使用说明
- 产物：Utils/Haptics.swift, Docs/animations.md

## 任务 1.6 Telemetry 事件合同（无 PII）
- 工时：3h | 依赖：无
- 验收：
  - 绑定 TelemetryService；多值参数用 JSON 字符串
  - 事件：generate_click, generate_result, cancel_generate, remote_config_fetch, pantry_state_check, ui_performance
  - 单测：参数编码为 JSON 字符串
- 产物：Services/TelemetryEventBuilder.swift, Tests/TelemetryEventBuilderTests.swift

