# AI 食谱生成器 v2.4 — 任务索引（与 PRD v2.4 Enhanced 对齐）

本目录包含依据《PRD_AI_Recipe_Generator_v2.4_Enhanced_Frontend.md》拆分的任务清单，按阶段（P0/P1/P2）与模块组织。建议优先完成 P0（Quick-only + 架构基础），随后推进 P1，再规划 P2。

## 任务分组一览
- 01_P0_architecture_and_foundations.md — 架构与基础设施（P0）
- 02_P0_viewmodel_and_generation.md — ViewModel 与生成流程（P0）
- 03_P0_ui_quick_and_results.md — SwiftUI 快速模式 UI 与结果视图（P0）
- 04_P1_remote_config_and_custom_shell.md — 远程配置与 Custom 外壳（P1）
- 05_P2_full_custom_grouping_and_analytics.md — 完整 Custom、分组结果与分析（P2）
- 06_testing_and_quality.md — 测试、无障碍与质量保障（跨阶段）

## 阶段目标（与 PRD 8.1 一致）
- P0：Quick 模式 + 统一 ViewState + canGenerate + PantryStateProvider 适配器 + 生成取消
- P1：RemoteConfiguration Loader（Stub/本地 JSON + 24h 缓存）+ ConfigurationSummary 卡片 + Custom 外壳（天数+餐食）
- P2：Full Custom（每餐时间/菜品数）+ 分组结果 + 高级 a11y/Analytics 细化

## 里程碑检查（DoD）
- 每个任务含：验收标准、依赖、工时估算、产物路径
- 合并前必须通过对应测试（单元/UI/集成）与代码审查
- 文案键入 Localizable.strings；Telemetry 事件无 PII，参数使用 JSON 字符串

> 参考文档：/Users/<USER>/Desktop/ingredient-scanner/PRD_AI_Recipe_Generator_v2.4_Enhanced_Frontend.md

