# 03 — SwiftUI 快速模式 UI 与结果视图（P0）

与 PRD 章节：4.1/4.2/6.1/6.2、8.5（Quick 扁平模型）

## 任务 3.1 ModeSelector（SwiftUI Segmented Picker）
- 工时：3h | 依赖：02-2.4
- 验收：
  - Picker(.segmented) 切换 .quick/.custom
  - 无障碍 label/traits
  - 44pt 触控区域
- 产物：Views/Components/ModeSelector.swift, Tests/UI/ModeSelectorTests.swift

## 任务 3.2 GenerateButton（加载/取消/禁用）
- 工时：4h | 依赖：02-2.2/2.3
- 验收：
  - Button(.borderedProminent)；根据 canGenerate 启用/禁用
  - 加载时显示 ProgressView + 取消按钮
  - 文案：Quick = “快速生成晚餐”
- 产物：Views/Components/GenerateButton.swift, Tests/UI/GenerateButtonTests.swift

## 任务 3.3 ResultsView（扁平）
- 工时：6h | 依赖：02-2.3
- 验收：
  - Idle：引导文案（NSLocalizedString）
  - Loading：.redacted(.placeholder) + ProgressView；取消按钮
  - Loaded：List 或 ScrollView + LazyVStack 扁平卡片
  - Failed：错误图示 + 文案（本地化）+ 重试
- 产物：Views/Results/ResultsView.swift（含四态子视图）

## 任务 3.4 RecipeCard（SwiftUI）
- 工时：4h | 依赖：3.3
- 验收：
  - 展示标题、副标题、estimatedTime（可选）、图片
  - 有则展示 ingredientsFromPantry/additionalIngredients；无则隐藏
  - 无障碍 label/value/hint
- 产物：Views/Results/RecipeCard.swift, Tests/UI/RecipeCardTests.swift

