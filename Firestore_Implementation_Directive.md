
# **技术指令：Firestore 用户偏好同步**

**文件版本:** 1.0
**指令级别:** 最高优先级 (Mandatory)
**目标AI:** Cursor
**核心任务:** 实现一个健壮、可扩展、且严格遵循 Firebase 官方最佳实践的用户偏好设置同步系统。

---

## **Agent 1: 战略与合规总监**

本代理负责设定项目的核心理念、不可动摇的原则以及必须遵循的参考资料。

### **1.1. 核心开发理念**

#### **1.1.1. 核心业务目标 (Core Business Objective)**

**指令:** 本次任务的核心是**将用户 Profile 中的所有设置作为一个完整的对象 (`UserPreferences`) 上传并同步至 Firestore**。这背后有两大战略目的：

1.  **提升用户体验与粘性:** 实现用户数据的云端漫游。当用户更换手机或在多设备间使用时，只需重新登录，所有个性化的偏好设置（如过敏原、饮食禁忌、家庭规模等）都能被完整记忆和恢复，免去重复配置的烦恼。
2.  **奠定商业化基础:** 这是为未来**付费订阅模式 (Premium Subscription Model)** 做的关键准备。只有将用户的核心数据安全地存储在云端，我们才能在此之上构建需要服务器验证的付费功能（例如，高级饮食计划、家庭成员共享配置等）。

#### **1.1.2. 技术执行原则 (Technical Execution Principles)**

*   **官方文档是唯一真理:** 所有 Firebase 相关代码的实现，**必须**严格参照本文档 `1.2` 章节中列出的官方链接。任何偏离官方示例、风格或方法的代码，都将被无条件判定为不合格。
*   **结构优于捷径:** 优先考虑代码的可读性、可维护性和可扩展性。必须将数据操作逻辑封装在专门的服务中，严禁将其与视图(View)代码耦合。
*   **为未来设计:** 当前任务是实现用户偏好设置的同步 (MVP)。但整体架构设计必须预留清晰的接口，以便未来可以轻松地添加新功能（如收藏菜谱、上传图片历史等），而无需重构现有代码。

### **1.2. 强制性参考资料 (Source of Truth)**

在编写任何代码之前，必须完全理解并遵循以下官方文档。任何偏离官方指导的实现都将被视为不合规。

1.  **核心概念 (Core Concepts)**
    *   **官方文档首页:** [Cloud Firestore](https://firebase.google.com/docs/firestore)
        *   **指令:** 建立对 Firestore 的宏观理解。
    *   **数据模型:** [Data Model](https://firebase.google.com/docs/firestore/data-model)
        *   **指令:** **(关键)** 必须深刻理解 Firestore 的 `collection/document/subcollection` 结构。这是构建可扩展数据架构的基础。
    *   **支持的数据类型:** [Supported Data Types](https://firebase.google.com/docs/firestore/manage-data/data-types)
        *   **指令:** **(关键)** 确保 `UserPreferences` Swift 模型中使用的所有属性类型都与 Firestore 支持的类型完全对应。

2.  **基础操作 (Fundamental Operations)**
    *   **添加与管理数据:** [Add and Manage Data](https://firebase.google.com/docs/firestore/manage-data/add-data)
        *   **内容约束:** 必须使用 `setData(from: document)` 的 `Codable` 方式来创建或更新用户偏好文档。
    *   **读取数据:** [Get Data](https://firebase.google.com/docs/firestore/query-data/get-data)
        *   **内容约束:** 必须使用 `document.data(as: YourCodableType.self)` 的方式来解码文档。
    *   **查询数据:** [Query Data](https://firebase.google.com/docs/firestore/query-data/queries)
        *   **指令:** 了解如何构建简单和复合查询，为未来功能做准备。

3.  **Swift 最佳实践 (Swift Best Practices)**
    *   **Codable 数据映射:** [Model data with Codable](https://firebase.google.com/docs/firestore/solutions/swift-codable-data-mapping)
        *   **指令:** **(强制性)** 这是整个项目数据持久化的核心。严禁手动创建字典 `[String: Any]` 进行数据交换。

4.  **健壮性与安全 (Robustness & Security)**
    *   **离线数据持久化:** [Enable Offline Persistence](https://firebase.google.com/docs/firestore/manage-data/enable-offline)
        *   **指令:** 理解并信赖 iOS 上的默认离线缓存能力，无需编写复杂的网络状态判断逻辑。
    *   **错误处理:** [Handle Errors](https://firebase.google.com/docs/firestore/manage-data/transactions-and-batched-writes#handle_errors)
        *   **指令:** 所有数据库调用都必须包含在 `do-catch` 块中，并对错误进行日志记录。
    *   **安全规则:** [Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
        *   **指令:** **(安全红线)** 必须阅读并理解此文档，以完全领会 `3.1` 章节中提供的安全规则的重要性。

5.  **限制与配额 (Limits & Quotas)**
    *   **使用限制:** [Usage and Limits](https://firebase.google.com/docs/firestore/quotas)
        *   **指令:** 了解关键限制（如文档大小、写入速率），确保设计不会触及天花板。

---

## **Agent 2: 首席架构师**

本代理负责提供具体的、可执行的、高质量的代码实现蓝图。

### **2.1. 文件创建**

*   **路径:** `Services/`
*   **文件名:** `UserProfileService.swift`

### **2.2. 核心服务实现 (`UserProfileService.swift`)**

请严格按照以下结构和代码实现 `UserProfileService`。

```swift
import Foundation
import FirebaseFirestore
import FirebaseFirestoreSwift // 必须导入此库以使用 Codable 功能

/// 管理用户个人资料在 Cloud Firestore 中的所有数据交互。
/// 职责：创建、读取、更新用户偏好设置。
final class UserProfileService {
    
    /// 共享的单例实例，确保全局唯一。
    static let shared = UserProfileService()
    
    // 私有的构造函数，强制使用单例。
    private init() {}
    
    /// 指向 `users` 集合的引用，这是所有用户数据的根集合。
    private let usersCollection = Firestore.firestore().collection("users")
    
    /// 获取特定用户文档的引用。
    /// - Parameter userId: 用户的唯一ID (来自 Firebase Auth)。
    /// - Returns: 指向该用户文档的 DocumentReference。
    private func userDocument(userId: String) -> DocumentReference {
        return usersCollection.document(userId)
    }
    
    /// **【写操作核心】**
    /// 保存或更新用户的偏好设置到 Firestore。
    /// 此方法使用 `setData(from:)`，如果文档不存在则创建，如果存在则完全覆盖。
    /// - Parameters:
    ///   - preferences: 需要保存的 `UserPreferences` 对象。
    ///   - userId: 目标用户的ID。
    /// - Throws: 如果数据编码或写入失败，则抛出错误。
    func savePreferences(_ preferences: UserPreferences, for userId: String) async throws {
        var prefsToSave = preferences
        prefsToSave.userId = userId // 确保数据模型中的ID与文档ID一致
        
        do {
            try userDocument(userId: userId).setData(from: prefsToSave)
        } catch {
            print("ERROR: Failed to save user preferences to Firestore: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// **【读操作核心】**
    /// 从 Firestore 获取用户的偏好设置。
    /// - Parameter userId: 目标用户的ID。
    /// - Returns: 一个可选的 `UserPreferences` 对象。如果文档不存在，则返回 `nil`。
    /// - Throws: 如果网络请求或文档解码失败，则抛出错误。
    func fetchPreferences(for userId: String) async throws -> UserPreferences? {
        do {
            // 使用 `data(as:)` 将文档直接解码为我们的 Codable 结构体。
            return try await userDocument(userId: userId).getDocument(as: UserPreferences.self)
        } catch {
            print("ERROR: Failed to fetch or decode user preferences: \(error.localizedDescription)")
            throw error
        }
    }
}
```

### **2.3. 集成指令**

1.  **在登录流程中集成 (AuthenticationService.swift):**
    *   **位置:** 在 `signInWithApple`, `signInWithGoogle`, `signInWithEmail` 函数中，当 `Auth.auth().signIn(...)` 成功并获取到 `user` 对象后。
    *   **逻辑:** 必须调用 `UserProfileService` 来获取或创建用户偏好。建议封装一个私有函数来处理此逻辑，以避免代码重复。

    ```swift
    // 在 AuthenticationService.swift 中建议添加的私有函数
    private func fetchOrCreateUserProfile(for user: User) async throws -> UserPreferences {
        let userId = user.uid
        
        if let preferences = try await UserProfileService.shared.fetchPreferences(for: userId) {
            // 老用户：成功获取到偏好设置，直接返回。
            return preferences
        } else {
            // 新用户：未找到偏好设置，创建一份默认设置。
            var defaultPreferences = UserPreferences.default
            defaultPreferences.userId = userId
            
            // 将默认设置立即保存回 Firestore，为用户完成初始化。
            try await UserProfileService.shared.savePreferences(defaultPreferences, for: userId)
            
            return defaultPreferences
        }
    }
    ```

2.  **在设置修改流程中集成 (例如 `PreferencesEditView.swift`):**
    *   **位置:** 在用户修改了任何偏好设置并点击“保存”或“完成”按钮的事件处理函数中。
    *   **逻辑:** 必须调用 `UserProfileService.shared.savePreferences(...)`，将包含了最新修改的整个 `UserPreferences` 对象写回 Firestore。

---

## **Agent 3: 安全与扩展顾问**

本代理负责确保方案的安全性、未来的可扩展性以及提供清晰的验证标准。

### **3.1. 安全指令 (强制性配置)**

*   **任务:** 配置 Firestore 安全规则，确保用户只能读写自己的数据。
*   **执行者:** 项目所有者（您本人）。
*   **位置:** Firebase 控制台 -> Firestore Database -> 规则 (Rules) 标签页。
*   **必须使用的规则:**

    ```
    rules_version = '2';
    service cloud.firestore {
      match /databases/{database}/documents {
        // 匹配 users 集合中的任何文档
        match /users/{userId} {
          // 允许读和写操作，当且仅当请求者的认证UID与文档的ID完全匹配。
          allow read, write: if request.auth.uid == userId;
        }
      }
    }
    ```
*   **警告:** 如果不配置此规则，您的用户数据将处于不安全状态，这是一个严重的安全漏洞。

### **3.2. 扩展性接口设计 (未来规划)**

本架构已为未来功能预留了清晰的扩展路径。

*   **如何增加新的用户偏好 (例如“烹饪经验等级”):**
    1.  **唯一需要修改的文件:** `Models/UserPreferences.swift`。
    2.  **操作:** 只需在该结构体中添加一个新的属性，例如 `var cookingExperience: String = "beginner"`。
    3.  **结果:** 由于我们使用了 `Codable`，`UserProfileService` 的代码**完全不需要任何修改**，它会自动处理新字段的存取。

*   **如何增加完全不同的功能 (例如“收藏菜谱”):**
    1.  **架构模式:** 使用“子集合 (Subcollection)”。
    2.  **数据路径:** `users/{userID}/favoriteRecipes/{recipeID}`
    3.  **操作:** 未来可以创建一个新的服务，如 `RecipeService.swift`，专门用于在此子集合中进行增删改查，这与 `UserProfileService` 的职责完全分离，互不干扰。

### **3.3. 验证标准 (Definition of Done)**

任务完成的标志是以下所有场景均按预期工作：

1.  **新用户注册:** 登录成功后，在 Firebase 控制台的 `users` 集合下，能看到一个以该用户 `UserID` 命名的新文档，其内容为 `UserPreferences.default` 的默认值。
2.  **老用户登录:** 登录成功后，App 内显示的为其之前保存的偏好设置。
3.  **修改并保存:** 用户在设置页面修改偏好（如添加过敏项）并保存后，在控制台能立刻看到对应文档的数据被更新。
4.  **跨设备同步:** 在设备 A 上修改并保存后，在设备 B 上退出并重新登录，能看到在设备 A 上所做的修改。
5.  **离线操作:** 关闭网络，修改并保存设置。然后关闭 App。重新打开网络，再次打开 App 并登录，应能看到离线时所做的修改已被同步。

