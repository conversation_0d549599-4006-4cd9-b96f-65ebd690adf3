
# 优化后的三分类饮食限制清单

根据您的要求，我们保留 `Allergies & Intolerances`, `Dietary Restrictions`, 和 `Strict Exclusions` 三个分类，并由专家团队审核，将项目分配到最合适的列表中。我们建议您用此清单更新您代码中的`enum`内容。

---

### 1. Allergies & Intolerances (过敏与不耐受)

**说明:** 此列表专注于医疗和生理反应，即身体对特定物质的强制性、非自愿的负面反应。这是最需要被严格遵守的类别。

*   **Milk** (牛奶)
*   **Eggs** (鸡蛋)
*   **Fish** (鱼类)
*   **Crustacean Shellfish** (甲壳类水产)
*   **Tree Nuts** (坚果)
*   **Peanuts** (花生)
*   **Wheat** (小麦)
*   **Soybeans** (大豆)
*   **Sesame** (芝麻)
*   **Gluten** (麸质/面筋)
*   **Lactose** (乳糖)
*   **Sulfites** (亚硫酸盐)
*   **Corn** (玉米)
*   **Mustard** (芥末)
*   **Celery** (芹菜)

---

### 2. Dietary Restrictions (饮食方式与偏好)

**说明:** 此列表涵盖用户因生活方式、健康目标、宗教或道德原因而主动选择的饮食模式。

*   **Vegan** (纯素)
*   **Vegetarian** (素食)
*   **Lacto-Ovo Vegetarian** (蛋奶素)
*   **Pescatarian** (鱼素)
*   **Paleo** (旧石器饮食)
*   **Keto** (生酮饮食)
*   **Low-Carb** (低碳水)
*   **Low-Sodium** (低盐/低钠)
*   **Low-Fat** (低脂)
*   **Low-FODMAP** (低FODMAP)
*   **Diabetic-Friendly** (糖尿病友好)
*   **Halal** (清真)
*   **Kosher** (犹太洁食)

---

### 3. Strict Exclusions (特定排除的食材)

**说明:** 此列表主要针对用户因个人口味、喜好或特定原因不吃的具体食材。这更偏向于“不喜欢吃”而不是“不能吃”。

*   **Red Meat** (红肉)
*   **Pork** (猪肉)
*   **Beef** (牛肉)
*   **Lamb** (羊肉)
*   **Poultry** (禽肉)
*   **Offal / Organ Meats** (内脏)
*   **Game Meats** (野味)
*   **Mushrooms** (蘑菇)
*   **Onions** (洋葱)
*   **Garlic** (大蒜)
*   **Cilantro / Coriander** (香菜)
*   **Spicy Food** (辛辣食物)
*   **Alcohol** (酒精)
*   **Caffeine** (咖啡因)
*   **Artificial Sweeteners** (人工甜味剂)
*   **[+] Add Custom Exclusion** (添加自定义排除项) - *建议在UI上保留此功能*
