# Account Settings Task Breakdown

## 概览
- **范围依据**: `v10.1/account-settings-prd.md`
- **实现指南**: `v10.1/README.md`
- **平台规范**: [Firebase Auth iOS Manage Users](https://firebase.google.com/docs/auth/ios/manage-users)
- **目标周期**: 5 个工作日 (约 40 小时)，与 README 阶段划分一致

---

## Phase 0 – 配置核对与前置准备 (Day 0，~5h)

### Task 0.1 Firebase & reCAPTCHA 配置巡检
- **优先级**: 🔴 Critical  
- **负责人**: iOS 开发 + 后端/DevOps  
- **耗时**: 2h  
- **验收**:  
  - [ ] Firebase Console 中已上传 APNs Auth Key，并与生产证书匹配  
  - [x] Xcode `Info.plist` 包含所有 `com.googleusercontent.apps.*` URL Schemes  
  - [x] `GoogleService-Info.plist` 的 `GOOGLE_APP_ID`、`BUNDLE_ID` 与当前 target 一致  
  - [ ] reCAPTCHA / DeviceCheck / SafetyNet 设置经安全团队确认  
  - [ ] 真实设备登录测试通过（Email、Apple、Google）

> ⚠️ 额外跟进：`Application/IngredientScanner.entitlements` 当前为空，请在 Phase 1 前确认 Sign in with Apple 能力已启用；同时与 DevOps/安全团队确认 APNs Auth Key 与 reCAPTCHA 配置。

### Task 0.2 代码基线审计与依赖梳理
- **优先级**: 🟡 High  
- **负责人**: iOS 开发  
- **耗时**: 3h  
- **验收**:  
  - [x] 梳理 `AuthenticationService` 现有 API 与错误处理  
  - [x] 确认 `PreferencesEditView.signOutSection` 移除影响面  
  - [x] 盘点所有需要在 Reset 中清理的服务：`SwiftDataStorageService`、`FavoritesStore`、`PlanStore`、`HistoryStorageService`、`DiskStorageManager`、Telemetry/UserDefaults、Firestore Quick History/Favorites  
  - [x] 列出需要新增/扩展的依赖（ReauthenticationCoordinator、AccountResetManager 等）

---

## Phase 1 – Firebase Auth 合规层实现 (Day 1，~9h)

### Task 1.1 AuthenticationService API 对齐
- **优先级**: 🔴 Critical  
- **耗时**: 4h  
- **验收**:  
  - [x] 新增或更新以下 async API，并调用 `user.reload(completion:)` 同步本地状态：`updateUserProfile`、`updateUserEmail`、`updateUserPassword`（`Services/AuthenticationService.swift`）  
  - [x] 实现 `sendEmailVerification`、`sendPasswordReset`，发送前调用 `Auth.auth().useAppLanguage()` 或设置 `languageCode`（`Services/AuthenticationService.swift`）  
  - [x] 实现 `deleteUser`、`reauthenticateUser`、`reloadUser`、`signOut`，全部返回结构化 `AuthError`（`Services/AuthenticationService.swift`）  
  - [x] 封装 `EmailAuthProvider.credential`、`OAuthProvider.credential` 生成逻辑以支持重新认证（`Services/AuthenticationService.swift`）

### Task 1.2 统一错误码与安全操作包装
- **优先级**: 🔴 Critical  
- **耗时**: 2h  
- **验收**:  
  - [x] `AuthError` 枚举支持 `requiresRecentLogin`、`emailAlreadyInUse`、`weakPassword`、`userNotFound` 等（`Services/AuthenticationService.swift`）  
  - [x] 提供 `performSecureOperation` 包装器，遇到 `requiresRecentLogin` 时触发重新认证并自动重试一次（`Services/AuthenticationService.swift`）  
  - [x] 出错时保证原始 `NSError` 被记录以便调试（`Services/AuthenticationService.swift`）

### Task 1.3 ReauthenticationCoordinator & UI
- **优先级**: 🟡 High  
- **耗时**: 3h  
- **验收**:  
  - [x] 支持 Email/Password、Apple、Google 三种重新认证路径（`Features/Profile/Reauthentication/ReauthenticationCoordinator.swift`）  
  - [x] 提供 SwiftUI `ReauthenticationView`，包含输入、loading、错误提示（`Features/Profile/Reauthentication/ReauthenticationView.swift`）  
  - [x] 对外暴露统一接口供 UI 调用（如 `.isPresented` 状态、结果回调）（`Features/Profile/Reauthentication/ReauthenticationCoordinator.swift`）

---

## Phase 2 – UI 与导航集成 (Day 2–3，~12h)

### Task 2.1 Profile 页面信息架构调整
- **优先级**: 🔴 Critical  
- **耗时**: 2h  
- **验收**:  
  - [x] `PreferencesEditView` 移除 `signOutSection`  
  - [x] Profile 页新增 `Account` 入口（符合无障碍要求，支持 Dynamic Type）  
  - [x] 匿名/未登录用户直接引导至 Sign In（遵循已确认决策 4）

### Task 2.2 ProfileAccountView 及动作列表
- **优先级**: 🔴 Critical  
- **耗时**: 4h  
- **验收**:  
  - [x] 显示邮箱 / displayName，未验证邮箱提示 `Verify Email`  
  - [x] 根据 `providerData` 进行 UI gating：只有包含 password provider 才显示“Change Password”，其他提供 `送重置邮件` 说明  
  - [x] 所有破坏性操作（Reset、Delete、Sign Out）使用 `.destructive` role，并二次确认  
  - [x] 与 `ReauthenticationCoordinator`、`AccountResetManager`、`AuthenticationService` 新 API 完整连通

### Task 2.3 子页面表单实现
- **优先级**: 🟡 High  
- **耗时**: 4h  
- **验收**:  
  - [x] `ChangePasswordView`：当前/新/确认密码校验 ≥ 6 位，弱密码错误提示  
  - [x] `UpdateEmailView`：提交后自动 `user.reload`，未验证状态提示发送验证邮件  
  - [x] `EditProfileView`：调用 `User.createProfileChangeRequest().commitChanges` 并过滤展示输入  
  - [x] 所有表单支持 loading 状态、错误回显、本地化字符串

### Task 2.4 Telemetry & Analytics 前端埋点
- **优先级**: 🟡 High  
- **耗时**: 2h  
- **验收**:  
  - [x] 触发 PRD 列出的事件 (`account_profile_updated` 等八个)  
  - [x] 参数包含：`provider`、`result`、`error_code`、`requires_recent_login`、`reauth_attempts`、`language_code`、`duration_ms`  
  - [ ] 与数据团队确认命名规范（已确认决策 3）

---

## Phase 3 – 数据重置与服务层 (Day 3–4，~9h)

### Task 3.1 AccountResetManager 实现
- **优先级**: 🔴 Critical  
- **耗时**: 4h  
- **验收**:  
  - [x] 统一串行执行 `reset()`/`clearAll()`，对每一步提供 log + 错误处理  
  - [x] 保持登录态不变，完成后返回成功提示  
  - [x] 支持 UI 进度状态、取消保护（执行期间禁止重复点击）

### Task 3.2 本地存储清理扩展
- **优先级**: 🔴 Critical  
- **耗时**: 3h  
- **验收**:  
  - [x] `SwiftDataStorageService.clearAllUserData()`  
  - [x] `FavoritesStore.clearAll()`、`PlanStore.clearAll()`、`HistoryStorageService.clearAllQuick()`、`DiskStorageManager.purgeFolder(name:)`  
  - [x] Telemetry/UserDefaults 重置默认值（如 `telemetry_opt_in`、`foodExpirationReminderEnabled`）

### Task 3.3 Firestore Quick History/Favorites 清理
- **优先级**: 🔴 Critical  
- **耗时**: 2h  
- **验收**:  
  - [x] 在 Reset 流程中调用后端接口删除 Firestore Quick History、Favorites（对应已确认决策 1）  
  - [x] 离线时记录待同步，联网后补偿执行  
  - [x] 删除结果写入日志，并纳入 telemetry（success/failure）

---

## Phase 4 – 测试、验证与发布准备 (Day 5，~7h)

### Task 4.1 自动化测试
- **优先级**: 🔴 Critical  
- **耗时**: 3h  
- **验收**:  
  - [ ] 单元测试：Auth 包装器、Reauth 重试逻辑、Reset Manager 序列  
  - [ ] 集成测试：Change Password / Update Email / Delete Account / Reset Account 全流程（含重新认证、离线场景）  
  - [ ] UI 测试：Account 入口、表单验证、确认对话框、Dynamic Type

### Task 4.2 QA 回归与 Checklist 覆盖
- **优先级**: 🟡 High  
- **耗时**: 3h  
- **验收**:  
  - [ ] 逐项执行 PRD QA 清单 (邮件用户、Apple/Google、匿名、离线、Telemetry、Sign Out)  
  - [ ] 记录缺陷并回归验证  
  - [ ] 输出发布说明与用户沟通要点（强调 Reset 为“仅清空本设备数据”）

### Task 4.3 文档与交付物更新
- **优先级**: 🟢 Medium  
- **耗时**: 1h  
- **验收**:  
  - [ ] 更新 `technical-implementation-guide.md`、`ui-implementation-guide.md`、`data-reset-implementation.md` 与 `testing-checklist.md`  
  - [ ] 完成最终代码审查清单、Telemetry 对接文档、Checklist 签字

---

## 风险与依赖跟踪
- **配置风险**：若 Task 0.1 未完成，后续 Change/Delete 流程可能因 reCAPTCHA 失败。=> 设立“阻断项”提示，未完成不得进入 Phase 1。
- **数据清理复杂度**：Task 3.2/3.3 中任一失败需清晰回滚策略，必要时采取“失败即停”并提示用户重试。
- **重新认证 UX**：Task 1.3 需在早期完成，以支持 Phase 2/4 的功能联调。
- **Telemetry 合规**：Task 2.4 必须在 Phase 4 前与数据团队复核，否则无法关卡。

---

## 时间概览
| 阶段 | 预计耗时 | 主要负责人 |
| --- | --- | --- |
| Phase 0 | 5h | iOS 开发 + 后端/DevOps |
| Phase 1 | 9h | iOS 开发 |
| Phase 2 | 12h | iOS 开发 + 设计/产品 (审稿) |
| Phase 3 | 9h | iOS 开发 + 后端 (Firestore) |
| Phase 4 | 7h | iOS 开发 + QA + 数据团队 |
| **总计** | **42h (~5 工作日)** |  |

---

## 交付清单
- ✅ 更新的 Profile/Account SwiftUI 视图（含所有表单、对话框、无障碍支持）
- ✅ 扩展的 `AuthenticationService` 及重新认证协调器
- ✅ `AccountResetManager` 与本地/云端数据清理实现
- ✅ Firebase Auth 对齐测试、Reset/Telemetry 集成测试报告
- ✅ Telemetry 事件 & 参数与分析团队对齐的确认记录
- ✅ 用户文案、本地化、多语言资源更新

完成以上任务，即可满足 PRD 与 README 所定义的功能、合规与质量标准。
