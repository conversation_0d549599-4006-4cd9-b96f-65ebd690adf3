# Data Reset Implementation Guide

## AccountResetManager 完整实现

基于PRD要求，实现统一的账户数据重置管理器，清理所有本地用户数据并恢复默认状态。

## 1. AccountResetManager 核心实现

### 1.1 主要管理器类
```swift
import Foundation
import SwiftUI
import FirebaseAuth

@MainActor
final class AccountResetManager: ObservableObject {
    static let shared = AccountResetManager()
    
    @Published var isResetting = false
    @Published var resetProgress: ResetProgress = .idle
    @Published var resetError: Error?
    
    private let authService: AuthenticationService
    private let userProfileService: UserProfileService
    
    enum ResetProgress {
        case idle
        case clearingSwiftData
        case clearingFavorites
        case clearingPlans
        case clearingHistory
        case clearingCache
        case clearingUserDefaults
        case restoringDefaults
        case syncingToCloud
        case completed
        
        var description: String {
            switch self {
            case .idle: return "Ready"
            case .clearingSwiftData: return "Clearing local ingredients..."
            case .clearingFavorites: return "Clearing favorites..."
            case .clearingPlans: return "Clearing meal plans..."
            case .clearingHistory: return "Clearing history..."
            case .clearingCache: return "Clearing cache..."
            case .clearingUserDefaults: return "Resetting preferences..."
            case .restoringDefaults: return "Restoring defaults..."
            case .syncingToCloud: return "Syncing to cloud..."
            case .completed: return "Reset complete"
            }
        }
        
        var progressValue: Double {
            switch self {
            case .idle: return 0.0
            case .clearingSwiftData: return 0.1
            case .clearingFavorites: return 0.2
            case .clearingPlans: return 0.3
            case .clearingHistory: return 0.4
            case .clearingCache: return 0.5
            case .clearingUserDefaults: return 0.6
            case .restoringDefaults: return 0.8
            case .syncingToCloud: return 0.9
            case .completed: return 1.0
            }
        }
    }
    
    private init() {
        self.authService = AuthenticationService.shared
        self.userProfileService = UserProfileService.shared
    }
    
    /// 执行完整的账户重置
    func resetAccount() async throws {
        guard !isResetting else { return }
        
        isResetting = true
        resetError = nil
        
        defer {
            isResetting = false
        }
        
        do {
            // 1. 清理SwiftData
            resetProgress = .clearingSwiftData
            try await clearSwiftData()
            
            // 2. 清理收藏
            resetProgress = .clearingFavorites
            try await clearFavorites()
            
            // 3. 清理计划
            resetProgress = .clearingPlans
            try await clearPlans()
            
            // 4. 清理历史
            resetProgress = .clearingHistory
            try await clearHistory()
            
            // 5. 清理缓存
            resetProgress = .clearingCache
            try await clearCache()
            
            // 6. 清理UserDefaults
            resetProgress = .clearingUserDefaults
            try await clearUserDefaults()
            
            // 7. 恢复默认偏好
            resetProgress = .restoringDefaults
            try await restoreDefaultPreferences()
            
            // 8. 同步到云端（如果在线）
            resetProgress = .syncingToCloud
            try await syncToCloud()
            
            resetProgress = .completed
            
            // 记录成功事件
            logResetSuccess()
            
        } catch {
            resetError = error
            resetProgress = .idle
            
            // 记录失败事件
            logResetFailure(error)
            
            throw error
        }
    }
}
```

### 1.2 各个清理步骤实现
```swift
extension AccountResetManager {
    
    /// 清理SwiftData中的所有用户数据
    private func clearSwiftData() async throws {
        let swiftDataService = SwiftDataStorageService.shared
        
        // 清理所有保存的食谱
        try await swiftDataService.clearAllRecipes()
        
        // 清理所有食材
        try await swiftDataService.clearAllIngredients()
        
        // 清理购物清单
        try await swiftDataService.clearShoppingList()
        
        // 清理历史记录
        try await swiftDataService.clearRecipeHistory()
        
        print("✅ SwiftData cleared successfully")
    }
    
    /// 清理收藏数据
    private func clearFavorites() async throws {
        let favoritesStore = FavoritesStore.shared
        
        // 清理收藏列表
        await favoritesStore.clearAll()
        
        print("✅ Favorites cleared successfully")
    }
    
    /// 清理餐食计划
    private func clearPlans() async throws {
        let planStore = PlanStore.shared
        
        // 清理所有计划
        await planStore.clearAll()
        
        print("✅ Meal plans cleared successfully")
    }
    
    /// 清理历史记录
    private func clearHistory() async throws {
        let historyService = HistoryStorageService.shared
        
        // 清理快速历史
        await historyService.clearAllQuick()
        
        print("✅ History cleared successfully")
    }
    
    /// 清理磁盘缓存
    private func clearCache() async throws {
        let diskManager = DiskStorageManager.shared
        
        // 清理图片缓存
        await diskManager.purgeFolder(name: "ImageCache")
        
        // 清理其他缓存文件夹
        await diskManager.purgeFolder(name: "QuickHistory")
        
        print("✅ Cache cleared successfully")
    }
    
    /// 清理UserDefaults中的用户设置
    private func clearUserDefaults() async throws {
        let defaults = UserDefaults.standard
        
        // 清理收藏相关
        defaults.removeObject(forKey: "favorites.recipes.v1")
        defaults.removeObject(forKey: "favorites.snapshots.v1")
        
        // 清理计划相关
        defaults.removeObject(forKey: "planstore.lastQuick.v1")
        defaults.removeObject(forKey: "planstore.lastMealPrep.v1")
        defaults.removeObject(forKey: "planstore.retentionNotice.v1")
        
        // 清理历史索引
        defaults.removeObject(forKey: "quick.history.index.v1")
        
        // 重置遥测设置
        defaults.removeObject(forKey: "telemetry_opt_in")
        defaults.removeObject(forKey: "foodExpirationReminderEnabled")
        defaults.removeObject(forKey: "expirationSuppressNoonDate")
        
        // 清理远程配置缓存
        defaults.removeObject(forKey: "remote_config_cache_v1")
        defaults.removeObject(forKey: "remote_config_timestamp_v1")
        
        // 保留测试相关的键（如果存在）
        // defaults.removeObject(forKey: "recipes.selectedTab") // 保留
        // defaults.removeObject(forKey: "UITEST_*") // 保留
        
        print("✅ UserDefaults cleared successfully")
    }
    
    /// 恢复默认用户偏好
    private func restoreDefaultPreferences() async throws {
        guard let userId = Auth.auth().currentUser?.uid else {
            throw ResetError.userNotFound
        }
        
        // 创建默认偏好
        let defaultPreferences = UserPreferences.createDefault(for: userId)
        
        // 更新到AuthenticationService
        await authService.updatePreferences(defaultPreferences)
        
        // 保存到本地SwiftData
        try await SwiftDataStorageService.shared.saveUserPreferences(defaultPreferences)
        
        print("✅ Default preferences restored successfully")
    }
    
    /// 同步到云端
    private func syncToCloud() async throws {
        guard let userId = Auth.auth().currentUser?.uid else {
            throw ResetError.userNotFound
        }
        
        // 如果在线，同步默认偏好到Firestore
        if OptimizedNetworkService.shared.isOnline {
            let defaultPreferences = UserPreferences.createDefault(for: userId)
            try await userProfileService.savePreferences(defaultPreferences, for: userId)
            print("✅ Synced to cloud successfully")
        } else {
            print("⚠️ Offline - will sync when connection restored")
        }
    }
}
```

## 2. 存储服务扩展

### 2.1 FavoritesStore 清理方法
```swift
extension FavoritesStore {
    
    /// 清理所有收藏数据
    func clearAll() {
        defaults.removeObject(forKey: key)
        defaults.removeObject(forKey: snapshotKey)
        print("✅ FavoritesStore cleared")
    }
}
```

### 2.2 PlanStore 清理方法
```swift
extension PlanStore {
    
    /// 清理所有计划数据
    func clearAll() {
        // 清理快速计划
        defaults.removeObject(forKey: "planstore.lastQuick.v1")
        
        // 清理餐食准备计划
        defaults.removeObject(forKey: "planstore.lastMealPrep.v1")
        
        // 清理保留通知
        defaults.removeObject(forKey: "planstore.retentionNotice.v1")
        
        print("✅ PlanStore cleared")
    }
}
```

### 2.3 HistoryStorageService 清理方法
```swift
extension HistoryStorageService {
    
    /// 清理所有快速历史数据
    public func clearAllQuick() {
        // 清理内存缓存
        quickCache.removeAll()
        cacheLoaded = false
        
        // 清理索引
        defaults.removeObject(forKey: StorageConfiguration.quickHistoryIndexKey)
        
        // 清理磁盘文件
        queue.async { [weak self] in
            self?.disk.purgeFolder(name: StorageConfiguration.quickHistoryFolderName)
        }
        
        print("✅ Quick history cleared")
    }
}
```

### 2.4 DiskStorageManager 清理方法
```swift
extension DiskStorageManager {
    
    /// 清空指定文件夹
    public func purgeFolder(name: String) {
        let folderURL = baseDir.appendingPathComponent(name, isDirectory: true)
        
        do {
            if fm.fileExists(atPath: folderURL.path) {
                try fm.removeItem(at: folderURL)
                print("✅ Purged folder: \(name)")
            }
        } catch {
            print("❌ Failed to purge folder \(name): \(error)")
        }
    }
    
    /// 清理所有用户数据文件夹
    public func purgeAllUserData() {
        purgeFolder(name: StorageConfiguration.quickHistoryFolderName)
        purgeFolder(name: StorageConfiguration.imageCacheFolderName)
        // 添加其他需要清理的文件夹
    }
}
```

### 2.5 SwiftDataStorageService 清理方法
```swift
extension SwiftDataStorageService {
    
    /// 清理所有用户数据
    func clearAllUserData() async throws {
        guard let context = modelContext else {
            throw StorageError.contextNotAvailable
        }
        
        // 清理所有保存的食谱
        try await clearAllRecipes()
        
        // 清理所有食材
        try await clearAllIngredients()
        
        // 清理购物清单
        try await clearShoppingList()
        
        // 清理历史记录
        try await clearRecipeHistory()
        
        // 清理用户偏好（将在restoreDefaultPreferences中重新创建）
        let existingPrefs = try fetchUserPreferences()
        if let existing = existingPrefs {
            context.delete(existing)
        }
        
        try context.save()
        print("✅ All SwiftData user data cleared")
    }
    
    private func clearAllRecipes() async throws {
        guard let context = modelContext else { return }
        
        let descriptor = FetchDescriptor<SavedRecipe>()
        let recipes = try context.fetch(descriptor)
        
        for recipe in recipes {
            context.delete(recipe)
        }
    }
    
    private func clearAllIngredients() async throws {
        guard let context = modelContext else { return }
        
        let descriptor = FetchDescriptor<SavedIngredient>()
        let ingredients = try context.fetch(descriptor)
        
        for ingredient in ingredients {
            context.delete(ingredient)
        }
    }
    
    private func clearShoppingList() async throws {
        guard let context = modelContext else { return }
        
        let descriptor = FetchDescriptor<ShoppingListItem>()
        let items = try context.fetch(descriptor)
        
        for item in items {
            context.delete(item)
        }
    }
    
    private func clearRecipeHistory() async throws {
        guard let context = modelContext else { return }
        
        let descriptor = FetchDescriptor<RecipeHistory>()
        let history = try context.fetch(descriptor)
        
        for record in history {
            context.delete(record)
        }
    }
}
```

## 3. 错误处理和日志

### 3.1 重置错误类型
```swift
enum ResetError: LocalizedError {
    case userNotFound
    case swiftDataError(Error)
    case networkError(Error)
    case partialFailure([String])
    
    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "User not found"
        case .swiftDataError(let error):
            return "Database error: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .partialFailure(let failures):
            return "Some operations failed: \(failures.joined(separator: ", "))"
        }
    }
}
```

### 3.2 分析事件记录
```swift
extension AccountResetManager {
    
    private func logResetSuccess() {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        // 记录成功事件
        Analytics.logEvent("account_reset_confirmed", parameters: [
            "user_id": userId,
            "result": "success",
            "duration_ms": Int(Date().timeIntervalSince1970 * 1000),
            "steps_completed": 8
        ])
    }
    
    private func logResetFailure(_ error: Error) {
        guard let userId = Auth.auth().currentUser?.uid else { return }
        
        // 记录失败事件
        Analytics.logEvent("account_reset_confirmed", parameters: [
            "user_id": userId,
            "result": "failure",
            "error_code": String(describing: error),
            "failed_at_step": resetProgress.description
        ])
    }
}
```

## 4. UI 集成

### 4.1 重置进度视图
```swift
struct AccountResetProgressView: View {
    @ObservedObject var resetManager: AccountResetManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 24) {
            Text("Resetting Account")
                .font(.title2.weight(.semibold))
            
            VStack(spacing: 16) {
                ProgressView(value: resetManager.resetProgress.progressValue)
                    .progressViewStyle(LinearProgressViewStyle())
                
                Text(resetManager.resetProgress.description)
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
            }
            
            if resetManager.resetProgress == .completed {
                Button("Done") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .interactiveDismissDisabled(resetManager.isResetting)
    }
}
```

这个实现提供了完整的数据重置功能，确保所有用户数据都被正确清理，同时保持账户登录状态。每个步骤都有详细的日志记录和错误处理。
