# UI Implementation Guide

## 账户管理界面完整实现

基于PRD要求，实现Profile页面中的Account区域和所有相关的账户操作界面。

## 1. ProfileAccountView - 主账户管理页面

### 1.1 核心视图结构
```swift
import SwiftUI
import FirebaseAuth

struct ProfileAccountView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss
    
    @StateObject private var resetManager = AccountResetManager.shared
    @StateObject private var reauthCoordinator = ReauthenticationCoordinator(authService: AuthenticationService.shared)
    
    @State private var showingDeleteConfirmation = false
    @State private var showingResetConfirmation = false
    @State private var showingChangePassword = false
    @State private var showingUpdateEmail = false
    @State private var showingEditProfile = false
    
    var body: some View {
        NavigationView {
            List {
                // 账户信息头部
                accountHeaderSection
                
                // 账户信息操作
                accountInfoSection
                
                // 安全和会话操作
                securitySection
            }
            .navigationTitle("Account")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") { dismiss() }
                }
            }
        }
        .sheet(isPresented: $showingChangePassword) {
            ChangePasswordView()
        }
        .sheet(isPresented: $showingUpdateEmail) {
            UpdateEmailView()
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .sheet(isPresented: $reauthCoordinator.isPresented) {
            ReauthenticationView(coordinator: reauthCoordinator)
        }
        .confirmationDialog("Reset Account", isPresented: $showingResetConfirmation) {
            Button("Reset Account", role: .destructive) {
                Task {
                    try await resetManager.resetAccount()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This will clear all data stored on this device only. Your Firebase account remains active and you'll stay signed in.")
        }
        .confirmationDialog("Delete Account", isPresented: $showingDeleteConfirmation) {
            Button("Delete Account", role: .destructive) {
                Task {
                    await deleteAccount()
                }
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This will permanently delete your account and all associated data. This action cannot be undone.")
        }
    }
}
```

### 1.2 视图组件实现
```swift
extension ProfileAccountView {
    
    @ViewBuilder
    private var accountHeaderSection: some View {
        Section {
            HStack(spacing: 16) {
                // 用户头像
                AsyncImage(url: authService.currentUser?.photoURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 40))
                        .foregroundStyle(.blue.gradient)
                }
                .frame(width: 60, height: 60)
                .clipShape(Circle())
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Signed in as")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Text(userDisplayText)
                        .font(.headline)
                        .foregroundStyle(.primary)
                    
                    if let user = authService.currentUser {
                        HStack(spacing: 4) {
                            Circle()
                                .fill(user.isEmailVerified ? .green : .orange)
                                .frame(width: 8, height: 8)
                            
                            Text(user.isEmailVerified ? "Verified" : "Unverified")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 8)
        }
    }
    
    @ViewBuilder
    private var accountInfoSection: some View {
        Section("Account Information") {
            AccountActionRow(
                icon: "person.circle",
                title: "Edit Profile",
                subtitle: "Update display name and photo"
            ) {
                showingEditProfile = true
            }
            
            AccountActionRow(
                icon: "envelope",
                title: "Update Email",
                subtitle: currentUserEmail ?? "No email on file"
            ) {
                showingUpdateEmail = true
            }
            
            if let user = authService.currentUser, !user.isEmailVerified {
                AccountActionRow(
                    icon: "checkmark.seal",
                    title: "Verify Email",
                    subtitle: "Send verification email"
                ) {
                    Task {
                        await sendEmailVerification()
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private var securitySection: some View {
        Section("Security & Data") {
            if userCapabilities.canChangePassword {
                AccountActionRow(
                    icon: "key",
                    title: "Change Password",
                    subtitle: "Update your password"
                ) {
                    showingChangePassword = true
                }
            } else {
                AccountActionRow(
                    icon: "key",
                    title: "Reset Password",
                    subtitle: "Send password reset email"
                ) {
                    Task {
                        await sendPasswordReset()
                    }
                }
            }
            
            AccountActionRow(
                icon: "arrow.clockwise",
                title: "Reset Account",
                subtitle: "Clear all data on this device",
                isDestructive: false
            ) {
                showingResetConfirmation = true
            }
            
            AccountActionRow(
                icon: "trash",
                title: "Delete Account",
                subtitle: "Permanently delete your account",
                isDestructive: true
            ) {
                showingDeleteConfirmation = true
            }
            
            AccountActionRow(
                icon: "rectangle.portrait.and.arrow.right",
                title: "Sign Out",
                subtitle: "Sign out of your account",
                isDestructive: false
            ) {
                signOut()
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var userDisplayText: String {
        if let user = authService.currentUser {
            if let displayName = user.displayName, !displayName.isEmpty {
                return displayName
            } else if let email = user.email {
                return email
            }
        }
        return "Unknown User"
    }
    
    private var currentUserEmail: String? {
        return authService.currentUser?.email
    }
    
    private var userCapabilities: AuthenticationService.UserCapabilities {
        return authService.getUserCapabilities() ?? AuthenticationService.UserCapabilities(
            canChangePassword: false,
            canUpdateEmail: false,
            canUpdateProfile: false,
            requiresReauth: false,
            providerName: "Unknown"
        )
    }
}
```

## 2. AccountActionRow - 可复用的操作行组件

```swift
struct AccountActionRow: View {
    let icon: String
    let title: String
    let subtitle: String
    let isDestructive: Bool
    let action: () -> Void
    
    init(
        icon: String,
        title: String,
        subtitle: String,
        isDestructive: Bool = false,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.isDestructive = isDestructive
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(isDestructive ? .red : .blue)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .foregroundStyle(isDestructive ? .red : .primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundStyle(.tertiary)
            }
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}
```

## 3. ChangePasswordView - 修改密码页面

```swift
struct ChangePasswordView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    SecureField("Current Password", text: $currentPassword)
                        .textContentType(.password)
                    
                    SecureField("New Password", text: $newPassword)
                        .textContentType(.newPassword)
                    
                    SecureField("Confirm New Password", text: $confirmPassword)
                        .textContentType(.newPassword)
                } header: {
                    Text("Password Change")
                } footer: {
                    Text("Your new password must be at least 6 characters long.")
                }
                
                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundStyle(.red)
                            .font(.caption)
                    }
                }
            }
            .navigationTitle("Change Password")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        Task {
                            await changePassword()
                        }
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("Password Changed", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your password has been successfully updated.")
        }
    }
    
    private var isFormValid: Bool {
        !currentPassword.isEmpty &&
        !newPassword.isEmpty &&
        newPassword.count >= 6 &&
        newPassword == confirmPassword
    }
    
    private func changePassword() async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await authService.safeUpdatePassword(to: newPassword)
            showingSuccess = true
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
}
```

## 4. UpdateEmailView - 更新邮箱页面

```swift
struct UpdateEmailView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss
    
    @State private var newEmail = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("New Email Address", text: $newEmail)
                        .textContentType(.emailAddress)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                } header: {
                    Text("Email Address")
                } footer: {
                    Text("You'll need to verify your new email address after updating.")
                }
                
                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundStyle(.red)
                            .font(.caption)
                    }
                }
            }
            .navigationTitle("Update Email")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Update") {
                        Task {
                            await updateEmail()
                        }
                    }
                    .disabled(!isFormValid || isLoading)
                }
            }
        }
        .alert("Email Updated", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your email has been updated. Please check your new email for verification.")
        }
    }
    
    private var isFormValid: Bool {
        !newEmail.isEmpty && newEmail.contains("@")
    }
    
    private func updateEmail() async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await authService.safeUpdateEmail(to: newEmail)
            showingSuccess = true
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
}
```

## 5. EditProfileView - 编辑资料页面

```swift
struct EditProfileView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss
    
    @State private var displayName = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("Display Name", text: $displayName)
                        .textContentType(.name)
                } header: {
                    Text("Profile Information")
                } footer: {
                    Text("This name will be visible to other users.")
                }
                
                if let errorMessage = errorMessage {
                    Section {
                        Text(errorMessage)
                            .foregroundStyle(.red)
                            .font(.caption)
                    }
                }
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        Task {
                            await updateProfile()
                        }
                    }
                    .disabled(displayName.isEmpty || isLoading)
                }
            }
        }
        .onAppear {
            displayName = authService.currentUser?.displayName ?? ""
        }
        .alert("Profile Updated", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your profile has been successfully updated.")
        }
    }
    
    private func updateProfile() async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await authService.updateUserProfile(displayName: displayName, photoURL: nil)
            showingSuccess = true
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
}
```

## 6. Profile页面集成

### 6.1 更新ProfileView
```swift
// 在ProfileView中添加Account入口
extension ProfileView {
    
    @ViewBuilder
    private var authenticatedView: some View {
        VStack(spacing: 0) {
            // 现有的Preferences内容
            PreferencesEditView()
            
            // 新增Account入口
            accountSection
        }
    }
    
    @ViewBuilder
    private var accountSection: some View {
        VStack(spacing: 16) {
            Divider()
            
            Button {
                showingAccountView = true
            } label: {
                HStack {
                    Image(systemName: "person.circle")
                        .font(.title2)
                        .foregroundStyle(.blue)
                    
                    VStack(alignment: .leading) {
                        Text("Account")
                            .font(.headline)
                        Text("Manage your account settings")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundStyle(.tertiary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(.plain)
        }
        .padding(.horizontal)
        .sheet(isPresented: $showingAccountView) {
            ProfileAccountView()
        }
    }
}
```

这个UI实现指南提供了完整的账户管理界面，包括所有必要的表单、确认对话框和错误处理。每个视图都遵循iOS设计规范，支持无障碍功能和本地化。
