# V10.1 Account Settings Implementation Guide

## Overview
This guide defines the engineering plan for delivering the Account Settings overhaul described in `v10.1/account-settings-prd.md`. The scope covers consolidating account actions inside the Profile screen, adding Change Password/Delete Account/Reset Account flows, and aligning all Firebase Auth usage with the iOS Manage Users guidance.

## File Structure
```
v10.1/
├── README.md                     # Implementation guide (this document)
├── account-settings-prd.md       # Product requirements document
├── task-breakdown.md             # Detailed task breakdown and timeline
├── technical-implementation-guide.md  # Firebase Auth API implementation
├── data-reset-implementation.md  # AccountResetManager implementation
├── ui-implementation-guide.md    # SwiftUI views implementation
└── testing-checklist.md          # QA testing and acceptance criteria
```

## Prerequisites & Dependencies
- Confirm Firebase Auth is initialised on iOS with the latest SDK and the app targets iOS 17+ (matching the existing codebase assumptions).
- Reconcile Info.plist and Firebase Console configuration **before** coding:
  - Upload APNs Auth Key for push/reCAPTCHA challenges.
  - Ensure URL Schemes (`com.googleusercontent.apps.*`) and custom URL types are present for OAuth callbacks.
  - Verify any `FirebaseAppDelegateProxyEnabled` or `GOOGLE_APP_ID` entries match the current bundle IDs.
- Align with security on reCAPTCHA: ensure device checks and SafetyNet/DeviceCheck toggles in Console are handled per security review.
- Inventory local data stores that must participate in Reset Account: `SwiftDataStorageService`, `FavoritesStore`, `PlanStore`, `HistoryStorageService`, `DiskStorageManager`, telemetry flags, and any new cache keys.

## Implementation Approach (5 Phases)

1. **Setup & Audit (Day 0)**
   - Review existing `AuthenticationService`, `Profile` views, and dependency registrations.
   - Draft the reusable ReauthenticateCoordinator (password + Apple/Google flows) and define its API surface.
   - Capture a configuration checklist run-through (APNs key, URL schemes, entitlements).

2. **Firebase Auth Compliance Layer (Day 1)**
   - Implement Swift wrappers that mirror `Auth.auth().addStateDidChangeListener`, `User.createProfileChangeRequest().commitChanges`, `user.updateEmail(to:)`, `user.sendEmailVerification`, `user.updatePassword(to:)`, `Auth.auth().sendPasswordReset(withEmail:)`, `user.reauthenticate(with:)`, `user.delete(completion:)`, `Auth.auth().signOut()`, and `user.reload(completion:)`.
   - Emit typed errors preserving `AuthErrorCode` for UI gating.
   - Add integration tests or lightweight harness to exercise reauth retry logic.

3. **UI & Navigation Integration (Day 2–3)**
   - Remove the legacy `signOutSection` from `PreferencesEditView` and insert the Account module entry point inside Profile.
   - Build SwiftUI components: `ProfileAccountView`, change password forms, delete account confirmation, reset account confirmation modal.
   - Inject the ReauthenticateCoordinator where `AuthErrorCode.requiresRecentLogin` may occur.

4. **Data Reset & Local Services (Day 3–4)**
   - Implement `AccountResetManager` orchestrating sequential calls to `reset()`/`clearAll()` across services (including new Firestore Quick History/Favorites deletion).
   - Provide transactional logging and user feedback for each step; surface non-blocking failures with retry guidance.

5. **Validation & Release (Day 5)**
   - Run the QA checklist from the PRD (email/password, Apple, Google, anonymous, offline scenarios, telemetry).
   - Confirm copy, localization placeholders, and analytics instrumentation cross teams (Product, QA, Data).

## Key Code Touchpoints
- `Features/Profile/PreferencesEditView.swift` – remove sign-out block, add navigation to Account view.
- New `Features/Profile/Account` module – `ProfileAccountView`, `AccountActionRow`, `AccountActionDetail` screens.
- `Services/AuthenticationService.swift` – expose Swift-friendly wrappers for Firebase Auth methods and centralize error handling, language configuration, and reauth pipeline.
- `AccountResetManager` (new) – orchestrate resets across `SwiftDataStorageService`, `FavoritesStore`, `PlanStore`, `HistoryStorageService`, `DiskStorageManager`, telemetry/user defaults, and Firestore cleanup.
- Reauth UI – shared SwiftUI component for password re-auth (email credential) and OAuth re-auth (Apple/Google) integrated via `ASAuthorizationController` or `GIDSignIn`.
- Analytics pipeline – extend telemetry events (`account_*`) with provider/result/error metadata.

## UX & Interaction Guidelines
- Account header mirrors design specs: icon + `Signed in as {email}` or display name fallback.
- Anonymous users cannot reach the Account module (app enforces sign-in up front).
- Provide explicit copy in Reset Account: “This clears data stored on **this device** only; your Firebase account remains active.”
- Change Password form: current + new + confirm fields, inline validation, disabled Save until valid.
- Delete Account confirmation: multi-step dialog with provider-specific reauth prompt and destructive styling (`role: .destructive`).
- Localize all strings (Base + zh-Hans) and support Dynamic Type.

## Data Handling Requirements
- Always wait for `Auth.auth().addStateDidChangeListener` to report non-nil user before presenting data.
- After mutations (`updateEmail`, `updateProfile`, `updatePassword`), call `user.reload(completion:)` to sync local cache.
- Use `Auth.auth().useAppLanguage()` or explicit `languageCode` before sending verification/reset emails.
- Reset Account flow must clear:
  - SwiftData ingredients and preferences to default via `AuthenticationService.updatePreferences(.createDefault(for: uid))`.
  - Favorites (`favorites.recipes.v1`, `favorites.snapshots.v1`).
  - Plan store (`planstore.*`).
  - History quick files (`QuickHistory/` folder) and index keys.
  - Remote config cache, telemetry flags, and device-level caches.
  - Firestore Quick History/Favorites documents once API available.
- Delete Account flow: after `user.delete`, ensure local reset runs and navigation returns to unauthenticated state.

## Telemetry & Logging
- Events (`account_profile_updated`, `account_email_updated`, `account_email_verification_sent`, `account_password_reset_email_sent`, `account_change_password_success`, `account_delete_confirmed`, `account_reset_confirmed`, `account_sign_out`).
- Include parameters: `provider`, `duration_ms`, `result`, `error_code`, `requires_recent_login`, `reauth_attempts`, `language_code`.
- Log reset steps and reauth attempts with anonymized context for debugging.

## Testing & Validation Checklist
- ✅ Unit: AuthenticationService wrappers, reauth retry logic, AccountResetManager sequencing (prefer dependency injection for stores).
- ✅ Integration: End-to-end change password, update email (including reauth), delete account (with reauth), reset account (local + Firestore cleanup).
- ✅ UI: Dynamic Type coverage, destructive coloration, copy verification, localization placeholders.
- ✅ Offline: Disable Firebase-dependent buttons when network unavailable; ensure reset account still completes.
- ✅ Regression: Sign-out workflow remains intact; telemetry events are emitted when opt-in.

## Risks & Mitigations
- **Configuration drift** – Mitigate with pre-flight checklist (APNs key, URL schemes, reCAPTCHA settings) stored in engineering doc.
- **Reauth UX fragmentation** – Use shared coordinator/component and consistent error copy.
- **Data cleanup gaps** – Implement `reset()` contracts and cover with integration tests & logging.
- **User misunderstanding of Reset** – Reinforce “device-only” messaging and evaluate renaming during design review.

## Deliverables
- Updated Profile and Account SwiftUI views with integrated actions.
- Extended `AuthenticationService` and reauth coordinator components.
- `AccountResetManager` and reset methods for all stores (including Firestore cleanup).
- Updated telemetry instrumentation and localization entries.
- QA checklist artifacts and release notes reflecting new account capabilities.

## Quick Start Guide

### For Developers
1. **Start with** `task-breakdown.md` - 了解完整的任务分解和时间规划
2. **Technical Implementation** - 参考 `technical-implementation-guide.md` 严格按照Firebase Auth iOS文档实现
3. **Data Management** - 使用 `data-reset-implementation.md` 实现AccountResetManager
4. **UI Development** - 按照 `ui-implementation-guide.md` 创建所有界面
5. **Testing** - 使用 `testing-checklist.md` 进行完整的QA验收

### For QA/Testing
- 直接使用 `testing-checklist.md` 进行验收测试
- 确保所有测试用例都通过才能发布

### For Product/Design
- 参考 `account-settings-prd.md` 了解完整的产品需求
- 使用 `ui-implementation-guide.md` 验证界面设计实现

Follow this guide alongside the PRD and Firebase Auth iOS Manage Users documentation to deliver the Account Settings experience safely and maintainably.
