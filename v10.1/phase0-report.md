# Phase 0 – Account Settings Preflight

Date: 2025-09-16
Owner: Codex Agent

## Summary
- Reviewed Firebase configuration artifacts and highlighted any gaps ahead of the Account module work.
- Audited the current authentication and data services to understand the impact of removing `signOutSection` and to scope the Account reset requirements.
- Identified missing Firebase Auth compliance points that must be addressed before entering Phase 1.

## Task 0.1 Firebase & reCAPTCHA 配置巡检

| Checklist Item | Status | Notes |
| --- | --- | --- |
| Firebase Console APNs Auth Key uploaded & matched | ⚠️ Blocked | Requires console access; request confirmation from DevOps before Phase 1. |
| `com.googleusercontent.apps.*` URL Schemes present in Info.plist | ✅ | Confirmed in `Application/Info.plist`. |
| `GoogleService-Info.plist` `GOOGLE_APP_ID` & `BUNDLE_ID` match target | ✅ | Matches `com.kuo.ingredientscannertemp` in `Application/GoogleService-Info.plist`. |
| reCAPTCHA / DeviceCheck / SafetyNet reviewed | ⚠️ Blocked | Needs security team confirmation; add to kickoff checklist. |
| Real device login smoke (Email/Apple/Google) | ⚠️ Pending | Schedule QA run once APNs/reCAPTCHA checklist clears. |

Additional observation: `Application/IngredientScanner.entitlements` is currently empty. Confirm Sign in with Apple capability is attached to the build target before shipping account deletion flows.

## Task 0.2 代码基线审计与依赖梳理

### AuthenticationService Audit
- Auth state monitoring already uses `Auth.auth().addStateDidChangeListener` and updates `currentUser` (`Services/AuthenticationService.swift`).
- Missing Firebase Auth wrappers that Phase 1 must implement: `updateUserEmail`, `updateUserPassword`, `sendEmailVerification`, `reauthenticateUser`, `deleteUser`, explicit `reloadUser`, and structured error surfaces (no `requiresRecentLogin` case yet).
- Password reset helper exists but does not call `Auth.auth().useAppLanguage()` prior to `sendPasswordReset`; add localization setup.
- Error mapping currently omits `requiresRecentLogin` and other codes required by the PRD.

### PreferencesEditView Impact
- `signOutSection` still lives in `Features/Profile/PreferencesEditView.swift`. Removing it will affect sign-out confirmation alerts and the network status badge currently rendered there. Capture replacements inside the new Account module before removal.

### Reset Scope Inventory

| Service / Store | Current Reset Support | Gaps |
| --- | --- | --- |
| `SwiftDataStorageService` | Has `clearAllIngredients()` only. | Needs unified `clearAllUserData()` that wipes recipes, history, preferences. |
| `FavoritesStore` | Toggle/load APIs only. | Add `clearAll()` for favorites list and snapshots. |
| `PlanStore` | Persist/merge helpers only. | Add `clearAll()` to remove quick and meal-prep caches. |
| `HistoryStorageService` | CRUD per entry. | No bulk clear for quick history or disk payloads; add `clearAllQuick()` and folder purge. |
| `DiskStorageManager` | Delete single key, TTL cleanup. | Expose helper to purge entire folder (Quick History, Image cache). |
| `TelemetryService` | Opt-in toggles. | Provide reset to revert consent flags and cached session state. |
| Remote Firestore (Quick History/Favorites) | Not integrated. | Coordinate with backend for deletion endpoint before Phase 3.

### Dependencies to Stage
- `ReauthenticationCoordinator`: shared flow covering Email + OAuth reauth, needs SwiftUI binding surface for modals.
- `AccountResetManager`: orchestrates sequential resets across local stores + Firestore with granular error reporting.
- Localized strings bundle for new Account actions (Account, Change Password, Reset Account, Delete Account, Verify Email, etc.).
- Telemetry event scaffolding (`account_*`) with provider/result/error metadata.

## Risks & Follow-ups
- **Apple Sign-In entitlements**: verify target capabilities; empty entitlements file hints at missing configuration.
- **reCAPTCHA enforcement**: without confirmed APNs key and console config, delete/change password flows may fail on device.
- **Reset completeness**: several services lack clearing APIs; plan corresponding PRs early in Phase 1.

## Ready for Phase 1?
Proceed with Phase 1 once DevOps confirms APNs/reCAPTCHA readiness and the entitlement gap is resolved. Technical groundwork is documented; implementation work can now start in alignment with the Firebase Auth iOS Manage Users guide and UI blueprint.
