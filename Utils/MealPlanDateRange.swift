import Foundation

/// Provides the valid date window and helpers for Meal Plan generation.
/// Per V6 spec: valid range is [today, today + 7 days].
struct MealPlanDateRange {
    static let maxPlanDurationDays: Int = 7

    /// Returns the valid date range from the start of today to the start of day in 7 days.
    static func validDateRange(from today: Date = Date(), calendar: Calendar = .current) -> ClosedRange<Date> {
        let start = calendar.startOfDay(for: today)
        let upper = calendar.date(byAdding: .day, value: maxPlanDurationDays, to: start) ?? start
        return start...upper
    }

    /// Clamps a date into the valid range.
    static func clamp(_ date: Date, from today: Date = Date(), calendar: Calendar = .current) -> Date {
        let range = validDateRange(from: today, calendar: calendar)
        if date < range.lowerBound { return range.lowerBound }
        if date > range.upperBound { return range.upperBound }
        return date
    }

    /// Checks whether a date falls within the valid range.
    static func isWithinValidRange(_ date: Date, from today: Date = Date(), calendar: Calendar = .current) -> Bool {
        let range = validDateRange(from: today, calendar: calendar)
        return range.contains(date)
    }
}

