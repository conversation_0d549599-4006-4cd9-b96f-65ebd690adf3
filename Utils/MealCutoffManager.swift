import Foundation

/// Manages same-day cutoff times for meals and provides skip logic.
/// Defaults: Breakfast 10:30, Lunch 14:30, Dinner 21:30.
struct MealCutoffManager {
    struct Cutoffs {
        var breakfast: DateComponents
        var lunch: DateComponents
        var dinner: DateComponents

        static let `default` = Cutoffs(
            breakfast: DateComponents(hour: 10, minute: 30),
            lunch: DateComponents(hour: 14, minute: 30),
            dinner: DateComponents(hour: 21, minute: 30)
        )
    }

    static let userTipSkippedMealsToday = NSLocalizedString(
        "tip_skipped_meals_passed_today",
        comment: "User-facing tip when skipping meals that already passed today"
    )

    private let cutoffs: Cutoffs

    init(cutoffs: Cutoffs = .default) {
        self.cutoffs = cutoffs
    }

    /// Returns true if the cutoff for the given meal has already passed at `now`.
    func hasPassedCutoff(for meal: MealType, now: Date = Date(), calendar: Calendar = .current) -> Bool {
        var base = calendar.dateComponents([.year, .month, .day], from: now)
        let components: DateComponents
        switch meal {
        case .breakfast: components = cutoffs.breakfast
        case .lunch: components = cutoffs.lunch
        case .dinner: components = cutoffs.dinner
        }
        base.hour = components.hour
        base.minute = components.minute
        base.second = components.second ?? 0
        let cutoff = calendar.date(from: base) ?? now
        return now > cutoff
    }

    /// Whether a slot on `date` for `meal` should be skipped due to same-day cutoff.
    /// Only applies when `date` is the same day as `now`.
    func shouldSkipSameDay(meal: MealType, on date: Date, now: Date = Date(), calendar: Calendar = .current) -> Bool {
        guard calendar.isDate(date, inSameDayAs: now) else { return false }
        return hasPassedCutoff(for: meal, now: now, calendar: calendar)
    }
}

