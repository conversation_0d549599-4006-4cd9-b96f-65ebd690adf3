import Foundation

/// Utility helpers for Do Not Eat consolidation flows.
enum DoNotEatHelper {
    struct DedupeResult: Equatable {
        let allergies: [AllergyIntolerance]
        let strictExclusions: [StrictExclusion]
        let customStrings: [String]
        let normalizedKeys: Set<String>
        let promotedCustoms: [String]
    }

    /// Normalizes user-entered text for comparisons (trim, collapse spaces, lowercase).
    static func normalizeForComparison(_ text: String) -> String {
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return "" }
        let collapsed = collapseWhitespace(trimmed)
        return collapsed.lowercased()
    }

    /// Deduplicate three sources of exclusions, preferring enum-backed values.
    static func deduplicateSelections(
        allergies: [AllergyIntolerance],
        strictEnums: [StrictExclusion],
        customStrings: [String]
    ) -> DedupeResult {
        var seenKeys = Set<String>()
        var dedupedAllergies: [AllergyIntolerance] = []
        var dedupedStrict: [StrictExclusion] = []
        var dedupedCustom: [String] = []
        var promoted: [String] = []

        let allergyLookup = Dictionary(uniqueKeysWithValues: AllergyIntolerance.allCases.map { (normalizeForComparison($0.rawValue), $0) })
        let strictLookup = Dictionary(uniqueKeysWithValues: StrictExclusion.allCases.map { (normalizeForComparison($0.rawValue), $0) })

        @discardableResult
        func appendAllergyIfNeeded(_ value: AllergyIntolerance) -> Bool {
            let key = normalizeForComparison(value.rawValue)
            guard !seenKeys.contains(key) else { return false }
            seenKeys.insert(key)
            dedupedAllergies.append(value)
            return true
        }

        @discardableResult
        func appendStrictIfNeeded(_ value: StrictExclusion) -> Bool {
            let key = normalizeForComparison(value.rawValue)
            guard !seenKeys.contains(key) else { return false }
            seenKeys.insert(key)
            dedupedStrict.append(value)
            return true
        }

        allergies.forEach { _ = appendAllergyIfNeeded($0) }
        strictEnums.forEach { _ = appendStrictIfNeeded($0) }

        for raw in customStrings {
            let sanitized = sanitizeCustomText(raw)
            guard !sanitized.isEmpty else { continue }
            let key = normalizeForComparison(sanitized)

            if let allergy = allergyLookup[key], appendAllergyIfNeeded(allergy) {
                promoted.append(sanitized)
                continue
            }

            if let strict = strictLookup[key], appendStrictIfNeeded(strict) {
                promoted.append(sanitized)
                continue
            }

            guard !seenKeys.contains(key) else { continue }
            seenKeys.insert(key)
            dedupedCustom.append(sanitized)
        }

        return DedupeResult(
            allergies: dedupedAllergies,
            strictExclusions: dedupedStrict,
            customStrings: dedupedCustom,
            normalizedKeys: seenKeys,
            promotedCustoms: promoted
        )
    }

    /// Builds the combined exclusion set for quick membership checks.
    static func buildCombinedExclusionSet(from preferences: UserPreferences) -> Set<String> {
        let values = preferences.allergiesIntolerances.map { $0.rawValue } +
                     preferences.strictExclusions.map { $0.rawValue } +
                     preferences.customStrictExclusions
        let normalized = values.map { normalizeForComparison($0) }.filter { !$0.isEmpty }
        return Set(normalized)
    }

    // MARK: - Helpers

    private static func sanitizeCustomText(_ text: String) -> String {
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return "" }
        return collapseWhitespace(trimmed)
    }

    private static func collapseWhitespace(_ text: String) -> String {
        let parts = text.split { $0.isWhitespace }
        return parts.joined(separator: " ")
    }
}
