import Foundation

struct BatchOperationResult: Equatable {
    let totalSelected: Int
    let successCount: Int
    let skippedFavorites: Int
    let failedCount: Int
    let message: String
}

@MainActor
enum BatchOperationManager {

    // MARK: - Quick History

    static func performQuick(action: ManageAction, selected: [QuickResultHistory]) async -> BatchOperationResult {
        let total = selected.count
        guard total > 0 else {
            return BatchOperationResult(totalSelected: 0, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "No items selected")
        }

        let favorites = Set(FavoritesStore.shared.all())

        switch action {
        case .delete:
            // Protect any quick entry that contains a favorited recipe
            let protectedIds: Set<UUID> = Set(selected.filter { entry in
                entry.recipes.contains { favorites.contains($0.id) }
            }.map { $0.id })

            let deletableIds: Set<UUID> = Set(selected.map { $0.id }.filter { !protectedIds.contains($0) })

            if deletableIds.isEmpty {
                return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: protectedIds.count, failedCount: 0, message: "Skipped \(protectedIds.count) favorited item(s)")
            }

            // Atomic replace of remaining items
            let all = QuickHistoryManager.shared.all()
            let kept = all.filter { !deletableIds.contains($0.id) }
            QuickHistoryManager.shared.replaceAll(kept)

            return BatchOperationResult(totalSelected: total, successCount: deletableIds.count, skippedFavorites: protectedIds.count, failedCount: 0, message: "Deleted \(deletableIds.count); skipped \(protectedIds.count) favorited")

        case .addToFavorites:
            var added = 0
            for entry in selected {
                for r in entry.recipes {
                    if !FavoritesStore.shared.isFavorite(id: r.id) {
                        FavoritesManager.shared.toggleFavorite(id: r.id)
                        added += 1
                    }
                }
            }
            return BatchOperationResult(totalSelected: total, successCount: added, skippedFavorites: 0, failedCount: 0, message: "Added \(added) to Favorites")

        case .removeFromFavorites:
            var removed = 0
            for entry in selected {
                for r in entry.recipes {
                    if FavoritesStore.shared.isFavorite(id: r.id) {
                        FavoritesManager.shared.toggleFavorite(id: r.id)
                        removed += 1
                    }
                }
            }
            return BatchOperationResult(totalSelected: total, successCount: removed, skippedFavorites: 0, failedCount: 0, message: "Removed \(removed) from Favorites")

        case .share:
            // Sharing pipeline (UIActivityViewController) is handled in a later task (4.3)
            return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "Share is not implemented yet")

        case .viewDetails:
            return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "View details is only available for single selection")

        case .regenerate:
            return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "Regenerate applies to Plans only")
        }
    }

    // MARK: - Plans (Meal Slots)

    static func performPlans(action: ManageAction, selectedSlots: [MealSlot]) async -> BatchOperationResult {
        let total = selectedSlots.count
        guard total > 0 else {
            return BatchOperationResult(totalSelected: 0, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "No slots selected")
        }

        let favorites = Set(FavoritesStore.shared.all())
        let protected = selectedSlots.filter { favorites.contains($0.recipe.id) }
        let actionable = selectedSlots.filter { !favorites.contains($0.recipe.id) }

        switch action {
        case .delete:
            let ids = Set(actionable.map { $0.slotId })
            PlanStore.shared.deleteSlotsInLastMealPrep(ids)
            return BatchOperationResult(totalSelected: total, successCount: ids.count, skippedFavorites: protected.count, failedCount: 0, message: "Deleted \(ids.count); skipped \(protected.count) favorited")

        case .regenerate:
            // Build regen request and replace recipes in the last plan
            guard let last = PlanStore.shared.loadLastMealPrep() else {
                return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: total, message: "No existing plan to regenerate")
            }

            // Prepare target slots (exclude protected)
            let targets = actionable.map { slot in
                RegenerateRequest.TargetSlot(slotId: slot.slotId, dayIndex: slot.dayIndex, mealType: slot.mealType)
            }

            // Exclude current titles to avoid duplicates
            let exclusions = RegenerateRequest.Exclusions(titles: actionable.map { $0.recipe.title })

            // Pantry context
            let pantryCount = ServiceContainer.shared.pantryService.pantryItems.count
            let pantryContext = PantryContext(hasItems: pantryCount > 0, itemCount: pantryCount)

            let regen = RegenerateRequest(
                targetSlots: targets,
                originalInputsDays: last.days,
                originalSelectedMeals: last.selectedMeals,
                cuisines: last.cuisines,
                additionalRequest: last.additionalRequest,
                pantryContext: pantryContext,
                exclusions: exclusions
            )

            // Generate replacements
            let adapter = RecipeServiceAdapter(recipeService: ServiceContainer.shared.recipeGenerationService, pantryService: ServiceContainer.shared.pantryService)
            let auth = ServiceContainer.shared.authenticationService
            do {
                let replacements = try await adapter.regenerateBatch(regen, authService: auth)
                // Map replacements sequentially to target slots (1:1 up to min)
                var map: [UUID: RecipeUIModel] = [:]
                let count = min(replacements.count, targets.count)
                for i in 0..<count {
                    map[targets[i].slotId] = replacements[i]
                }
                if !map.isEmpty {
                    PlanStore.shared.replaceRecipesInLastMealPrep(map)
                }
                return BatchOperationResult(totalSelected: total, successCount: map.count, skippedFavorites: protected.count, failedCount: total - map.count - protected.count, message: "Regenerated \(map.count); skipped \(protected.count) favorited")
            } catch {
                return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: total, message: "Regenerate failed: \(error.localizedDescription)")
            }

        case .addToFavorites:
            var added = 0
            for slot in actionable { // Favorited ones are already fav; safe to include only actionable here to avoid toggling
                if !FavoritesStore.shared.isFavorite(id: slot.recipe.id) {
                    FavoritesManager.shared.toggleFavorite(id: slot.recipe.id)
                    added += 1
                }
            }
            return BatchOperationResult(totalSelected: total, successCount: added, skippedFavorites: protected.count, failedCount: 0, message: "Added \(added) to Favorites; skipped \(protected.count) already favorited")

        case .removeFromFavorites:
            var removed = 0
            for slot in selectedSlots { // remove favorites among all selected
                if FavoritesStore.shared.isFavorite(id: slot.recipe.id) {
                    FavoritesManager.shared.toggleFavorite(id: slot.recipe.id)
                    removed += 1
                }
            }
            return BatchOperationResult(totalSelected: total, successCount: removed, skippedFavorites: 0, failedCount: 0, message: "Removed \(removed) from Favorites")

        case .share:
            return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "Share is not implemented yet")

        case .viewDetails:
            return BatchOperationResult(totalSelected: total, successCount: 0, skippedFavorites: 0, failedCount: 0, message: "View details is only available for single selection")
        }
    }
}
