import SwiftUI

/// Provides animation and transition helpers for week-to-week navigation.
/// With Reduce Motion enabled, transitions degrade to crossfades.
struct WeekTransitionAnimator {
    static func animation(reduceMotion: Bool) -> Animation {
        if reduceMotion { return .easeInOut(duration: 0.15) }
        return .interactiveSpring(response: 0.32, dampingFraction: 0.86, blendDuration: 0.12)
    }

    enum Direction { case left, right }

    static func transition(for direction: Direction, reduceMotion: Bool) -> AnyTransition {
        if reduceMotion { return .opacity.animation(.easeInOut(duration: 0.15)) }
        switch direction {
        case .left:
            return .asymmetric(
                insertion: .move(edge: .trailing).combined(with: .opacity),
                removal: .move(edge: .leading).combined(with: .opacity)
            )
        case .right:
            return .asymmetric(
                insertion: .move(edge: .leading).combined(with: .opacity),
                removal: .move(edge: .trailing).combined(with: .opacity)
            )
        }
    }
}

