import Foundation
import UIKit

/// Lightweight UI performance monitor for first-paint and average FPS.
/// - Designed to be opt-in and low overhead.
/// - Reports via TelemetryService.trackUIPerformance("ui_performance").
@MainActor
final class PerformanceMonitor {
    private var displayLink: CADisplayLink?
    private var startTime: CFTimeInterval = 0
    private var firstFrameTime: CFTimeInterval?
    private var frameCount: Int = 0
    private var screenName: String = ""

    func start(screen: String) {
        guard displayLink == nil else { return }
        self.screenName = screen
        self.startTime = CACurrentMediaTime()
        self.frameCount = 0
        self.firstFrameTime = nil
        let link = CADisplayLink(target: self, selector: #selector(tick))
        link.add(to: .main, forMode: .common)
        self.displayLink = link
    }

    func stopAndReport() {
        guard let link = displayLink else { return }
        link.invalidate()
        displayLink = nil
        report()
    }

    @objc private func tick() {
        frameCount += 1
        if firstFrameTime == nil { firstFrameTime = CACurrentMediaTime() }
    }

    private func report() {
        let endTime = CACurrentMediaTime()
        let elapsed = max(0.001, endTime - startTime)
        let avgFps = Int(round(Double(frameCount) / elapsed))
        let firstPaintMs = Int(round(((firstFrameTime ?? endTime) - startTime) * 1000))
        ServiceContainer.shared.telemetryService.trackUIPerformance(screen: screenName, avgFps: avgFps, firstPaintMs: firstPaintMs)
    }
}

