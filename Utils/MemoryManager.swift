import Foundation
import UIKit
import Darwin

/// Centralized memory management utilities for lightweight trimming and sampling.
/// - Listens to system memory warnings and triggers registered trim actions.
/// - Provides simple memory footprint sampling for instrumentation.
@MainActor
public final class MemoryManager {
    public static let shared = MemoryManager()

    private var trimActions: [() -> Void] = []
    private var isStarted = false

    private init() {}

    /// Start listening to memory warnings (idempotent).
    public func start() {
        guard !isStarted else { return }
        isStarted = true
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            // Bridge to MainActor explicitly for Swift 6 isolation rules
            Task { @MainActor in
                self?.handleMemoryWarning()
            }
        }
    }

    /// Register a cache trimming action to be invoked on memory pressure.
    /// Example: `MemoryManager.shared.registerTrimAction { imageCache.removeAllObjects() }`
    public func registerTrimAction(_ action: @escaping () -> Void) {
        trimActions.append(action)
    }

    /// Handle memory warning by invoking all registered trim actions.
    public func handleMemoryWarning() {
        for action in trimActions { action() }
    }

    /// Best-effort process memory usage sampling (MB). Returns -1 on failure.
    public func sampleMemoryMB() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        if kerr == KERN_SUCCESS {
            let usedBytes = Double(info.resident_size)
            return usedBytes / (1024.0 * 1024.0)
        } else {
            return -1
        }
    }
}
