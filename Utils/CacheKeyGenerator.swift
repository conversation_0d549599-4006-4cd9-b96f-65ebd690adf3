import Foundation
import CryptoKit

/// Generates stable, order-independent cache keys for recipe generation and details.
/// Keys are built from three independent components:
/// - Pantry hash (canonicalized + sorted names)
/// - Preferences hash (sorted lists; excludes volatile fields like transient times)
/// - Meal spec hash (optional; per-meal counts/times)
struct CacheKeyGenerator {
    private let canonicalizer = NameCanonicalizer()

    // MARK: - Public API

    /// Stable pantry hash from ingredient names (order-independent, canonicalized)
    func pantryHash(from ingredientNames: [String]) -> String {
        let canonical = ingredientNames
            .map { canonicalizer.canonicalize($0) }
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
            .sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
        let joined = canonical.joined(separator: "\n")
        return sha256Hex(joined)
    }

    /// Stable preferences hash using fields that impact generation output.
    /// Excludes transient fields like temporary cooking time values.
    func preferencesHash(from prefs: RecipePreferences) -> String {
        // Normalize arrays (sorted, lowercased where appropriate)
        let diet = prefs.dietaryRestrictions.map { $0.lowercased() }.sorted()
        let allergies = prefs.allergiesAndIntolerances.map { $0.lowercased() }.sorted()
        let exclusions = prefs.strictExclusions.map { $0.lowercased() }.sorted()
        let custom = prefs.customStrictExclusions.map { $0.lowercased() }.sorted()
        let cuisines = prefs.cuisines.map { $0.lowercased() }.sorted()
        let equipment = prefs.equipmentOwned.map { $0.lowercased() }.sorted()

        // Build a compact canonical JSON string for hashing
        let dict: [String: Any] = [
            "respectRestrictions": prefs.respectRestrictions,
            "servings": prefs.numberOfServings,
            "dietary": diet,
            "allergies": allergies,
            "exclusions": exclusions,
            "customExclusions": custom,
            "cuisines": cuisines,
            "equipment": equipment
        ]
        let json = canonicalJSON(dict)
        return sha256Hex(json)
    }

    /// Stable meal specification hash from RequestDetails (order-independent by meal type).
    func mealSpecHash(from details: RequestDetails?) -> String? {
        guard let details = details else { return nil }
        // Map to a simple [type: {count, time}] dictionary and sort by type
        var mealDict: [String: [String: Int]] = [:]
        for meal in details.meals {
            mealDict[meal.type] = [
                "count": meal.numberOfDishes,
                "time": meal.maxCookingTimeMinutes
            ]
        }
        let json = canonicalJSON(mealDict)
        return sha256Hex(json)
    }

    /// Composite cache key for a recipe detail entry.
    /// - Parameters:
    ///   - title: Recipe title (part of the identity)
    ///   - pantryNames: Pantry ingredient names
    ///   - preferences: User/preferences context
    ///   - mealSpecHash: Optional precomputed meal spec hash
    /// - Returns: Stable cache key string
    func detailCacheKey(title: String, pantryNames: [String], preferences: RecipePreferences, mealSpecHash: String? = nil) -> String {
        let pHash = pantryHash(from: pantryNames)
        let prefHash = preferencesHash(from: preferences)
        let comp = ["v1", "detail", sha256Hex(title.lowercased()), pHash, prefHash, mealSpecHash ?? ""].joined(separator: ":")
        return comp
    }

    // MARK: - Helpers

    private func sha256Hex(_ input: String) -> String {
        let data = Data(input.utf8)
        let digest = SHA256.hash(data: data)
        return digest.map { String(format: "%02x", $0) }.joined()
    }

    /// Create a deterministic JSON string (keys sorted recursively)
    private func canonicalJSON(_ value: Any) -> String {
        if let dict = value as? [String: Any] {
            let sortedKeys = dict.keys.sorted()
            let items = sortedKeys.map { key -> String in
                let val = canonicalJSON(dict[key] as Any)
                return "\"\(escape(key))\":\(val)"
            }
            return "{\(items.joined(separator: ","))}"
        } else if let arr = value as? [Any] {
            let items = arr.map { canonicalJSON($0) }
            return "[\(items.joined(separator: ","))]"
        } else if let s = value as? String {
            return "\"\(escape(s))\""
        } else if let b = value as? Bool {
            return b ? "true" : "false"
        } else if let i = value as? Int {
            return String(i)
        } else if let d = value as? Double {
            return String(d)
        } else if value is NSNull {
            return "null"
        } else {
            // Fallback to Swift description
            return "\"\(escape(String(describing: value)))\""
        }
    }

    private func escape(_ s: String) -> String {
        var out = ""
        out.reserveCapacity(s.count)
        for ch in s.unicodeScalars {
            switch ch {
            case "\\": out.append("\\\\")
            case "\"": out.append("\\\"")
            case "\n": out.append("\\n")
            case "\r": out.append("\\r")
            case "\t": out.append("\\t")
            default: out.unicodeScalars.append(ch)
            }
        }
        return out
    }
}
