import SwiftUI

/// Provides animation helpers for collapsed/expanded calendar transitions.
/// With Reduce Motion, transitions degrade to quick opacity changes.
struct CalendarExpansionAnimator {
    static func animation(reduceMotion: Bool) -> Animation {
        if reduceMotion { return .easeInOut(duration: 0.15) }
        return .spring(response: 0.28, dampingFraction: 0.85, blendDuration: 0.1)
    }

    static func transition(reduceMotion: Bool) -> AnyTransition {
        if reduceMotion { return .opacity }
        return .asymmetric(
            insertion: .scale(scale: 0.98).combined(with: .opacity),
            removal: .scale(scale: 0.98).combined(with: .opacity)
        )
    }
}

