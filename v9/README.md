# V9 Do Not Eat Consolidation Implementation Guide

## Overview
This guide describes how to implement the unified **Do Not Eat** experience defined in `v9/do-not-eat-unified-exclusions-prd.md`. The goal is to merge the Allergies and Strict Exclusions profile flows into a single SwiftUI screen (iOS 17+) while keeping generator behaviour unchanged and introducing an additive store for custom exclusions.

## File Structure
```
v9/
├── README.md                            # Implementation guide (this document)
└── do-not-eat-unified-exclusions-prd.md # Product requirements document for V9 consolidation
```

## Prerequisites & Dependencies
- Additive `customStrictExclusions` field must be defined in `UserPreferences` and SwiftData models before UI work (default empty for backwards compatibility).
- Ensure `AuthenticationService.updatePreferences` and `userProfileService.savePreferencesOfflineAware` pass through the new field.
- Confirm `RecipeGenerationService` consumes the union of allergies, strict enums, and custom strings without further code changes.
- Audit existing data for ad-hoc custom exclusions so migration helpers can retain them.

## Implementation Approach (4 Phases)

1. **Technical Foundation (Day 0–1)**
   - Implement storage updates: add `customStrictExclusions` arrays to models plus conversion helpers for enum ↔ string union.
   - Write unit tests for migration/normalisation utilities.
   - Scaffold `Features/Profile/DoNotEatView.swift` with mock data to validate adaptive chip layout and “My Exclusions” summary.

2. **Data Integration (Day 2)**
   - Connect the view to real `UserPreferences` via `AuthenticationService`, populating three sets (allergies, strict enums, custom strings).
   - Build dedupe helpers (case-insensitive) that return canonical display casing and prefer enum hits over duplicate strings.
   - Implement the custom item entry sheet with validation rules defined in the PRD; surface inline errors and disable Save accordingly.

3. **Navigation & Polish (Day 3)**
   - Replace the two legacy rows in `Features/Profile/PreferencesEditView.swift` with a single "Do Not Eat" push destination; reuse existing save callback pattern, returning a struct that carries all three collections.
   - Keep `AllergiesIntolerancesView` / `StrictExclusionsView` compiled but hidden (debug-only entry if desired) to support rollback.
   - Add help sheet copy, localization placeholders, and VoiceOver announcements.

4. **Validation & Release Prep (Day 4)**
   - Run regression/unit suites plus migration tests against sample data.
   - Smoke-test recipe generation (Quick + Meal Plan) ensuring combined exclusions take effect.
   - Review analytics wiring (optional) and update release notes.

## Key Code Touchpoints
- **Preferences routing** – `Features/Profile/PreferencesEditView.swift` currently presents separate editors; introduce a single destination that handles a combined payload (`DoNotEatSelection` struct with three arrays).
- **Selection logic references** – `Features/Profile/AllergiesIntolerancesView.swift` & `StrictExclusionsView.swift` provide existing toggle/save patterns and copy we can reuse.
- **Data model updates** – `Models/UserPreferences.swift`, `Models/SwiftDataModels.swift`, and any DTOs/Cache layers must expose `customStrictExclusions` with default values to maintain decoding safety.
- **Generation safeguards** – `Services/RecipeGenerationService.swift:92` already unions allergies + strict exclusions; extend helper to include custom strings when building the blocked set.
- **Caching / sync** – `Utils/CacheKeyGenerator.swift` may need to include custom exclusions in the hash to prevent stale caches.

## UX & Interaction Guidelines
- Title: **Do Not Eat**; subtitle: “Items in this list will be strictly excluded from recipe ideas. Mark allergies to avoid any risk.”
- Sections:
  - `My Exclusions` – chips summarising selections with inline remove (tap to toggle off).
  - `Allergies & Intolerances` – chips from `AllergyIntolerance.allCases` with icons and “Allergy” tag when selected.
  - `Strict Exclusions` – chips from `StrictExclusion.allCases`; sub-group if needed for readability.
- Chip styling: rounded rectangle (20pt radius), adaptive grid (`LazyVGrid` or custom flow). Selected state uses accent fill + white text; unselected uses tertiary outline.
- Custom item entry: `+ Add custom item` button near top launching sheet. Successful save appends to view state immediately with `Custom` badge.
- Navigation bar: trailing `Save`, leading back gesture for cancel.

## Data Handling Requirements
- Maintain four internal collections:
  - `selectedAllergies: Set<AllergyIntolerance>`
  - `selectedStrictEnums: Set<StrictExclusion>`
  - `selectedCustomStrings: Set<CustomEntry>` (store both original text and lowercase key)
  - `allSelectedKeys: Set<String>` (computed lowercase keys for fast dedupe)
- Save path:
  - Allergies → `preferences.allergiesIntolerances`
  - Strict enums → `preferences.strictExclusions`
  - Custom strings → `preferences.customStrictExclusions`
- Migration helper: when loading, ensure any existing enum-backed strict exclusions remain intact and the custom array defaults to empty. During save, normalise to prevent duplicates (if a custom item matches an enum label, promote to enum and drop the custom string).
- Update `preferences.lastUpdated` and route through `authService.updatePreferences` to refresh in-app cache and sync layers.

## Accessibility & Localization
- VoiceOver: announce “Allergy chip, selected, Dairy” style strings with category context.
- Ensure 44x44 touch targets and focus order matching visual sections.
- Land new copy in localization files once content is finalized.

## Telemetry (Optional, PRD §10)
- Event `do_not_eat_view` with counts for allergy/strict/custom selections.
- Events for `do_not_eat_custom_added` / `do_not_eat_custom_removed` including string length (not value) for privacy.

## Testing & Validation Checklist (Expanded)
- ✅ **Unit**: normalisation & dedupe helpers; enum/custom migration utilities; custom input validator.
- ✅ **Integration**: save/load cycle exercising all three arrays; migration from legacy data; cache key updates.
- ✅ **UI**: chip wrap layout across iPhone widths; custom entry sheet error states; VoiceOver traversal.
- ✅ **Regression**: `FoodPreferencesRegressionTests`, `CrossDeviceSyncTests`, plus any generator prompt tests touching preferences.
- ✅ **Manual**: generate recipes (Quick + Meal Plan) to confirm exclusions; simulate offline save queue; verify analytics (if enabled).

## Risks & Mitigations
- **Duplicate entries** – Use centralised helper with unit coverage; log conflicts during testing.
- **User confusion over severity** – Provide clear section headers, help sheet copy, and an “Allergy” tag on chips.
- **Layout crowding on small screens** – Adaptive grid with vertical spacing, test on iPhone SE size.
- **Migration defects** – Implement fallback/rollback path and assert conversions with tests before rollout.

## Deliverables
- `Features/Profile/DoNotEatView.swift` plus shared helpers/extensions.
- Updated `UserPreferences`/SwiftData models and cache/adapters handling `customStrictExclusions`.
- Updated routing in `PreferencesEditView` with single entry.
- Automated tests covering data conversion and selection logic.
- README/PRD kept in sync within `v9/`.

Follow this guide alongside the PRD to deliver the consolidated Do Not Eat experience safely and efficiently for iOS 17+ users.
