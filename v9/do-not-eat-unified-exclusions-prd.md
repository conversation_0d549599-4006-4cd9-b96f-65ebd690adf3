# V9 PRD — Do Not Eat Consolidated Preferences

## 1. Background
Users currently manage “Allergies & Intolerances” and “Strict Exclusions” through two distinct profile screens. Feedback shows the split creates friction, while reference apps demonstrate a single, chip-based exclusion hub with clearer discoverability. Previous analysis recommends unifying the UI while leaving the underlying data model largely intact to avoid large migrations.

## 2. Goals
- Provide one consolidated "Do Not Eat" entry in Profile settings.
- Offer a modern, chip-driven selection experience grouped by exclusion type.
- Preserve prompt/filter behaviour by continuing to pass combined exclusions to generation.
- Enable custom exclusions entry without regressions.

## 3. Non-Goals
- No breaking changes to existing storage schemas; additive fields are allowed when default decoding continues to succeed.
- No changes to recipe generation prompts or filtering logic.
- No introduction of new remote configuration or feature flags beyond existing ones.

## 4. Users & Scenarios
- Primary: users with food allergies or strong dislikes configuring exclusions once and expecting consistent filtering.
- Scenario: user visits Profile → Do Not Eat, reviews current exclusions, adds a custom item (“Kale”), ensures it persists, and confirms recipe generation respects combined preferences.

## 5. Scope
- In scope: Profile UI updates, unified selection logic, persistence hookup, basic analytics.
- Out of scope: redesign of dietary restrictions, backend API adjustments, copy localization changes beyond new strings.

## 6. Dependencies & Assumptions
- Authentication service continues providing `UserPreferences` with populated arrays.
- Existing save pipeline (`userProfileService.savePreferencesOfflineAware`) remains available and reliable.
- Chip styling leverages current SwiftUI components (iOS 17+ environment) without additional dependencies.

## 7. Functional Requirements

### FR-01: Single Entry Point
- Replace “Allergies & Intolerances” and “Strict Exclusions” rows with one "Do Not Eat" row in `PreferencesEditView`.
- Row displays aggregated count of exclusions (sum of allergies + strict enums + custom).
- Legacy views remain reachable via debug-only navigation (if required).

### FR-02: Consolidated Selection Screen
- Create `DoNotEatView` with navigation title “Do Not Eat”.
- Display current selections in a "My Exclusions" section (chips with remove affordance).
- Group available options into sections:
  - Allergies & Intolerances (source: `AllergyIntolerance.allCases`).
  - Strict Exclusions (source: `StrictExclusion.allCases`, optionally sub-grouped by category for layout clarity).
- Chips toggle selection on tap; selected state uses accent fill, unselected uses tertiary outline.
- Selected chips show contextual tags (e.g., “Allergy”, “Strict”, “Custom”).

### FR-03: Custom Item Support
- Provide prominent “Add custom item” button near the top; present input in a sheet with single text field and Save/Cancel actions.
- Validation rules: trimmed value must be 2–40 characters, allow letters, numbers, spaces, hyphen, apostrophe. Collapse repeated spaces.
- Show inline error message for invalid input or duplicates; disable Save until validation passes.
- On successful save, new item appears immediately in "My Exclusions" with a `Custom` badge.

### FR-04: Persistence & Validation
- On save, split selections: 
  - Items matching `AllergyIntolerance` enum → `preferences.allergiesIntolerances`.
  - Items matching `StrictExclusion` enum → `preferences.strictExclusions`.
  - Custom strings → `preferences.customStrictExclusions` (new string array storing raw user input).
- Combined exclusion set (enum values + custom strings) feeds existing prompts and filters unchanged.
- Deduplicate case-insensitively across all three sets while preserving display casing (enum rawValue or sanitized user input).
- Continue using `userProfileService.savePreferencesOfflineAware` for persistence and `authService.updatePreferences` for local cache. Display existing success/error feedback.

### FR-05: Help & Education
- Provide question-mark button near “My Exclusions” opening a help sheet that clarifies strict avoidance and the difference between allergies vs dislikes.
- Optional grey subtext under title: “Items in this list will be strictly excluded from recipe ideas. Mark allergies to avoid any risk.”

### FR-06: Accessibility & Responsiveness
- Support VoiceOver by announcing chip role, category, and selection state.
- Layout adapts to compact width (adaptive grid or flow layout). Minimum hit target 44x44.

### FR-07: Custom Storage & Migration
- Introduce additive `customStrictExclusions: [String]` field in `UserPreferences` and SwiftData models with backwards-compatible decoding defaults.
- Provide migration helper that initialises the new array to empty and ensures any previously stored custom values (if added through legacy flows) are copied across without data loss.
- Add unit tests covering enum/string round-trips, combined collection building, and migration edge cases.

## 8. Non-Functional Requirements
- Performance: Screen loads available exclusions within 150 ms on iPhone 12 equivalent.
- Reliability: Saving preferences must succeed offline with queueing (existing behaviour).
- Data consistency: Enum/string conversion and deduplication complete within 50 ms for up to 100 entries and retain 100% of pre-existing selections.
- Usability: QA validation ensures zero orphaned selections; custom items appear instantly in top section.

## 9. UX Specifications
- Layout mimics reference app with grouped chips and floating sections.
- Colors follow existing design tokens (`.orange` accent for allergies, `.blue` accent for strict) or a unified accent if design prefers.
- Chips: rounded rectangle with 20pt corner radius, padding (horizontal 12, vertical 8).
- Buttons: `+ Add custom item`, `Save` on nav bar trailing, `Cancel` via back swipe/chevron.
- Empty state copy for “My Exclusions”: “No items selected yet.”

## 10. Telemetry & Instrumentation (Optional V2)
- Log screen view event with counts of allergies/strict/custom.
- Log custom addition and removal events for future UX improvement.

## 11. Acceptance Criteria
- Navigating to Profile shows only one “Do Not Eat” row with accurate count.
- Selecting/deselecting chips updates “My Exclusions” and counts immediately.
- Adding a custom item adds a chip labelled `Custom`, persists after relaunch, and appears in generator prompt via combined exclusion set.
- Enum/string migration runs automatically with no data loss (validated by automated test).
- Existing regression tests (`FoodPreferencesRegressionTests`, `CrossDeviceSyncTests`) remain green without modification.
- Quick and Meal Plan generations continue to omit combined exclusions (manual smoke test).

## 12. Rollout Plan
- Ship behind no new flag (UI only). Optionally guard navigation entry with remote flag for phased release.
- Monitor crash logs and user feedback for 1 week post-release.

## 13. Risks & Mitigations
- **Risk:** Misclassification of custom values leading to duplicates.
  - **Mitigation:** Normalize strings for comparison; unit test helper.
- **Risk:** Users misinterpret severity differences.
  - **Mitigation:** Provide help text emphasising medical implications.
- **Risk:** Layout crowding on small screens.
  - **Mitigation:** Use adaptive grid with wrap and scroll.
- **Risk:** Custom storage migration fails for legacy data.
  - **Mitigation:** Add conversion validation with rollback and integration tests.

## 14. Milestones
- Day 0–1: Implement `DoNotEatView` layout with mock data and selection scaffolding.
- Day 2: Wire real data, build conversion helpers, implement custom entry sheet and validation.
- Day 3: Integrate routing, polish copy, add help content and accessibility passes.
- Day 4: Regression testing, migration validation, telemetry hooks (if chosen), prepare release notes.

## 15. Appendix
- Legacy views kept under `#if DEBUG` for potential fallback.
- Reference screens: see shared screenshot from competitor app.
- Target platform: iOS 17+, SwiftUI-based implementation.
