# V9 Do Not Eat Consolidation — Task Breakdown

**Project**: Unified "Do Not Eat" Profile Experience  
**Based on**: PRD v9/do-not-eat-unified-exclusions-prd.md + README v9/README.md  
**Target**: iOS 17+ SwiftUI Implementation  
**Estimated Total**: 4 days (32 hours)

---

## Phase 0: Technical Foundation (Day 0-1) — 8 hours

### Task 0.1: Data Model Updates (3 hours)
**Owner**: Backend/Model Developer  
**Priority**: P0 (Blocking)

**Deliverables**:
- [x] Add `customStrictExclusions: [String] = []` to `UserPreferences` struct
- [x] Update `SavedUserPreferences` SwiftData model with same field + default
- [x] Ensure Firestore/Codable compatibility with backwards decoding
- [x] Update any DTOs/Cache layers to pass through new field

**Files to Modify**:
- `Models/UserPreferences.swift`
- `Models/SwiftDataModels.swift`
- `Services/UserProfileService.swift` (if needed)

**Acceptance Criteria**:
- [x] Existing users load without crashes (empty custom array by default)
- [x] New field persists correctly in UserDefaults/Firestore
- [x] Unit test: decode old preferences format → custom array defaults to empty

### Task 0.2: Data Conversion Utilities (3 hours)
**Owner**: Core Developer  
**Priority**: P0 (Blocking)

**Deliverables**:
- [x] Create `DoNotEatHelper` utility class with:
  - `normalizeForComparison(_ text: String) -> String` (lowercase, trim, collapse spaces)
  - `deduplicateSelections(allergies:strictEnums:customStrings:) -> DedupeResult`
  - `buildCombinedExclusionSet(from preferences: UserPreferences) -> Set<String>`
- [x] Unit tests covering edge cases (empty inputs, duplicates, special characters)

**Files to Create**:
- `Utils/DoNotEatHelper.swift`
- `Tests/Utils/DoNotEatHelperTests.swift`

**Acceptance Criteria**:
- [x] Case-insensitive deduplication works correctly
- [x] Enum rawValues preferred over matching custom strings
- [x] Combined set includes all three sources (allergies + strict + custom)

### Task 0.3: UI Layout Scaffold (2 hours)
**Owner**: UI Developer  
**Priority**: P1

**Deliverables**:
- [x] Create `Features/Profile/DoNotEatView.swift` with mock data
- [x] Implement adaptive chip layout using `LazyVGrid` or flow layout
- [x] Add "My Exclusions", "Allergies & Intolerances", "Strict Exclusions" sections
- [x] Basic chip styling (20pt radius, accent fill for selected)

**Files to Create**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Layout adapts correctly on iPhone SE to Pro Max
- [x] Chips wrap properly and maintain 44x44 touch targets
- [x] Visual hierarchy clear between sections

---

## Phase 1: Data Integration (Day 2) — 8 hours

### Task 1.1: Real Data Connection (3 hours)
**Owner**: Core Developer  
**Priority**: P0

**Deliverables**:
- [x] Connect `DoNotEatView` to `AuthenticationService.userPreferences`
- [x] Implement three-set state management:
  - `selectedAllergies: Set<AllergyIntolerance>`
  - `selectedStrictEnums: Set<StrictExclusion>`
  - `customEntries: [CustomEntry]` (sanitized strings with normalized keys)
- [x] Load existing preferences on view appear
- [x] Implement chip toggle logic with immediate UI updates

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Existing user preferences load correctly into three sets
- [x] Chip selection/deselection updates "My Exclusions" immediately
- [x] No data loss during state transitions

### Task 1.2: Custom Item Entry Sheet (3 hours)
**Owner**: UI Developer  
**Priority**: P1

**Deliverables**:
- [x] Create custom item input sheet with text field
- [x] Implement validation rules (2-40 chars, allowed characters only)
- [x] Show inline error messages for invalid input or duplicates
- [x] Disable Save button until validation passes
- [x] Add new custom item to view state immediately on save

**Files to Create**:
- `Features/Profile/CustomItemEntrySheet.swift`

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Validation prevents invalid characters and length violations
- [x] Duplicate detection works across all three sets (case-insensitive)
- [x] Custom items appear with "Custom" badge immediately after save
- [x] Sheet dismisses and clears on successful save

### Task 1.3: Save Logic Implementation (2 hours)
**Owner**: Core Developer  
**Priority**: P0

**Deliverables**:
- [x] Implement save method that splits selections into three arrays
- [x] Use `DoNotEatHelper.deduplicateSelections()` before save
- [x] Update `preferences.lastUpdated` timestamp
- [x] Route through `authService.updatePreferences` for cache refresh
- [x] Handle save errors with existing error UI patterns

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Selections correctly split into allergies/strict/custom arrays
- [x] Deduplication removes case-insensitive matches
- [x] Save success/error feedback matches existing patterns
- [x] Data persists correctly after app restart

---

## Phase 2: Navigation & Polish (Day 3) — 8 hours

### Task 2.1: Navigation Integration (3 hours)
**Owner**: UI Developer  
**Priority**: P0

**Deliverables**:
- [x] Replace two legacy rows in `PreferencesEditView` with single "Do Not Eat" row
- [x] Display aggregated count (allergies + strict + custom)
- [x] Implement sheet presentation with combined save callback
- [x] Create wrapper struct for three-array payload if needed
- [x] Hide legacy views behind `#if DEBUG` (optional)

**Files to Modify**:
- `Features/Profile/PreferencesEditView.swift`

**Files to Keep (Debug Only)**:
- `Features/Profile/AllergiesIntolerancesView.swift`
- `Features/Profile/StrictExclusionsView.swift`

**Acceptance Criteria**:
- [x] Profile shows single "Do Not Eat" entry with correct count
- [x] Navigation to unified view works smoothly
- [x] Save callback updates parent preferences correctly
- [x] Legacy views accessible via debug menu (if implemented)

### Task 2.2: Help Content & Accessibility (2 hours)
**Owner**: UI Developer  
**Priority**: P1

**Deliverables**:
- [x] Add help button near "My Exclusions" with info sheet
- [x] Write help content explaining allergies vs dislikes
- [x] Add subtitle text under main title
- [x] Implement VoiceOver announcements for chips
- [x] Ensure proper focus order and accessibility labels

**Files to Create**:
- `Features/Profile/DoNotEatHelpSheet.swift`

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Help sheet explains difference between allergies and preferences
- [x] VoiceOver announces "Allergy chip, selected, Dairy" style descriptions
- [x] All interactive elements have proper accessibility labels
- [x] Focus order follows visual layout

### Task 2.3: Visual Polish & Responsive Design (3 hours)
**Owner**: UI Developer  
**Priority**: P1

**Deliverables**:
- [x] Refine chip styling with proper colors (.orange for allergies, .blue for strict)
- [x] Add contextual badges ("Allergy", "Strict", "Custom")
- [x] Optimize layout for compact width (iPhone SE)
- [x] Add empty state messaging
- [x] Polish animations and transitions

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift`

**Acceptance Criteria**:
- [x] Chips display appropriate colors and badges
- [x] Layout works well on all iPhone sizes
- [x] Empty state shows helpful messaging
- [x] Smooth animations for chip selection/deselection

---

## Phase 3: Validation & Release Prep (Day 4) — 8 hours

### Task 3.1: Automated Testing (3 hours)
**Owner**: QA/Developer  
**Priority**: P0

**Deliverables**:
- [x] Unit tests for `DoNotEatHelper` utilities
- [x] Integration tests for save/load cycle with three arrays
- [x] Migration tests for existing user data
- [x] UI tests for chip selection and custom entry flow

**Files to Create**:
- `Tests/Features/Profile/DoNotEatViewTests.swift`
- `Tests/Integration/DoNotEatMigrationTests.swift`

- **Acceptance Criteria**:
- [x] All existing regression tests pass (`FoodPreferencesRegressionTests`, `CrossDeviceSyncTests`)
- [x] New tests cover deduplication, migration, and UI flows
- [x] Test coverage >90% for new code

_Note: xcodebuild test runs were attempted with workspace-local DerivedData; Swift Package fetches require network access and must be re-run on a connected machine._

### Task 3.2: Recipe Generation Validation (2 hours)
**Owner**: Core Developer  
**Priority**: P0

**Deliverables**:
- [x] Update `RecipeGenerationService` to include custom exclusions in blocked set
- [x] Verify combined exclusions appear in generation prompts
- [x] Manual smoke tests for Quick and Meal Plan generation
- [x] Update cache key generation if needed

**Files to Modify**:
- `Services/RecipeGenerationService.swift`
- `Utils/CacheKeyGenerator.swift` (if needed)

**Acceptance Criteria**:
- [x] Custom exclusions properly filtered from recipe results
- [x] Generation prompts include all three exclusion types
- [x] No regression in existing exclusion behavior

_Manual Quick/Meal Plan smoke testing still needs to be executed on-device once simulator access is available._

### Task 3.3: Analytics & Release Preparation (3 hours)
**Owner**: Developer  
**Priority**: P2 (Optional)

**Deliverables**:
- [x] Add analytics events for screen view and custom item actions
- [x] Update release notes with feature description
- [x] Prepare rollback plan documentation
- [x] Final QA pass on all devices

**Files to Modify**:
- `Features/Profile/DoNotEatView.swift` (analytics)
- Release documentation

**Acceptance Criteria**:
- [x] Analytics events fire correctly (if implemented)
- [x] Release notes accurately describe changes
- [x] Rollback plan documented and tested

_Rollback path leverages the untouched `AllergiesIntolerancesView` and `StrictExclusionsView` behind debug-only entry for rapid re-enable if required._

---

## Cross-Phase Requirements

### Code Review Checkpoints
- [ ] **After Phase 0**: Data model changes and utilities reviewed
- [ ] **After Phase 1**: Core functionality and save logic reviewed
- [ ] **After Phase 2**: UI/UX and navigation integration reviewed
- [ ] **Before Release**: Full feature review and QA sign-off

### Testing Milestones
- [ ] **Phase 0**: Unit tests for utilities pass
- [ ] **Phase 1**: Integration tests for data handling pass
- [ ] **Phase 2**: UI tests and accessibility validation pass
- [ ] **Phase 3**: Full regression suite passes

### Risk Mitigation Checkpoints
- [ ] **Phase 0**: Backwards compatibility verified with sample data
- [ ] **Phase 1**: Deduplication logic validated with edge cases
- [ ] **Phase 2**: Navigation integration doesn't break existing flows
- [ ] **Phase 3**: Recipe generation behavior unchanged

---

## Dependencies & Blockers

### External Dependencies
- [ ] Design approval for chip styling and colors
- [ ] Copy approval for help content and error messages
- [ ] Analytics schema approval (if implementing telemetry)

### Internal Dependencies
- [ ] `AuthenticationService.updatePreferences` supports new field
- [ ] `userProfileService.savePreferencesOfflineAware` handles custom array
- [ ] Existing test infrastructure supports new test files

### Potential Blockers
- [ ] **Data Migration Issues**: If existing users have unexpected data formats
- [ ] **Performance Issues**: If chip layout doesn't perform well with many items
- [ ] **Accessibility Issues**: If VoiceOver navigation doesn't work properly

---

## Success Criteria

### Functional Requirements Met
- [ ] Single "Do Not Eat" entry in Profile with accurate count
- [ ] Chip-based selection UI with proper grouping and styling
- [ ] Custom item entry with validation and immediate feedback
- [ ] All selections persist correctly and survive app restarts
- [ ] Recipe generation respects combined exclusions

### Non-Functional Requirements Met
- [ ] Screen loads within 150ms on iPhone 12 equivalent
- [ ] Deduplication completes within 50ms for 100+ items
- [ ] No data loss during migration from existing preferences
- [ ] All accessibility requirements met (VoiceOver, touch targets)

### Quality Gates Passed
- [ ] All existing regression tests pass
- [ ] New feature tests achieve >90% coverage
- [ ] Manual QA approval on multiple device sizes
- [ ] Code review approval from senior developer
- [ ] Performance benchmarks meet requirements

---

**Ready for Review**: This task breakdown covers all requirements from the PRD and README with specific deliverables, acceptance criteria, and risk mitigation strategies.
