# V8 Unified PRD — Meal Plan Generation (MVP, Extended UX, Performance)

## 1) Executive Summary & Background
- Meal Plan (Custom) generation suffers from critical stability issues that make it unusable beyond simple configurations, combined with performance problems that cause perceived "hangs." Investigation reveals crashes, stale-navigation to old results, broken Manage integration, and excessive downstream calls.
- Keep the calendar UI intact while introducing: start date selection, append‑only overlap, non‑blocking post‑save summary, prompt fidelity (all configs as constraints), and core stability/performance fixes. Quick mode remains unchanged.
- Outcomes: stable generation with graceful error isolation; non‑blocking feedback; reduced network calls; optional parallel per‑meal generation; responsive UI.

## 2) Goals and Non‑Goals
### Goals
- Phase 1 (P0) — Critical Stability & Prompt Fidelity
  - Zero crashes across configuration combinations; per‑meal failures do not crash the whole run.
  - Reliable generation that always processes new configurations (no stale navigation).
  - Functional Manage button that shows generated recipes and allows operations.
  - Idea generation prompts explicitly include all selected configurations as constraints (meal type; requested dish count; preferred cuisines; additional request; total time; servings; restrictions/exclusions; equipment).
- Phase 2 (P1) — Performance & Responsiveness
  - Eliminate perceived "freeze" during Meal Plan generation.
  - Reduce total AI calls for typical scenarios (Lunch+Dinner, 5 days) from ≤10 → 4.
  - Keep the main thread responsive; Cancel reacts promptly.

### Non‑Goals
- No change to Quick mode behavior (still prefetches details).
- No redesign of the calendar layout or navigation patterns.
- No server/API changes beyond existing client concurrency/prefetch controls.

## 2A) Environment & Targets
- Language/Concurrency: Swift 5.9 with Swift Concurrency (async/await, MainActor); Strict concurrency = complete
- Platform: iOS 17.0 (IPHONEOS_DEPLOYMENT_TARGET)
- UI Framework: SwiftUI
- Build/Project: XcodeGen-managed (project.yml)


## 3) Users & Key Scenarios
- Primary user: home cook planning multiple days (2–7) with 1–3 meal types, time/serving preferences.
- Crash scenario validation: 3 days; Breakfast 2 dishes + Lunch 3 dishes + Dinner 4 dishes.
- Performance scenario validation: 5 days; Lunch 3×@40m; Dinner 4×@60m; family size 5.
- Overlap scenario: Generate 1–7; later generate 5–9; 5–7 append; 8–9 new.

## 4) Scope, Assumptions & Dependencies
- In scope: generation pipeline behavior, overlap merge policy, non‑blocking feedback, prompt content fidelity, concurrency/performance knobs, telemetry.
- Out of scope: calendar layout redesign; new UI pages; server/API changes.
- Assumptions: same‑day cutoff logic unchanged (Utils/MealCutoffManager.swift). PlanStore uses atomic UserDefaults JSON (4‑week retention). Downstream AI can handle limited concurrency (cap if needed).


## 4A) Current Issues & Reproduction Steps (Condensed from Investigation)
- Issue 1: Meal Plan Generation Crashes beyond trivial configs
  - Repro: After a successful 1-breakfast, try 2–3 breakfast; breakfast+lunch; all meals; or multi-day → app crashes.
  - Likely Causes: Resource contention, memory pressure in Adapter, unhandled errors in per-meal loop.
  - Fix Path: Per-meal try/catch isolation; cap ideas per meal to 12; add top-level timeout and cancellation checks.
- Issue 2: Auto-Navigation to Stale Results
  - Repro: Generate once; change config (e.g., dish counts); press Generate → immediate jump to old results; no new generation.
  - Root: Navigation not gated by configuration change; cache reuse without detection.
  - Fix Path: FR‑MVP‑8 fingerprinting; navigate only after fresh generation; store/compare fingerprints in UserDefaults.
- Issue 3: Manage Button Shows No Items
  - Repro: Generate a plan; open Plans → Manage; generated items not listed.
  - Root: Plan indexing and slotsProvider mismatch for visible week (date keys/formatting).
  - Fix Path: Standardize yyyy‑MM‑dd UTC indexing; validate slots collection; add fallback collection from plan.days.

## 5) Functional Requirements

### FR‑MVP (Core)
- FR‑MVP‑1 Start Date Selection & Enumeration
  - Add/start tracking `userSelectedStartDate` in ViewModel (default: today; bounded to today … today+7; honor same‑day cutoff).
  - Build Meal Plan request with that start date; generator enumerates concrete (date×meal) slots from it.
  - Acceptance: when starting “today” after certain cutoffs, those meals are skipped; future days unaffected.

- FR‑MVP‑2 Append‑Only Merge (Overlap)
  - Merge policy default for Meal Plan saving: AppendAll (preserve existing items; add new side‑by‑side). No replacements occur in MVP.
  - Acceptance: For overlapping days, existing items remain; new items are visible in addition.

- FR‑MVP‑3 Non‑Blocking Post‑Save Summary
  - After save, show one inline banner (or toast) summarizing additions by day×meal, with a “View” CTA that jumps to the week. Auto‑dismiss in ~3–5 seconds; no modal dialogs.
  - Acceptance: Summary text lists correct counts; View CTA navigates to the relevant week.

- FR‑MVP‑4 Manage Integration & Visibility
  - Ensure plan indexing and slot collection return the correct items for the visible week (date keys in yyyy‑MM‑dd, UTC, en_US_POSIX). Manage enables delete/favorite/share operations.
  - Acceptance: Immediately after generation, Manage displays expected items and operations succeed.

- FR‑MVP‑5 Stability & Error Handling
  - Per‑meal try/catch in structured generation; failure of one meal type doesn’t crash the entire run. Resource cap: limit generated ideas per meal type to max 12. Timeout protection on top‑level generation (target ~45s; safe nanoseconds conversion).
  - Acceptance: Complex configs (e.g., 3 days; B2/L3/D4) complete without crash; partial failures are tolerated.

- FR‑MVP‑6 Prompt Fidelity (All Configs as Constraints)
  - Include in idea prompts: meal type, target dish count, total time, number of servings, restrictions/exclusions, equipment; include cuisines and additional request when provided.
  - Acceptance: Prompt content reflects available configuration; adapter still trims/expands to exact requested counts. Prompt contains meal‑type guidance (e.g., “Focus on Breakfast recipes”) when a meal type is selected.

- FR‑MVP‑7 Performance Guardrail (Minimal)
  - Disable recipe detail prefetch for the structured Meal Plan path only (keep Quick unchanged).
  - Acceptance: Meal Plan performs zero detail prefetch calls; plan content and slot assignment remain unchanged.


- FR‑MVP‑8 Configuration Change Detection (Stale Navigation Fix)
  - Implement generation fingerprinting to detect when user changes configurations (meals, days, per‑meal configs, cuisines, additional request).
  - Prevent auto‑navigation to stale results when configurations differ; only navigate to Plans after a successful fresh generation.
  - Store the last fingerprint in UserDefaults and invalidate/overwrite it on new generation.
  - Acceptance: Changing any configuration triggers actual generation (spinner visible); navigation occurs only after fresh generation completes; no jumps to previous results.

### FR‑EXT (Extended, Non‑Blocking Overlap UX)
- Recent‑Batch Highlights (TTL = 36h)
  - Only the latest generation batch is highlighted for up to 36 hours (configurable 24–48h). Calendar: overlay “+n” badges per day × meal for the latest batch. Day list rows: small “New” pill for latest batch. Manage view: “Show only new” filter.
  - Acceptance: New highlights auto‑replace previous highlights; auto‑expire after TTL.

- Soft Cap + Overflow Pool (Non‑Blocking)
  - Soft cap per day × meal (default 6, configurable). Items beyond the cap go to an overflow/suggestions pool instead of the visible list. Calendar/day card shows: “Lunch 6 (Full) · Suggestions +n”. Day view opens a medium‑detent sheet to manage overflow.
  - Acceptance: Visible list never exceeds the cap; overflow items are accessible and manageable.

- De‑dup & Recency Diversification
  - Strict de‑dup: the same recipe ID cannot appear more than once in the same day × meal list; extras go to overflow. Recency rule: recipes that appeared in the past 14 days are diverted to overflow by default (still user‑addable).
  - Acceptance: No duplicate IDs in a single day × meal list; recency diversion works; users can still include items from overflow.

- Frequent‑Overlap Non‑Blocking Guard (Suggest “Replace Non‑Favorites”)
  - Trigger a top inline banner when, within 7 days, overlap runs ≥3 and ≥2 days exceed the cap. CTAs: “Replace Non‑Favorites (This Run)” vs “Keep Appending”.
  - Acceptance: Banner appears only under the threshold; choosing either CTA proceeds without blocking.

### FR‑PERF (Performance & Responsiveness)
- Disable Detail Prefetch in Meal Plan Path
  - Adapter `generate(…, prefetchDetails: Bool = true)` guarded by flag; Structured path passes false. Quick remains default true.
  - Acceptance: Meal Plan performs 0 prefetch calls per meal type; generated plan content unchanged.

- Parallelize Per‑Meal Generation (with Cap)
  - Use TaskGroup to run per‑meal batches concurrently (cap 2–3 if needed). Errors in one meal type do not fail the whole plan.
  - Acceptance: Wall‑clock ≈ max(single meal batch time), not sum; no data races.

- Keep MainActor Responsive & Offload Heavy Work
  - Do not await entire generation on MainActor; marshal only state updates. Offload PlanStore merge/encode to background; finalize atomic write on MainActor.
  - Acceptance: UI remains responsive; Cancel reacts within 500 ms; retention unchanged.

## 6) Non‑Functional Requirements
- Reliability: Zero crashes with any configuration; 100% availability for meal plan generation.
- Error Resilience: Partial failures per meal type are tolerated and surfaced gracefully.
- Data Integrity: Generated plans saved correctly with proper slot assignment; 4‑week retention preserved.
- Performance: Reduce perceived wait; AI calls drop from ≤10 to 4 in core scenario.
- Concurrency Safety: No data races; respect API rate limits with a small cap.

## 7) UX & Copy (HIG‑Aligned, Minimal)
- Any UI changes must comply with Apple's iOS Human Interface Guidelines (https://developer.apple.com/design/human-interface-guidelines/designing-for-ios/).
- Keep calendar layout unchanged. Add information via overlays and inline elements only; avoid layout shifts.
- Start Date: system DatePicker or compact control; helper text: “Start generating from this date.”
- Post-save inline banner example: “Added: Mon Lunch ×2, Tue Dinner ×1 (no items were replaced). View” (auto-dismiss ~3–5s; CTA navigates to the week).
- Highlights/Pills: overlay “+n New” per day × meal; small “New” pill on rows; accessible tap targets; Dynamic Type; semantic colors.

## 8) Data Model & Interfaces (Minimal)
- OverlapSaveSummary (returned by save/merge): `addedCountsByDayMeal: [Date: [MealType: Int]]` (used to compose the banner string). Optionally track batchId/expiresAt for latest‑batch highlighting.
- No new fields on stored plan required for MVP.

## 9) Engineering Changes (File‑Level Mapping)
- Features/RecipeGenerator/RecipeGeneratorViewModel.swift — track `userSelectedStartDate`; present post‑save banner; navigation to Plans (or via CTA); keep MainActor responsive.
- Services/RecipeRequestBuilder.swift — thread `startDate` into `MealPlanGenerationRequest`.
- Services/StructuredMealPlanGenerator.swift — per‑meal try/catch; cap count at 12; cancellation checks; call adapter with `prefetchDetails: false` for Meal Plan; optional TaskGroup parallelization.
- Services/RecipeServiceAdapter.swift — add `prefetchDetails: Bool = true`; guard prefetch block; propagate cuisines/additional; set meal type/count.
- Services/RecipeGenerationService.swift — idea prompt builder includes meal type, dish count; cuisines/additional lines when non‑empty; keep time/servings/restrictions/equipment.
- Services/PlanStore.swift — merge with AppendAll; compute/return OverlapSaveSummary; consider background offload for merge/encode with atomic final write.
- Views/SwipeableMealPlanCalendarView.swift — ensure date indexing formatter uses yyyy‑MM‑dd, UTC, en_US_POSIX; Manage slot collection works.




- Add configuration change detection plumbing
  - Models: `GenerationFingerprint` (Codable, Equatable) with encode/decode helpers for UserDefaults; include selectedMeals, days, per‑meal configs, cuisines, additionalRequest.
  - ViewModel: compute current fingerprint before generation; compare with last stored; only navigate on new generation; update stored fingerprint after success.

## 9A) Technical Implementation Details (Key Snippets)

```swift
// Models/GenerationFingerprint.swift
struct GenerationFingerprint: Codable, Equatable {
    let selectedMeals: Set<MealType>
    let days: Int
    let mealConfigs: [String: MealConfig] // String keys for JSON compatibility
    let cuisines: [String]
    let additionalRequest: String?

    static func current(from config: CustomConfiguration) -> Self {
        let mc = Dictionary(uniqueKeysWithValues: config.mealConfigurations.map { ($0.key.rawValue, $0.value) })
        return .init(
            selectedMeals: config.selectedMeals,
            days: config.days,
            mealConfigs: mc,
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest
        )
    }
}

// UserDefaults helpers (simple):
extension GenerationFingerprint {
    func store() { if let d = try? JSONEncoder().encode(self) { UserDefaults.standard.set(d, forKey: "lastGenerationFingerprint") } }
    static func load() -> Self? {
        guard let d = UserDefaults.standard.data(forKey: "lastGenerationFingerprint") else { return nil }
        return try? JSONDecoder().decode(Self.self, from: d)
    }
}

// ViewModel navigation guard (sketch)
let current = GenerationFingerprint.current(from: customConfiguration)
let last = GenerationFingerprint.load()
if last == current {
    // Config unchanged → allow quick navigate only if desired; default: force fresh generation
    // return (if explicitly reusing cache); otherwise proceed to generate
}
// Proceed with generation; on success:
current.store()
```

```swift
// Timeout helper with bounded nanoseconds (Utilities/AsyncTimeout.swift)
struct TimeoutError: Error {}
func withTimeout<T>(_ seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
    let s = min(max(seconds, 0.001), 600.0)
    let ns = UInt64(s * 1_000_000_000)
    return try await withThrowingTaskGroup(of: T.self) { g in
        g.addTask { try await operation() }
        g.addTask { try await Task.sleep(nanoseconds: ns); throw TimeoutError() }
        let result = try await g.next()!
        g.cancelAll()
        return result
    }
}
```

```swift
// StructuredMealPlanGenerator — per‑meal isolation (excerpt)
for (meal, total) in totalPerMeal {
    do {
        try Task.checkCancellation()
        let capped = min(total, 12)
        let uiModels = try await adapter.generate(using: requestFor(meal, count: capped),
                                                 cookingTimeMinutes: cfg.cookingTimeMinutes,
                                                 authService: authService,
                                                 prefetchDetails: false)
        generatedByMeal[meal] = uiModels
    } catch {
        print("❌ Failed to generate \(meal): \(error)")
        generatedByMeal[meal] = []
    }
}
```

## 10) Telemetry & Instrumentation
- Log timings: ViewModel enter/exit performGeneration; Adapter enter/exit generate (tag mealType and prefetch flag); Prefetch enter/exit (legacy path only).
- Metrics per run: number of selected meals, total requested dishes, total AI calls, wall‑clock time (overall and per meal), cancellation events.
- UX: banner impressions; CTA clicks; “Show only new” usage; overflow opens/actions.


- Fingerprinting & navigation signals: log current vs last fingerprints (match/mismatch), whether navigation is suppressed or allowed, and when the stored fingerprint is updated.
- Stale‑result prevention: emit an explicit event when navigation is skipped due to unchanged configuration.

## 11) Acceptance Criteria (Measurable)
- Start date honored; same‑day cutoffs observed; enumeration begins from selected date.
- Overlap append: original items preserved; new items visible on overlapping days; summary accurate.
- Manage shows the generated items; delete/favorite/share succeed.
- Stability: complex config (3 days; B2/L3/D4) completes without crash; partial meal failures tolerated; cap enforced.
- Prompt fidelity: prompts include appropriate meal‑type guidance and reflect dish count, cuisines, additional request.
- Performance: Lunch+Dinner@5 days → AI calls drop from ≤10 to 4; UI stays responsive; Cancel < 500 ms. Quick mode unchanged.


- Configuration changes: modifying meals/days/dish counts/cuisines/additional request triggers new generation; navigation occurs only after successful fresh generation; no stale result jumps.


## 11A) Test Plan & Benchmarking (From Investigation)
- Unit Tests
  - Fingerprint & Navigation: same-config run does not auto-navigate; changed-config triggers generation; fingerprint updated on success.
  - Adapter Prefetch Flag: Structured path never prefetches; Quick path does (spy or flag capture).
  - Prompt Fidelity: meal type line present when selected; "Generate N" uses targetDishCount; cuisines/additional lines included when non-empty.
  - Structured Isolation: per-meal try/catch prevents whole-run crash; cap=12 enforced; cancellation checks present.
  - Date Indexing: yyyy‑MM‑dd UTC keys round-trip; slots resolved for visible week.
  - Timeout Helper: operations exceeding threshold throw TimeoutError; nanoseconds conversion bounded.

- Integration Tests
  - Stability: 3 days; B2/L3/D4 completes without crash; partial meal failures tolerated.
  - Overlap Append: generate baseline 1–7; then start on day 5 for 5 days → 5–7 appended; 8–9 new; summary counts match.
  - Manage Flow: after generation, Manage lists expected items; delete/favorite/share succeed.
  - Cache Invalidation: change dish count from 1→2 → new generation occurs; no jump to previous results.
  - Start Date Enumeration: start date honored; same-day cutoffs observed.
  - Performance Scenario: 5 days, Lunch 3×@40, Dinner 4×@60 → AI calls drop from ≤10 to 4; wall‑clock decreases.
  - Cancel Responsiveness: Cancel interrupts mid‑generation (<500 ms); no leaked state.

- Benchmarking & Metrics
  - Capture: selected meal count; total requested dishes; ai_calls_total; prefetch_enabled; wall‑clock overall and per meal.
  - Success thresholds: calls reduce to 4 in performance scenario; ≥50% perceived wait reduction; no regressions in Quick mode.

## 12) Milestones
- MVP (Days 1–3)
  - Day 1: Start date plumbing; adapter prefetch flag; structured call‑site updated.
  - Day 2: Per‑meal try/catch + cap; OverlapSaveSummary; Manage indexing fix; post‑save banner.
  - Day 3: Prompt fidelity lines; tests; polish and QA pass.
- Performance (Days 4–6)
  - Day 4: Disable prefetch (Meal Plan only) fully validated; unit tests.
  - Day 5: Parallelize per‑meal generation with cap; stop awaiting full task on MainActor; smoke tests.
  - Day 6: Offload PlanStore merge/encode; regression tests; optional prefetcher extraction.
- Extended UX (as bandwidth allows)
  - Highlights TTL; overflow pool; de‑dup/recency; non‑blocking guard banner; Manage "Show only new" filter.

## 13) Risks & Mitigations
- Parallel calls hit rate limit → cap concurrency (≤2–3); stagger starts by 200–300 ms.
- Hidden dependency on prefetch → confirm detail views load on demand; add smoke test.
- MainActor offload could introduce save ordering issues → keep final write atomic on MainActor; unit test merge determinism.
- UI noise from repeats → TTL on highlights; summary/toast throttling; overlay badges instead of repeated toasts.
- Stale navigation risk → enforce fingerprint comparison; navigate only after successful fresh generation; add telemetry for matches/mismatches.
- Timeout sensitivity → bound timeout helper (1 ms to 10 min) and surface user‑friendly error on timeout.
- Rollout risk → stage parallelization behind a feature flag; keep prefetch flag default true with structured path overriding to false.
- Observability gaps → add logs/metrics for per‑meal timings, idea count shortfalls, retries, and fingerprint state.


## 14) Open Questions
- Do we want per‑meal progress granularity in UI for Meal Plan?
- Should concurrency cap be configurable remotely (RemoteConfigurationManager)?
- Any A/B measurement windows for timing telemetry?

## 15) References
- Investigation content has been incorporated into this unified PRD; the standalone investigation doc has been removed.
- Legacy PRDs (baseline/MVP/extended) have been consolidated into this unified PRD.
- Key code paths: Features/RecipeGenerator/*, Services/StructuredMealPlanGenerator.swift, Services/RecipeServiceAdapter.swift, Services/RecipeGenerationService.swift, Services/PlanStore.swift, Utils/MealCutoffManager.swift

## 16) Rollout & Monitoring
- Feature flags and constants:
  - prefetchDetails default true; Structured path forces false.
  - concurrency cap (2–3) with optional remote config.
  - fingerprint gate (option to treat equal fingerprint as cache‑reuse vs force regenerate).
- Staged rollout (optional):
  - Enable parallelization and fingerprint enforcement for internal testers first; expand to 100% after stability.
- Monitoring & dashboards:
  - generation_started/completed timings; per‑meal timings; ai_calls_total; prefetch_enabled flag.
  - fingerprint_match/fingerprint_mismatch counts; navigations_suppressed_due_to_cache; timeout/error taxonomy.
- Alerting (basic):
  - Spike in timeouts or errors; drop in completion rate; surge in stale‑navigation suppressions.

