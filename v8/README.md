# V8 Meal Plan Generation Guidelines

## Project Overview
- Objective: eliminate crashes and performance bottlenecks in custom meal-plan generation, always trigger fresh runs when configurations change, and preserve the existing calendar UI.
- Delivery cadence: finish the MVP pass first (stability + prompt fidelity), then move on to performance improvements and optional UX enhancements.
- Expected outcomes: crash-free, per-meal generation that can execute in parallel; non-blocking post-save feedback; fewer AI calls; responsive UI while generation runs.

## Environment Constraints
- Language: Swift 5.9 with strict Swift Concurrency (async/await, actors, MainActor isolation).
- Platform: iOS 17.0 with SwiftUI UI layer.
- Build tooling: XcodeGen configuration (`project.yml`).
- Special rules: keep Quick mode behavior unchanged; do not alter server/API contracts; disable detail prefetching for the structured path by default.

## Coding Standards
- **Concurrency model**:
  - Keep the UI layer (`RecipeGeneratorViewModel`) on `@MainActor`; move long-running work off the main thread and avoid blocking via `await task.value` on the main actor.
  - Use actors (e.g., `StructuredMealPlanGenerator`) to isolate state. When parallelizing work, rely on bounded `TaskGroup`s and call `Task.checkCancellation()` inside loops.
- **Error isolation**: wrap per-meal requests in `do/catch`, return empty results on failure, and log the error without crashing the entire plan.
- **Resource caps**: enforce dish counts in the `1...12` range and cooking times in the `5...120` range. Follow the existing validation in `RecipeServiceAdapter` and `MealConfig`.
- **Prompt construction**: inject meal type, dish count, cook time, cuisines, additional request, servings, restrictions/allergies, and equipment into prompts. Keep Quick and Custom prompt wording aligned.
- **Slot enumeration & fingerprinting**: rely on `MealCutoffManager` to skip meals whose same-day cutoff already passed. Compare configuration fingerprints before navigating to results to avoid stale transitions.
- **PlanStore operations**:
  - Append-only merge: preserve existing slots, append new ones alongside them, retain favorites, and keep the four-week retention window with `yyyy-MM-dd` indexing.
  - Perform merge/encode work off the main actor and use `MainActor.run` for the final `UserDefaults` write to keep the save atomic.
- **Network calls**: pass `prefetchDetails: false` when the structured path calls `RecipeServiceAdapter`; leave Quick mode prefetching intact.
- **UI compliance**: align all UI modifications with Apple's iOS Human Interface Guidelines (https://developer.apple.com/design/human-interface-guidelines/designing-for-ios/); avoid bespoke patterns outside HIG.
- **Localization & accessibility**: add new strings through `Localizable.strings` with existing key conventions. Toasts/banners must remain non-blocking and accessible.

## Task Execution Principles
1. **MVP (Days 1-3)**
   - MVP-1: add a start-date picker (default today, range `today...today+7`), honor cutoffs, and thread the date through `RecipeRequestBuilder`.
   - MVP-2: call the append-only branch of `PlanStore.mergeAndSave` and surface the `OverlapSaveSummary` to the UI.
   - MVP-3: introduce a `prefetchDetails` flag on `RecipeServiceAdapter.generate`; disable prefetching for the structured flow.
   - MVP-4: harden per-meal error isolation, cap totals at 12 dishes, add cancellation checks and logging.
   - MVP-5: implement the reusable `withTimeout` helper, throw `TimeoutError` after 45 seconds, and handle it in the view model.
   - MVP-6: compute a configuration fingerprint (days, meals, dish counts, cooking times, cuisines, additional request, equipment, start date, etc.) and suppress stale navigation when fingerprints match.
   - MVP-7: present a non-blocking post-save summary (banner/toast) listing additions by day × meal.
   - MVP-8: fix Manage indexing by normalizing date keys to match the `slotsProvider` expectations and ensure Manage shows newly generated slots.
   - MVP-9: include every user-selected constraint (meal types, counts, times, cuisines, additional request, equipment, servings, restrictions) in prompt text.
   - MVP-10: back the MVP scope with unit tests (fingerprint guard, prefetch flag behavior, per-meal isolation, date indexing, timeout helper bounds).
2. **Performance (Days 4-6)**
   - PERF-1: permanently disable detail prefetching for meal-plan generation and cover it with tests.
   - PERF-2: parallelize per-meal generation via `TaskGroup`, limiting concurrency to 2–3 if required by rate limits.
   - PERF-3: stop awaiting the generation task on the main actor so the UI remains responsive; ensure cancellation responds in under 500 ms.
   - PERF-4: move `PlanStore` merge/encode work to a background context and finalize the write on the main actor.
   - PERF-5: add integration tests for the 5-day lunch+dinner performance scenario and verify AI calls drop to four.
3. **Extended UX (optional)**: highlights TTL, deduplication, non-blocking guard banners, Manage filtering, and other extras are stretch goals.

## Validation & Monitoring
- Mandatory scenarios: complex configuration (3 days; Breakfast 2 + Lunch 3 + Dinner 4), configuration change detection, overlap append (1–7 → 5–9), performance scenario (5-day Lunch + Dinner), Manage flow, and start-date/cutoff handling.
- Metrics to watch: `ai_calls_total`, generation duration, `fingerprint_match`/`fingerprint_mismatch`, cancel responsiveness, timeout/error taxonomy.
- Unit-test focus: fingerprint/navigation guardrails, prefetch flag behavior, prompt contents, per-meal try/catch isolation, date indexing, timeout helper boundaries.

## Key Code References
- `Features/RecipeGenerator/RecipeGeneratorView.swift` & `RecipeGeneratorViewModel.swift` – UI surface and state pipeline.
- `Services/StructuredMealPlanGenerator.swift` – structured generation, concurrency, cutoffs.
- `Services/RecipeServiceAdapter.swift` – preference assembly, prefetch control, fallback logic.
- `Services/PlanStore.swift` – append-only merge, retention, and overlap summary handling.
- `Utils/MealCutoffManager.swift` – same-day cutoff rules.
- `Utilities/AsyncTimeout.swift` (to be added/extended) – timeout utilities.

## Execution Reminders
- Follow task priority strictly (MVP → Performance → optional UX).
- Update or add tests for every change to satisfy PRD acceptance criteria.
- Assess Quick mode before shipping any change to guarantee no regressions.
- When adding files, preserve module layout (Features/Services/Utils) and keep comments concise.
