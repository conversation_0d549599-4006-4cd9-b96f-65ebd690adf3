import SwiftUI
import SwiftData
import UIKit
@preconcurrency import Firebase<PERSON>ore
@preconcurrency import GoogleSignIn

@main
struct IngredientScannerApp: App {
    @Environment(\.scenePhase) private var scenePhase
    @State private var serviceContainer: ServiceContainer
    private let swiftDataStorage = SwiftDataStorageService.shared

    init() {
        // Configure Firebase FIRST - this must happen before any Firebase services are accessed
        FirebaseApp.configure()
        
        // Configure Google Sign-In using Firebase default app options.clientID (per Firebase docs)
        if let clientId = FirebaseApp.app()?.options.clientID {
            GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientId)
            print("✅ Firebase and Google Sign-In configured successfully")
        } else {
            print("⚠️ Failed to read clientID from FirebaseApp options")
        }

        // Initialize ServiceContainer AFTER Firebase is fully configured
        serviceContainer = ServiceContainer.shared

        // Ensure notification center delegate is set early
        _ = ExpirationNotificationManager.shared
    }

    var body: some Scene {
        WindowGroup {
            // Use the RootView with proper service injection
            RootView(pantryService: serviceContainer.pantryService)
                .environment(serviceContainer.pantryService)
                .environment(serviceContainer.authenticationService)
                .environment(swiftDataStorage)
                .environment(ExpirationAlertManager.shared)
                .onOpenURL { url in
                    // Handle Google Sign-In URL callbacks as per Firebase documentation
                    _ = GIDSignIn.sharedInstance.handle(url)
                }
                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("openDeepLink"))) { note in
                    if let urlString = (note.userInfo?["url"]) as? String, let url = URL(string: urlString) {
                        _ = ServiceContainer.shared.authenticationService // keep ServiceContainer alive
                        let coordinator = NavigationCoordinator()
                        coordinator.handleDeepLink(url)
                    }
                }
                .task {
                    // App launched: run a background check once at startup
                    let pantry = serviceContainer.pantryService
                    await ExpirationNotificationManager.shared.checkAndScheduleAlerts(pantryItems: pantry.pantryItems)
                }
                #if DEBUG
                .task { await runUITestHooksIfRequested() }
                .onChange(of: scenePhase) { oldValue, newValue in
                    if newValue == .active { Task { await runUITestHooksIfRequested() } }
                }
                #endif
        }
        .modelContainer(swiftDataStorage.container ?? createFallbackContainer())
    }

    #if DEBUG
    private func runUITestHooksIfRequested() async {
        let env = ProcessInfo.processInfo.environment
        guard env["UITEST_MODE"] == "1" else { return }

        // Small delay to ensure services & storage are ready
        try? await Task.sleep(nanoseconds: 1_200_000_000)
        let pantry = serviceContainer.pantryService

        let phase = env["UITEST_PHASE"] ?? ""
        let addName = env["UITEST_ADD_INGREDIENT"]
        let addCatRaw = env["UITEST_ADD_CATEGORY"] ?? "Other"
        let assertNameEnv = env["UITEST_ASSERT_INGREDIENT_EXISTS"]

        var result: String? = nil

        if phase.uppercased() == "ADD", let name = addName, !name.isEmpty {
            let category = PantryCategory(rawValue: addCatRaw) ?? .other
            let ing = Ingredient(name: name, category: category, dateAdded: Date())
            await pantry.addIngredient(ing)
            // Give time for state propagation
            try? await Task.sleep(nanoseconds: 200_000_000)
            UserDefaults.standard.set(name, forKey: "UITEST_LAST_ADDED_NAME")
            UserDefaults.standard.set(category.rawValue, forKey: "UITEST_LAST_ADDED_CATEGORY")
            UserDefaults.standard.set(true, forKey: "UITEST_ADD_DONE")
            result = "ADDED"
        } else if phase.uppercased() == "ASSERT" {
            let checkName = assertNameEnv ?? UserDefaults.standard.string(forKey: "UITEST_LAST_ADDED_NAME") ?? ""
            let exists = pantry.pantryItems.contains { $0.name.caseInsensitiveCompare(checkName) == .orderedSame }
            result = exists ? "PASS" : "FAIL"
            UserDefaults.standard.set(result, forKey: "UITEST_RESULT")
        }

        if let status = result, let docs = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
            let url = docs.appendingPathComponent("ui_test_result.txt")
            try? status.data(using: .utf8)?.write(to: url)
        }
    }
    #endif
    
    /// Create fallback ModelContainer if SwiftDataStorageService fails
    private func createFallbackContainer() -> ModelContainer {
        do {
            let schema = Schema([
                SavedRecipe.self,
                SavedIngredient.self,
                SavedUserPreferences.self,
                ShoppingListItem.self,
                RecipeHistory.self
            ])
            
            let modelConfiguration = ModelConfiguration(
                schema: schema,
                isStoredInMemoryOnly: true
            )
            
            return try ModelContainer(
                for: schema,
                configurations: [modelConfiguration]
            )
        } catch {
            fatalError("Failed to create fallback ModelContainer: \(error)")
        }
    }
}
