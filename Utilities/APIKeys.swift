import Foundation

enum APIKeys {
    static let googleVisionAPIKey: String = {
        if let path = Bundle.main.path(forResource: "api-keys", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let key = plist["GoogleVisionAPIKey"] as? String {
            return key
        }
        return "YOUR_GOOGLE_VISION_API_KEY_HERE"
    }()

    static let geminiAPIKey: String = {
        if let path = Bundle.main.path(forResource: "api-keys", ofType: "plist"),
           let plist = NSDictionary(contentsOfFile: path),
           let key = plist["GeminiAPIKey"] as? String {
            return key
        }
        return "YOUR_GEMINI_API_KEY_HERE"
    }()
}
