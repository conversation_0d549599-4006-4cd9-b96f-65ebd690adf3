import SwiftUI
import UIKit

// MARK: - Notifications for Enhanced Camera Integration
extension Notification.Name {
    static let imagePickerDidSelectImage = Notification.Name("imagePickerDidSelectImage")
}

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    let sourceType: UIImagePickerController.SourceType
    @Environment(\.presentationMode) private var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        // Graceful fallback: if camera requested but unavailable (e.g., Simulator), use photo library
        let effectiveSource: UIImagePickerController.SourceType = {
            if sourceType == .camera && !UIImagePickerController.isSourceTypeAvailable(.camera) {
                return .photoLibrary
            }
            return sourceType
        }()

        picker.sourceType = effectiveSource
        picker.delegate = context.coordinator

        // Configure camera settings for better compatibility
        if effectiveSource == .camera {
            // Use default camera device to avoid unsupported device warnings
            picker.cameraDevice = .rear
            picker.cameraCaptureMode = .photo

            // Only set camera flash mode if available
            if UIImagePickerController.isFlashAvailable(for: picker.cameraDevice) {
                picker.cameraFlashMode = .auto
            }

            // Disable video quality settings that might cause issues
            picker.videoQuality = .typeHigh
        }

        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
                // Post notification for enhanced camera integration
                NotificationCenter.default.post(name: .imagePickerDidSelectImage, object: image)
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
} 
