import SwiftUI

/// Static 7-day (Mon–Sun) × 3-meal (Breakfast/Lunch/Dinner) matrix.
/// Displays visual indicators and triggers selection callback for filled slots.
struct CalendarMatrixView: View {
    let weekDays: [Date]                // Monday-first 7 dates
    let slotsProvider: (Date, MealType) -> [MealSlot]
    let favoritesProvider: (String) -> Bool
    var onSelectSlot: (Date, MealType, [MealSlot]) -> Void

    init(
        weekDays: [Date],
        slotsProvider: @escaping (Date, MealType) -> [MealSlot],
        favoritesProvider: @escaping (String) -> Bool = { _ in false },
        onSelectSlot: @escaping (Date, MealType, [MealSlot]) -> Void
    ) {
        self.weekDays = weekDays
        self.slotsProvider = slotsProvider
        self.favoritesProvider = favoritesProvider
        self.onSelectSlot = onSelectSlot
    }

    private let meals: [MealType] = [.breakfast, .lunch, .dinner]

    var body: some View {
        VStack(spacing: 12) {
            // Header row: empty corner + meal names
            HStack(spacing: 12) {
                Text("")
                    .frame(width: 64, alignment: .leading)
                ForEach(meals, id: \.self) { meal in
                    Text(meal.displayName)
                        .font(.footnote.weight(.semibold))
                        .frame(width: 64)
                        .foregroundStyle(.secondary)
                }
            }

            // Rows: Mon–Sun
            ForEach(Array(weekDays.enumerated()), id: \.offset) { _, day in
                HStack(spacing: 12) {
                    Text(shortWeekday(for: day))
                        .font(.footnote.weight(.semibold))
                        .frame(width: 64, alignment: .leading)
                        .foregroundStyle(.primary)

                    ForEach(meals, id: \.self) { meal in
                        let slots = slotsProvider(day, meal)
                        let hasPlan = !slots.isEmpty
                        let anyFavorite = slots.contains { favoritesProvider($0.recipe.id) }

                        if hasPlan {
                            Button {
                                onSelectSlot(day, meal, slots)
                            } label: {
                                MealSlotIndicator(hasPlan: true, isFavorited: anyFavorite, isRegenerated: false)
                            }
                            .buttonStyle(.plain)
                            .accessibilityLabel("\(shortWeekday(for: day)), \(meal.displayName), has plan")
                            .frame(width: 64, height: 40)
                        } else {
                            MealSlotIndicator(hasPlan: false)
                                .accessibilityLabel("\(shortWeekday(for: day)), \(meal.displayName), no plan")
                                .frame(width: 64, height: 40)
                        }
                    }
                }
            }
        }
    }

    private func shortWeekday(for date: Date) -> String {
        let f = DateFormatter()
        f.locale = Locale.current
        f.dateFormat = "EEE" // Mon, Tue, ...
        return f.string(from: date)
    }
}

#Preview("CalendarMatrixView") {
    let calendar = Calendar.current
    let week = WeeklyMealPlan(referenceDate: Date(), calendar: calendar)
    return CalendarMatrixView(
        weekDays: week.days,
        slotsProvider: { _, _ in [] },
        favoritesProvider: { _ in false }
    ) { _, _, _ in }
    .padding()
}

