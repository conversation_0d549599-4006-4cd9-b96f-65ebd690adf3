import SwiftUI

struct QuickResultCardView: View {
    let recipe: RecipeUIModel
    let highlightMeal: MealType?
    var onDelete: (() -> Void)? = nil

    private var timeText: String? {
        guard let minutes = recipe.estimatedTime else { return nil }
        return "\(minutes) min"
    }

    private var ingredientsSummary: String? {
        let pantry = recipe.ingredientsFromPantry ?? []
        let additional = recipe.additionalIngredients ?? []
        let combined = (pantry + additional)
        guard !combined.isEmpty else { return nil }
        let maxShow = 6
        if combined.count <= maxShow {
            return combined.joined(separator: ", ")
        } else {
            let prefix = combined.prefix(maxShow).joined(separator: ", ")
            return prefix + ", …"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            // Clickable area opens detail
            NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: recipe)) {
                VStack(alignment: .leading, spacing: 10) {
                    HStack(alignment: .top, spacing: 12) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(recipe.title)
                                .font(.headline)
                                .foregroundStyle(.primary)
                                .lineLimit(2)
                            if let sub = recipe.subtitle, !sub.isEmpty {
                                Text(sub)
                                    .font(.subheadline)
                                    .foregroundStyle(.secondary)
                                    .lineLimit(1)
                            }
                        }
                        Spacer(minLength: 8)
                        if let timeText = timeText {
                            Label(timeText, systemImage: "clock")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }

                    RecipeCompatibilityChips(highlight: highlightMeal ?? recipe.mealType)

                    if let summary = ingredientsSummary {
                        Text(summary)
                            .font(.caption)
                            .foregroundStyle(.secondary)
                            .lineLimit(2)
                            .accessibilityLabel("Ingredients: \(summary)")
                    }
                }
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)

            // Actions outside of the NavigationLink so taps don't navigate
            HStack(spacing: 16) {
                FavoriteButton(recipeId: recipe.id)
                Button(role: .destructive) {
                    onDelete?()
                } label: {
                    Label("Delete", systemImage: "trash")
                        .font(.callout)
                }
                .buttonStyle(.borderless)
                Spacer()
            }
            .padding(.top, 2)
        }
        .padding(12)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.gray.opacity(0.15), lineWidth: 1)
        )
        .contentShape(Rectangle())
        .accessibilityElement(children: .contain)
        .accessibilityLabel(Text("Recipe card: \(recipe.title)"))
    }
}

#Preview("Quick Result Card") {
    QuickResultCardView(
        recipe: RecipeUIModel(
            id: UUID().uuidString,
            title: "Pasta Pesto",
            subtitle: "Fresh basil, pine nuts",
            estimatedTime: 25,
            ingredientsFromPantry: ["Pasta", "Basil", "Garlic"],
            additionalIngredients: ["Parmesan", "Pine Nuts"],
            mealType: .lunch
        ),
        highlightMeal: .lunch
    )
    .padding()
}
