import SwiftUI

/// Time-based compatibility chips for Breakfast / Lunch / Dinner
struct RecipeCompatibilityChips: View {
    let highlight: MealType?

    var body: some View {
        HStack(spacing: 8) {
            chip(.breakfast, label: "🌅 Breakfast")
            chip(.lunch, label: "☀️ Lunch")
            chip(.dinner, label: "🌙 Dinner")
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Time compatibility chips")
    }

    private func chip(_ type: MealType, label: String) -> some View {
        let isOn = (highlight == type)
        return Text(label)
            .font(.caption)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                Capsule().fill((isOn ? Color.accentColor.opacity(0.15) : Color.secondary.opacity(0.12)))
            )
            .overlay(
                Capsule().stroke(isOn ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: isOn ? 1 : 0.5)
            )
            .foregroundStyle(isOn ? Color.accentColor : .secondary)
            .accessibilityLabel("\(label)\(isOn ? ", selected" : "")")
    }
}

#Preview("Compatibility Chips") {
    VStack(alignment: .leading, spacing: 12) {
        RecipeCompatibilityChips(highlight: .breakfast)
        RecipeCompatibilityChips(highlight: .lunch)
        RecipeCompatibilityChips(highlight: .dinner)
        RecipeCompatibilityChips(highlight: nil)
    }
    .padding()
}

