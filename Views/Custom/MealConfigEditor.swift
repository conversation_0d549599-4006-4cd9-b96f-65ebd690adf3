import SwiftUI

struct MealConfigEditor: View {
    let meal: MealType
    @Binding var config: MealConfig

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(meal.displayName)
                    .font(.headline)
                Spacer()
            }
            HStack(spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    Text("Time (min)").font(.subheadline).foregroundStyle(.secondary)
                    Slider(value: Binding(
                        get: { Double(config.cookingTimeMinutes) },
                        set: { config.cookingTimeMinutes = Int($0) }
                    ), in: 5...120, step: 5) { Text("") } minimumValueLabel: {
                        Text("5")
                    } maximumValueLabel: {
                        Text("120")
                    }
                    Text("\(config.cookingTimeMinutes) min")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                Divider()
                VStack(alignment: .leading, spacing: 6) {
                    Text("Dishes").font(.subheadline).foregroundStyle(.secondary)
                    Stepper(value: $config.numberOfDishes, in: 1...6) {
                        Text("\(config.numberOfDishes)")
                    }
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .frame(minHeight: 44)
    }
}

#Preview {
    @Previewable @State var cfg = MealConfig(cookingTimeMinutes: 30, numberOfDishes: 2)
    MealConfigEditor(meal: .dinner, config: $cfg).padding()
}

