import SwiftUI

struct AdditionalRequestEditor: View {
    @Binding var text: String
    let maxCount: Int = 500

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Additional Requests").font(.headline)
            ZStack(alignment: .bottomTrailing) {
                TextEditor(text: $text)
                    .frame(minHeight: 120)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                    )
                Text("\(text.count)/\(maxCount)")
                    .font(.caption2)
                    .foregroundStyle(text.count > maxCount ? .red : .secondary)
                    .padding(8)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .onChange(of: text) { _, newValue in
            if newValue.count > maxCount {
                text = String(newValue.prefix(maxCount))
            }
        }
    }
}

#Preview {
    @Previewable @State var t = "No onions, please."
    AdditionalRequestEditor(text: $t).padding()
}

