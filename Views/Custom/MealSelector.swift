import SwiftUI

struct MealSelector: View {
    @Binding var selectedMeals: Set<MealType>

    private let columns = [GridItem(.adaptive(minimum: 72), spacing: 8)]

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Meals")
                .font(.headline)
            LazyVGrid(columns: columns, spacing: 8) {
                ForEach(MealType.allCases, id: \.self) { meal in
                    Toggle(isOn: Binding(
                        get: { selectedMeals.contains(meal) },
                        set: { isOn in
                            if isOn { selectedMeals.insert(meal) } else { selectedMeals.remove(meal) }
                            UIImpactFeedbackGenerator(style: .light).impactOccurred()
                        }
                    )) {
                        Text(meal.displayName)
                            .padding(.vertical, 8)
                            .frame(maxWidth: .infinity)
                    }
                    .toggleStyle(.button)
                    .buttonBorderShape(.capsule)
                    .tint(.accentColor)
                    .frame(minHeight: 44)
                    .accessibilityLabel(Text(meal.displayName))
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

#Preview {
    @Previewable @State var selected: Set<MealType> = [.breakfast]
    MealSelector(selectedMeals: $selected)
        .padding()
}

