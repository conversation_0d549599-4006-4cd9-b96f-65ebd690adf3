import SwiftUI

/// Multi-select chips for cuisines (Phase 1)
struct CuisinesChips: View {
    @Binding var selected: [String]
    var allCuisines: [String] {
        RemoteConfigurationManager.shared.configuration.availableCuisines
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cuisines").font(.headline)
            FlowLayout(alignment: .leading, spacing: 8) {
                ForEach(allCuisines, id: \.self) { cuisine in
                    let isSelected = selected.contains(cuisine)
                    Button(action: {
                        if isSelected {
                            selected.removeAll { $0 == cuisine }
                        } else {
                            selected.append(cuisine)
                        }
                    }) {
                        Text(cuisine)
                            .font(.subheadline)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(isSelected ? Color.accentColor.opacity(0.2) : Color.secondary.opacity(0.1))
                            .foregroundColor(isSelected ? .accentColor : .primary)
                            .clipShape(Capsule())
                    }
                    .buttonStyle(.plain)
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

/// Minimal flow layout for chips - simplified version compatible with Swift 6
struct FlowLayout<Content: View>: View {
    let alignment: HorizontalAlignment
    let spacing: CGFloat
    @ViewBuilder var content: () -> Content

    var body: some View {
        // Use a simple wrapping approach that's Swift 6 compatible
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 80), spacing: spacing)
        ], alignment: .leading, spacing: spacing) {
            content()
        }
    }
}

