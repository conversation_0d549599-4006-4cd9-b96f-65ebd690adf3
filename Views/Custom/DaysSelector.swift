import SwiftUI

struct DaysSelector: View {
    @Binding var days: Int
    var maxDays: Int = 7
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Days")
                .font(.headline)
            Picker("Days", selection: $days) {
                ForEach(1...maxDays, id: \.self) { d in
                    Text("\(d)")
                        .tag(d)
                }
            }
            .pickerStyle(.segmented)
            .frame(minHeight: 44)
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

#Preview {
    @Previewable @State var days = 3
    DaysSelector(days: $days)
        .padding()
}

