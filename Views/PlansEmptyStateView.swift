import SwiftUI

struct PlansEmptyStateView: View {
    @Environment(NavigationCoordinator.self) private var coordinator: NavigationCoordinator

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar")
                .font(.system(size: 48))
                .foregroundStyle(.secondary)

            Text("Plan your week with ease")
                .font(.title3.weight(.semibold))
                .multilineTextAlignment(.center)

            Text("Create a structured 7-day or 3-day meal plan with just a tap.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundStyle(.secondary)

            VStack(spacing: 8) {
                Button(action: { openGeneratorPrefilled(days: 7) }) {
                    Label("Create 7-day Plan", systemImage: "calendar.badge.plus")
                        .labelStyle(.titleAndIcon)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)

                Button(action: { openGeneratorPrefilled(days: 3) }) {
                    Label("Create 3-day Plan", systemImage: "calendar.badge.plus")
                        .labelStyle(.titleAndIcon)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .controlSize(.large)
            }
        }
        .padding(24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Plans empty state")
    }

    private func openGeneratorPrefilled(days: Int) {
        // Prefill for Generator: mode=custom (Meal Plans), days, default meals (lunch + dinner)
        let defaults = UserDefaults.standard
        defaults.set("custom", forKey: "generator.prefill.mode")
        defaults.set(days, forKey: "generator.prefill.days")
        defaults.set([MealType.lunch.rawValue, MealType.dinner.rawValue], forKey: "generator.prefill.meals")
        coordinator.switchToTab(2) // Open Generator tab
    }
}

#Preview {
    NavigationStack { PlansEmptyStateView() }
        .environment(NavigationCoordinator())
}

