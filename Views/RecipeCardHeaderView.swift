import SwiftUI

struct RecipeCardHeaderView: View {
    let date: Date
    let mealType: MealType
    let dishCount: Int

    private var dateText: String {
        let f = DateFormatter()
        f.dateStyle = .medium
        f.timeStyle = .short
        return f.string(from: date)
    }

    private var subtitleText: String {
        "\(mealType.displayName) · \(dishCount) dish\(dishCount == 1 ? "" : "es")"
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text(dateText)
                .font(.headline)
            Text(subtitleText)
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Generated on \(dateText), \(subtitleText)")
    }
}

#Preview("Recipe Card Header") {
    RecipeCardHeaderView(date: Date(), mealType: .dinner, dishCount: 3)
        .padding()
}

