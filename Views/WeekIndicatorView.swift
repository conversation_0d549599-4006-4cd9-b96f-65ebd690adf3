import SwiftUI

/// Displays a formatted week range with optional previous/next controls.
/// Example title: "Jan 1–7, 2025"
struct WeekIndicatorView: View {
    let title: String
    let canGoPrevious: Bool
    let canGoNext: Bool
    let onPrevious: () -> Void
    let onNext: () -> Void

    init(
        title: String,
        canGoPrevious: Bool = true,
        canGoNext: Bool = true,
        onPrevious: @escaping () -> Void,
        onNext: @escaping () -> Void
    ) {
        self.title = title
        self.canGoPrevious = canGoPrevious
        self.canGoNext = canGoNext
        self.onPrevious = onPrevious
        self.onNext = onNext
    }

    var body: some View {
        HStack(spacing: 12) {
            But<PERSON>(action: onPrevious) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 15, weight: .semibold))
            }
            .disabled(!canGoPrevious)
            .accessibilityLabel("Previous week")

            Text(title)
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .center)

            But<PERSON>(action: onNext) {
                Image(systemName: "chevron.right")
                    .font(.system(size: 15, weight: .semibold))
            }
            .disabled(!canGoNext)
            .accessibilityLabel("Next week")
        }
    }
}

#Preview("WeekIndicatorView") {
    WeekIndicatorView(
        title: "Jan 1–7, 2025",
        canGoPrevious: true,
        canGoNext: false,
        onPrevious: {},
        onNext: {}
    ).padding()
}

