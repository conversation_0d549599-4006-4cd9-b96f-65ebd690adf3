import SwiftUI

/// Collapsed summary view for a week: shows per-day meal counts (0–3).
/// Example: 3 2 4 1 2 3 2
struct CollapsedCalendarView: View {
    let weekDays: [Date]                // Monday-first 7 dates
    let slotsProvider: (Date, MealType) -> [MealSlot]

    init(
        weekDays: [Date],
        slotsProvider: @escaping (Date, MealType) -> [MealSlot]
    ) {
        self.weekDays = weekDays
        self.slotsProvider = slotsProvider
    }

    private let meals: [MealType] = [.breakfast, .lunch, .dinner]

    var body: some View {
        HStack(spacing: 10) {
            ForEach(weekDays, id: \.self) { day in
                let count = mealCount(for: day)
                VStack(spacing: 6) {
                    Text(shortWeekday(for: day))
                        .font(.caption2.weight(.semibold))
                        .foregroundStyle(.secondary)
                        .frame(maxWidth: .infinity)
                    Text("\(count)")
                        .font(.headline.weight(.bold))
                        .frame(width: 28, height: 28)
                        .background(.thinMaterial, in: RoundedRectangle(cornerRadius: 6, style: .continuous))
                        .overlay(
                            RoundedRectangle(cornerRadius: 6, style: .continuous)
                                .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                        )
                        .accessibilityLabel("\(shortWeekday(for: day)) has \(count) meals")
                }
                .frame(maxWidth: .infinity)
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12, style: .continuous)
                .fill(.ultraThinMaterial)
        )
    }

    private func mealCount(for date: Date) -> Int {
        meals.reduce(0) { acc, meal in
            acc + (slotsProvider(date, meal).isEmpty ? 0 : 1)
        }
    }

    private func shortWeekday(for date: Date) -> String {
        let f = DateFormatter()
        f.locale = Locale.current
        f.dateFormat = "EEE" // Mon, Tue, ...
        return f.string(from: date)
    }
}

#Preview("CollapsedCalendarView") {
    let cal = Calendar.current
    let week = WeeklyMealPlan(referenceDate: Date(), calendar: cal)
    return CollapsedCalendarView(
        weekDays: week.days,
        slotsProvider: { _, _ in [] }
    )
    .padding()
}

