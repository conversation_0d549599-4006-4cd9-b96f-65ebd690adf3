import SwiftUI

struct HorizontalQuickResultsView: View {
    let items: [QuickResultHistory]
    @Binding var currentIndex: Int
    @Environment(\.accessibilityReduceMotion) private var reduceMotion

    // Virtualization window: render near pages, lightweight placeholders otherwise
    private let windowRadius: Int = 2

    var body: some View {
        TabView(selection: $currentIndex) {
            ForEach(items.indices, id: \.self) { idx in
                Group {
                    if abs(currentIndex - idx) <= windowRadius {
                        QuickResultPageView(item: items[idx])
                    } else {
                        // Lightweight placeholder to limit memory use
                        VStack {
                            Spacer()
                            ProgressView()
                                .progressViewStyle(.circular)
                            Text("Loading…")
                                .font(.footnote)
                                .foregroundStyle(.secondary)
                                .padding(.top, 4)
                            Spacer()
                        }
                        .accessibilityLabel("Loading page \(idx + 1)")
                    }
                }
                .tag(idx)
                .padding(.bottom, 8) // breathing room above indicator
            }
        }
        .tabViewStyle(.page(indexDisplayMode: .never))
        .animation(reduceMotion ? nil : .easeInOut(duration: 0.2), value: currentIndex)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Quick history horizontal pager")
    }
}

#Preview("Horizontal Pager") {
    @Previewable @State var index = 0
    let sampleItems: [QuickResultHistory] = (0..<5).map { n in
        QuickResultHistory(
            mealType: .dinner,
            numberOfDishes: 2,
            totalCookTime: 40,
            cuisines: ["Fusion"],
            additionalRequest: nil,
            recipes: [
                .init(id: UUID().uuidString, title: "Dish #\(n) - A", subtitle: "Subtitle A", estimatedTime: 20),
                .init(id: UUID().uuidString, title: "Dish #\(n) - B", subtitle: "Subtitle B", estimatedTime: 15)
            ]
        )
    }
    return NavigationStack {
        VStack(spacing: 8) {
            HorizontalQuickResultsView(items: sampleItems, currentIndex: $index)
            PageIndicatorView(count: sampleItems.count, currentIndex: index, mode: .dots)
        }
        .padding(.top)
    }
}
