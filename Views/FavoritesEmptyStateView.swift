import SwiftUI

struct FavoritesEmptyStateView: View {
    @AppStorage("recipes.selectedTab") private var selectedRecipesTab: String = "quick"

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "heart")
                .font(.system(size: 48))
                .foregroundStyle(.secondary)

            Text("No favorites yet")
                .font(.title3.weight(.semibold))
                .multilineTextAlignment(.center)

            Text("Browse your Quick results or Plans to add favorites.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundStyle(.secondary)

            HStack(spacing: 12) {
                But<PERSON>(action: { selectedRecipesTab = "quick" }) {
                    Label("Browse Quick", systemImage: "clock")
                        .labelStyle(.titleAndIcon)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)

                Button(action: { selectedRecipesTab = "plans" }) {
                    Label("Browse Plans", systemImage: "calendar")
                        .labelStyle(.titleAndIcon)
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
            }
            .controlSize(.large)
        }
        .padding(24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Favorites empty state")
    }
}

#Preview {
    NavigationStack { FavoritesEmptyStateView() }
}

