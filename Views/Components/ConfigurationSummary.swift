import SwiftUI

struct ConfigurationSummary: View {
    @Binding var days: Int
    @Binding var selectedMeals: Set<MealType>
    
    var body: some View {
        let text = SummaryGenerator.shared.summaryText(days: days, meals: selectedMeals)
        return Text(text)
            .font(.body)
            .foregroundStyle(.primary)
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 12, style: .continuous)
                    .fill(Color(uiColor: .secondarySystemGroupedBackground))
            )
            .accessibilityLabel(Text("configuration_summary"))
            .accessibilityHint(Text("configuration_summary_hint"))
            .animation(.easeInOut, value: days)
            .animation(.easeInOut, value: selectedMeals)
    }
}

#Preview {
    @Previewable @State var days = 3
    @Previewable @State var meals: Set<MealType> = [.lunch, .dinner]
    ConfigurationSummary(days: $days, selectedMeals: $meals)
        .padding()
}

