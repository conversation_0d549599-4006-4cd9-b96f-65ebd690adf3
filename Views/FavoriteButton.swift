import SwiftUI

struct FavoriteButton: View {
    let recipeId: String
    @State private var isFav: Bool = false

    var body: some View {
        Button(action: toggle) {
            Image(systemName: isFav ? "heart.fill" : "heart")
                .foregroundStyle(isFav ? .red : .primary)
                .accessibilityLabel(isFav ? "Remove from favorites" : "Add to favorites")
        }
        .onAppear { isFav = FavoritesManager.shared.isFavorite(id: recipeId) }
        .accessibilityAddTraits(.isButton)
    }

    private func toggle() {
        FavoritesManager.shared.toggleFavorite(id: recipeId)
        isFav.toggle()
    }
}

#Preview("Favorite Button") {
    FavoriteButton(recipeId: UUID().uuidString)
}

