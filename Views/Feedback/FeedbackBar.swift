import SwiftUI

struct FeedbackBar: View {
    var onLike: (() -> Void)?
    var onDislike: (() -> Void)?

    var body: some View {
        HStack(spacing: 16) {
            <PERSON><PERSON>(action: { onLike?() }) {
                Label("Like", systemImage: "hand.thumbsup")
            }
            .buttonStyle(.bordered)

            Button(action: { onDislike?() }) {
                Label("Dislike", systemImage: "hand.thumbsdown")
            }
            .buttonStyle(.bordered)
        }
        .frame(maxWidth: .infinity)
        .padding(.top, 4)
    }
}

#Preview {
    FeedbackBar()
}

