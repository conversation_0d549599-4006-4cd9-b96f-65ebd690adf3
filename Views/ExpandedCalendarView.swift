import SwiftUI

/// Expanded calendar view: full 7×3 matrix with a trailing Manage button.
struct ExpandedCalendarView: View {
    let weekDays: [Date]
    let slotsProvider: (Date, MealType) -> [MealSlot]
    let favoritesProvider: (String) -> Bool
    var onSelectSlot: (Date, MealType, [MealSlot]) -> Void
    var onTapManage: () -> Void

    init(
        weekDays: [Date],
        slotsProvider: @escaping (Date, MealType) -> [MealSlot],
        favoritesProvider: @escaping (String) -> Bool = { _ in false },
        onSelectSlot: @escaping (Date, MealType, [MealSlot]) -> Void,
        onTapManage: @escaping () -> Void
    ) {
        self.weekDays = weekDays
        self.slotsProvider = slotsProvider
        self.favoritesProvider = favoritesProvider
        self.onSelectSlot = onSelectSlot
        self.onTapManage = onTapManage
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Spacer()
                But<PERSON>(action: onTapManage) {
                    Label("Manage", systemImage: "slider.horizontal.3")
                        .labelStyle(.titleAndIcon)
                }
                .buttonStyle(.bordered)
                .accessibilityLabel("Manage selections")
            }
            .padding(.horizontal)

            CalendarMatrixView(
                weekDays: weekDays,
                slotsProvider: slotsProvider,
                favoritesProvider: favoritesProvider,
                onSelectSlot: onSelectSlot
            )
            .padding(.horizontal)
        }
    }
}

#Preview("ExpandedCalendarView") {
    let cal = Calendar.current
    let week = WeeklyMealPlan(referenceDate: Date(), calendar: cal)
    return ExpandedCalendarView(
        weekDays: week.days,
        slotsProvider: { _, _ in [] },
        favoritesProvider: { _ in false },
        onSelectSlot: { _, _, _ in },
        onTapManage: {}
    )
    .padding()
}

