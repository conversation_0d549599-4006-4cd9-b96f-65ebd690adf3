import SwiftUI

struct QuickEmptyStateView: View {
    @Environment(NavigationCoordinator.self) private var coordinator: NavigationCoordinator

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "clock.badge.questionmark")
                .font(.system(size: 48))
                .foregroundStyle(.secondary)

            Text("No Quick results yet")
                .font(.title3.weight(.semibold))
                .multilineTextAlignment(.center)
                .foregroundStyle(.primary)

            Text("Generate a quick set of recipe ideas based on your pantry.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundStyle(.secondary)

            Button(action: { coordinator.switchToTab(2) }) {
                Label(NSLocalizedString("a11y_generate", comment: "Generate"), systemImage: "wand.and.stars")
                    .labelStyle(.titleAndIcon)
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
            .accessibilityHint(NSLocalizedString("a11y_generate_hint", comment: "Generate recipe suggestions."))
        }
        .padding(24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .accessibilityLabel("Quick empty state")
    }
}

#Preview {
    NavigationStack { QuickEmptyStateView() }
        .environment(NavigationCoordinator())
}

