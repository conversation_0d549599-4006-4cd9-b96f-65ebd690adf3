import SwiftUI

enum PageIndicatorMode {
    case dots
    case counter
}

struct PageIndicatorView: View {
    let count: Int
    let currentIndex: Int
    var mode: PageIndicatorMode = .dots

    var body: some View {
        HStack(spacing: 8) {
            switch mode {
            case .dots:
                HStack(spacing: 6) {
                    ForEach(0..<count, id: \.self) { idx in
                        Circle()
                            .fill(idx == currentIndex ? Color.primary : Color.secondary.opacity(0.3))
                            .frame(width: 6, height: 6)
                            .accessibilityHidden(true)
                    }
                }
            case .counter:
                Text("\(min(max(currentIndex + 1, 1), max(count, 1)))/\(max(count, 1))")
                    .font(.subheadline.monospacedDigit())
                    .foregroundStyle(.secondary)
            }
        }
        .accessibilityLabel("Page \(currentIndex + 1) of \(max(count, 1))")
    }
}

#Preview("Page Indicator — Dots") {
    VStack(spacing: 12) {
        PageIndicatorView(count: 5, currentIndex: 0, mode: .dots)
        PageIndicatorView(count: 5, currentIndex: 2, mode: .dots)
        PageIndicatorView(count: 5, currentIndex: 4, mode: .dots)
    }
    .padding()
}

#Preview("Page Indicator — Counter") {
    PageIndicatorView(count: 10, currentIndex: 3, mode: .counter)
        .padding()
}

