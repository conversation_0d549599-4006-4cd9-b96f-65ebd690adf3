/* Sign-in button text */
"Sign in" = "Iniciar sesión";

/* Long form sign-in button text */
"Sign in with Google" = "Iniciar sesión con Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "Aceptar";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancelar";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Configuración";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "No se ha podido iniciar sesión en la cuenta";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "El administrador requiere que configures una contraseña en este dispositivo para acceder a esta cuenta. Inténtalo de nuevo cuando lo hayas hecho.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "El dispositivo no cumple la política de privacidad que ha definido tu administrador.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "¿Has conectado tu dispositivo con la aplicación Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Para proteger los datos de tu organización, debes conectar tu dispositivo con la aplicación Device Policy antes de iniciar sesión.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Conectar";
