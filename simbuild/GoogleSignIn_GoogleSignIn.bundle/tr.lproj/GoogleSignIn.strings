/* Sign-in button text */
"Sign in" = "Oturum aç";

/* Long form sign-in button text */
"Sign in with Google" = "Google ile oturum aç";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "Tamam";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "İptal";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Ayarlar";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Hesapta oturum açılamıyor";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Yöneticiniz, bu hesaba erişmek için bu cihazda bir şifre kodu ayarlamanızı gerektiriyor. Lütfen şifre kodu ayarlayın ve tekrar deneyin.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Bu cihaz, yöneticinizin ayarladığı güvenlik politikasıyla uyumlu değil.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Cihaz Politika Uygulamasına bağlanılsın mı?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Kuruluşunuzun verilerini korumak için, giriş yapmadan önce Cihaz Politikası uygulamasına bağlanmalısınız.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Bağlan";
