/* Sign-in button text */
"Sign in" = "Conectați-vă";

/* Long form sign-in button text */
"Sign in with Google" = "Conectați-vă cu Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Anulați";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Setări";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Nu vă puteți conecta la cont";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Administratorul impune să setați o parolă pe acest dispozitiv ca să accesați contul. Setați o parolă și încercați din nou.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Dispozitivul nu respectă politica de securitate stabilită de administrator.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Vă conectați cu aplicația Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Pentru a vă proteja datele organizației, trebuie să vă conectați cu aplicația Device Policy înainte de a vă conecta.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Conectați";
