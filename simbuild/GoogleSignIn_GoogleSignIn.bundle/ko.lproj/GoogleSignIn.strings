/* Sign-in button text */
"Sign in" = "로그인";

/* Long form sign-in button text */
"Sign in with Google" = "Google 계정으로 로그인";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "확인";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "취소";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "설정";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "계정에 로그인할 수 없음";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "관리자의 설정에 따라 이 계정에 액세스하려면 사용 중인 기기에 비밀번호를 설정해야 합니다. 비밀번호를 설정한 후 다시 시도해 주세요.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "관리자가 설정한 보안 정책을 준수하지 않는 기기입니다.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Device Policy 앱과 연결하시겠습니까?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "조직의 데이터를 보호하려면 로그인하기 전에 Device Policy 앱과 연결해야 합니다.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "연결";
