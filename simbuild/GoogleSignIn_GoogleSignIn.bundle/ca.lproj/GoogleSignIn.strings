/* Sign-in button text */
"Sign in" = "Inicia la sessió";

/* Long form sign-in button text */
"Sign in with Google" = "Inicia la sessió amb Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "D’acord";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Cancel·la";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Configuració";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "No es pot iniciar la sessió al compte";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "L'administrador requereix que estableixis una contrasenya en aquest dispositiu per accedir al compte. Estableix una contrasenya i torna-ho a provar.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "El dispositiu no compleix la política de seguretat establerta pel teu administrador.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Vols connectar-te amb l'aplicació Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Per protegir les dades de la teva organització, t'has de connectar amb l'aplicació Device Policy abans d'iniciar la sessió.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Vull connectar-me";
