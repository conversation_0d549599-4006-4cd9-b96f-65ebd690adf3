/* Sign-in button text */
"Sign in" = "Prihlásiť sa";

/* Long form sign-in button text */
"Sign in with Google" = "Prihlásiť sa pomocou účtu Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Zrušiť";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Nastavenia";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Nedá sa prihlásiť do účtu";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Správca vyžaduje, aby ste v tomto zariadení nastavili vstupný kód na prístup do príslušného účtu. Nastavte vstupný kód a skúste to znova.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Zariadenie nespĺňa pravidlá zabezpečenia nastavené vaším správcom.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Prepojiť s aplikáciou Pravidlá pre zariadenie?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Na to, aby bolo možné chrániť dáta vašej organizácie, je nutné pred prihlásením aktivovať prepojenie s aplikáciou Pravidlá pre zariadenie.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Prepojiť";
