/* Sign-in button text */
"Sign in" = "Log ind";

/* Long form sign-in button text */
"Sign in with Google" = "Log ind med Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Annuller";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Indstillinger";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Der kunne ikke logges ind på kontoen";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Din administrator kræver, at du angiver en adgangskode på enheden for at få adgang til kontoen. Angiv en adgangskode, og prøv igen.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Enheden overholder ik<PERSON> den sikkerhedspolitik, der er angivet af din administrator.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Vil du oprette forbindelse til appen Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Du skal oprette forbindelse til appen Device Policy, inden du logger ind, for at beskytte din organisations data.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Opret forbindelse";
