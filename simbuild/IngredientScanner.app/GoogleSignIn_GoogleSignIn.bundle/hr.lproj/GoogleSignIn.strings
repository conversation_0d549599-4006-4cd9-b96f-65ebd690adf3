/* Sign-in button text */
"Sign in" = "<PERSON>ri<PERSON><PERSON>";

/* Long form sign-in button text */
"Sign in with Google" = "Prijavite se putem Googlea";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "U redu";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Odbaci";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Postavke";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Prijava na račun nije moguća";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Vaš administrator zahtijeva da postavite šifru zaporke na ovom uređaju da biste pristupili računu. Postavite šifru zaporke i pokušajte ponovo.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Uređaj nije usklađen sa sigurnosnim pravilima koja je postavio vaš administrator.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Želite li se povezati s aplikacijom Pravila za uređaje?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Da biste zaštitili podatke svoje organizacije, morate se povezati s aplikacijom Pravila za uređaje prije prijave.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Poveži";
