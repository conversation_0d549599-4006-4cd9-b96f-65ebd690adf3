/* Sign-in button text */
"Sign in" = "Zaloguj się";

/* Long form sign-in button text */
"Sign in with Google" = "Zaloguj się przez Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "OK";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Anuluj";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Ustawienia";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Nie można zalogować się na konto";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Administrator wymaga ustawienia kodu dostępu do konta na tym urządzeniu. Ustaw kod dostępu i spróbuj ponownie.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Urządzenie nie jest zgodne z zasadami bezpieczeństwa ustanowionymi przez Twojego administratora.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Połączyć z aplikacją Device Policy?";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Aby chronić dane organizacji, przed zalogowaniem musisz się połączyć z aplikacją Device Policy.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Połącz";
