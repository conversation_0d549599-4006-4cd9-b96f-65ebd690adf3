/* Sign-in button text */
"Sign in" = "Σύνδεση";

/* Long form sign-in button text */
"Sign in with Google" = "Συνδεθείτε με το Google";

/* The text for the button for user to acknowledge and dismiss a dialog. */
"OK" = "ΟΚ";

/* The text for the button for user to dismiss a dialog without taking any action. */
"Cancel" = "Άκυρο";

/* The name of the iOS native "Settings" app. */
"SettingsAppName" = "Ρυθμίσεις";

/* The title for the error dialog for unable to sign in because of EMM policy. */
"EmmErrorTitle" = "Δεν είναι δυνατή η σύνδεση στον λογαριασμό";

/* The text in the error dialog asking user to set up a passcode for the device due to EMM policy. */
"EmmPasscodeRequired" = "Ο διαχειριστής σας απαιτεί να ορίσετε έναν κωδικό πρόσβασης στη συσκευή, για να έχετε πρόσβαση σε αυτόν τον λογαριασμό. Ορίστε έναν κωδικό πρόσβασης και δοκιμάστε ξανά.";

/* The text in the error dialog informing user that EMM policy prevented sign-in on the device. */
"EmmGeneralError" = "Η συσκευή δεν συμμορφώνεται με την πολιτική ασφαλείας που έχει ορίσει ο διαχειριστής σας.";

/* The title in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectTitle" = "Σύνδεση με την εφαρμογή Device Policy;";

/* The text in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectText" = "Προκειμένου να προστατεύσετε τα δεδομένα του οργανισμού σας, θα πρέπει να συνδεθείτε με την εφαρμογή Device Policy προτού συνδεθείτε στον λογαριασμό σας.";

/* The action button label in the error dialog informing user that connecting with Device Policy app is required. */
"EmmConnectLabel" = "Σύνδεση";
