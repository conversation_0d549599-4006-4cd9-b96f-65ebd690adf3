<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppAuth_AppAuth.bundle/Info.plist</key>
		<data>
		HS/4lWiwel3idUtGYXd+Nu3OBIk=
		</data>
		<key>AppAuth_AppAuth.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		MCDRptixYfA+qALBX9b4uxq3rwo=
		</data>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		sP5Uu4OZejLqS5730Rel17/oPpM=
		</data>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		9aHs7TPMknnNnrWRCQvhMmgjo6U=
		</data>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeResources</key>
		<data>
		ZWdN0qAKeiSb63OwJMjNV7KeCK0=
		</data>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>AppAuth_AppAuthCore.bundle/Info.plist</key>
		<data>
		UEfRWCLDai/X8Lrs4IwzvOUL3gk=
		</data>
		<key>AppAuth_AppAuthCore.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		MCDRptixYfA+qALBX9b4uxq3rwo=
		</data>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		4OlQrMajThRjnhhnQMWPDTaMNBc=
		</data>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		ZSzC8zneIYNA4DxTXTY2N2mjMx8=
		</data>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeResources</key>
		<data>
		ZWdN0qAKeiSb63OwJMjNV7KeCK0=
		</data>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Assets.car</key>
		<data>
		e3szHGHWRJcJ1SipcYIxx5T4haY=
		</data>
		<key>Firebase_FirebaseAuth.bundle/Info.plist</key>
		<data>
		x+fGydtJYpCvIdtGbNZMRF2kLb4=
		</data>
		<key>Firebase_FirebaseAuth.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		8cQ/rSS7XR8ohTuSAR27mQb1wiU=
		</data>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		mO9Q5nMipvyyJyHon8u+BvLoBBo=
		</data>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		SsFdm+9/dBIRKxwjqps+ExZ8V38=
		</data>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeResources</key>
		<data>
		+93AF2KkKF70e9S0m1GzxBmCT9E=
		</data>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Firebase_FirebaseCore.bundle/Info.plist</key>
		<data>
		JaCEZauQ9s850+VEy37om8FWnC8=
		</data>
		<key>Firebase_FirebaseCore.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sa2OhFlqdCIyz9oV7fUdDKWzFL0=
		</data>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		o0R7XuzRI9vO/9SLxUus5dyEdO0=
		</data>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		dhyDLtoegPZvEAulaiY5D+b5OEQ=
		</data>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeResources</key>
		<data>
		Py5p6PGa2PQGFtcFN7tODmh4UMM=
		</data>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/Info.plist</key>
		<data>
		Ij5cbZfk68eRBzU+zjON3DDLKsY=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		6pacyuJmXBmznK/SmdT8ImPf98E=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		AmqOzW5EsdHWWEmk4aXA3W/KMCM=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/Info.plist</key>
		<data>
		c0cKSn+MH1P2JW75aEE0wPo+F4k=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ifoThrqbbqoLG4yjAruMQRaf0Dw=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		Oh8fuTVjrGsgLgfHoMtDRgDL9A0=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		Qsdwp71mqm+P9Cx4ygJRLduzfsI=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeResources</key>
		<data>
		r6X+kHfeHjh6K0vCmZc9vyeYBcY=
		</data>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/Info.plist</key>
		<data>
		GNum3zbiTCJkmBqMMRQ2XUp1HOc=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		FF023Xu+ROGR6E1nZVY2pNdSWa0=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		M3r3KfMXRiSk8mMrq3YYzz3fmTU=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeResources</key>
		<data>
		9VYy8YrKkM6bye0xQHleeYVFCVI=
		</data>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/Info.plist</key>
		<data>
		sO/YFx4KVxaePHmqCBf2dms4GzM=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		cS+/OVePmxPoAxlfX7L8nIiXWRo=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		5v/yhikmzJKtfUCpWcLPBjj9nN0=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeResources</key>
		<data>
		9VYy8YrKkM6bye0xQHleeYVFCVI=
		</data>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Frameworks/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		fk3ys6tzo4ixkssfCUjASlegEkM=
		</data>
		<key>Frameworks/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		M1mCtsgscJkFWEojutUfdA0xkrc=
		</data>
		<key>Frameworks/FirebaseAnalytics.framework/_CodeSignature/CodeResources</key>
		<data>
		o8voZJ3FJga3AU0XkRQhxoeG/vw=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<data>
		OlmSUHWbKapfHgphtS6Uf1Ktzrw=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<data>
		4KBIdiMhtk9Pv5BKozN3UF74xGY=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		WXQUJr75eMRgiVnLGyf8Gr3uLUU=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<data>
		0Q24HEUeFllbg8ZptGxwkC8LTxM=
		</data>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<data>
		OwdtthT4U7+HAyfqLfeZUlvjaV8=
		</data>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/GoogleAdsOnDeviceConversion</key>
		<data>
		/Sp0JG3K0DiQb2O2w+tIwqXqwpU=
		</data>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/Info.plist</key>
		<data>
		ezoTvas9QuRcaTdbf+GQaBDQe3g=
		</data>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/_CodeSignature/CodeResources</key>
		<data>
		VfrkDFU4KniH0WKMxxJPy5cEqzc=
		</data>
		<key>Frameworks/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		1G4sv7Og0lE4CRoBwx9/nu/fhZo=
		</data>
		<key>Frameworks/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		yrCjuAzrETjp6Knx3IcgrltJl2k=
		</data>
		<key>Frameworks/GoogleAppMeasurement.framework/_CodeSignature/CodeResources</key>
		<data>
		IPftRaNkeXpKost8KtlLhPB3XB0=
		</data>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		A3U2MEGibEzdNH65HIhXh15eFG4=
		</data>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		7Z2jqaDT/O/szPSaEnUSykJoXzA=
		</data>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/_CodeSignature/CodeResources</key>
		<data>
		/2xOQoksj7fV+W2CrPsgTCyn12I=
		</data>
		<key>Frameworks/absl.framework/Info.plist</key>
		<data>
		/ZURSjM8lO4D6N/M9IjUclZQFtU=
		</data>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<data>
		YZdZW0SjqTv8GEjT3z9d2pjiz7g=
		</data>
		<key>Frameworks/absl.framework/absl</key>
		<data>
		WG/9khQJcUpm/QT+JwGRjW64ULU=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<data>
		SztSveZkg9oUBpLYuKwOAbIZpDo=
		</data>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		1GKSpq20OoFIxAuJBtHAw+vMZO8=
		</data>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<data>
		ubJ3M6MI+msm7A4nEsa6ypbkCOo=
		</data>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		RXBhOCzPKjFtmv2JkwWxwkCLJ2g=
		</data>
		<key>Frameworks/grpc.framework/grpc</key>
		<data>
		CNAuUilvYOm+Yjy7irHB81WB8SE=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<data>
		ywlvbsH44K4awuNtKK0m3bsmjGQ=
		</data>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<data>
		LgTxJHMowv71fNJ9hyDhrx7ds2U=
		</data>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<data>
		PpQ9Y9FLPnRrAnZK0UiEUUwyW1Q=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<data>
		RRM7PmEPFdoqu1Snl+phrXK8tTg=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<data>
		ICkAzk0IKbcdmEV1K8n6b1DN+ts=
		</data>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<data>
		eyp2OjuRgBOvd1Xcf0Dg4XElFOU=
		</data>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<data>
		POlQEYie/g7AuA89JDcdYKUsWWg=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<data>
		yIRoqaNOk9mXGAjUh6IsSbNcvrk=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<data>
		G9ZYmVug88vXqifyczIe7I9pHt4=
		</data>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		PFHYbs0V3eUFDWQyYQcwEetuqEk=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/Info.plist</key>
		<data>
		exW5dAdN14XyVsn0vp0AHfxjl2g=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		AL1dh5ctObXBjoBiabSJ86M3HQs=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		DWx/JP5vjqiXlRc3NP9Uw+HVN8k=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		5wXPIk/YKi6au5SqOctixI6vdNY=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeResources</key>
		<data>
		geDGh7xEYch0kQmlgo6FLcSfCVs=
		</data>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/Info.plist</key>
		<data>
		bkNexUCEcSSWAikI2/j3mFO+Qmk=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		GqeAMkwbcNQeG0K4qQhQh2vHhHo=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		sOIaaWKWAhrQbaHJf9mFeJIO4DY=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		9uxnxzi3lBvqinIwtslwlm+EYwI=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeResources</key>
		<data>
		NhlBNf4l9Kk4Ord1SSmZqN1xHp8=
		</data>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleService-Info.plist</key>
		<data>
		0mBgF6X1o4LfdacD54PgJxnZZ7k=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/Info.plist</key>
		<data>
		nvz5DyZIoi4RxlOKUuDHsRyT36E=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		Bpn4ciS1QfdDGVc7S27q+o0IUEM=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/Roboto-Bold.ttf</key>
		<data>
		RzJ98PNefNfIZFh0iXp0SWl1RK4=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		/6Yj0mtT4EOq6Il+AJ/Tf9h9Ey0=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		8lbIOy2JkqdbxmrPN32xdup7H4k=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeResources</key>
		<data>
		h/fLTP9au+/h2x3I0Wq4FEX3r0Q=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			1U4RM14vSknB5uPRHwbBf2P3L9k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			GWFStbBzSsozR8ndnlnCGWrVgIE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rkKd2a1WbhspF12uj7jbNaxdcsw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			CH8u4ji0ph6g0lWmwFmjuWeHjcQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			h9KGBhz6GBwN3U8va+ZhDVBbEsk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Sa1DzFvme+rejqFjRGh0R8Wvi2U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			abhi3BU5/ZWaUMKlntWlHWqGpfk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			iMO9o55MsPkZZzMfqxaQRohEIEs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			M6+vCrtORDxDSYRfCs0EQa/NF7E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rOsbQGNRZKkOBW4HhfQBTWwEQyI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gWfyNCqhd30Y2+42oIuL/HWmczg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			uazilWliTrW6MuAL6umfC/8JA2w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Esz3mlwnuaEJRGq9Kux4r37SBuQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/google.png</key>
		<data>
		guERtxRSG8LklxczLu47JIwLjiU=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/<EMAIL></key>
		<data>
		4UwXGGzo+UtMgR0hUDrtRbq4Wqk=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/<EMAIL></key>
		<data>
		A1wRWy89eRjMuVCP4K7S0AClNTw=
		</data>
		<key>GoogleSignIn_GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ui2oaT67k0R8hevxOaYRPvkBncY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kZvr7XJerhHMM69PkXbnvBFp03o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			f1AChdyqugygMHLDk8gT3Dci+JI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BSoNTBIf4kWxwJJhk3j4LqTytTQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Frdz7EAjoTfEGLrt45QOxezjzXY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ip22TOlHApPdL0R4Op+px9lnPt4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			sX0ftdSQz9WqdXlSCYZn0tktc/0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MXfc9JCBsSmgxmJ3xfMpVntvLfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZjmLydbxaQLSdIBGmX5LQQXMfhQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8kbG/vzCsprxXEBLWQPjsZYsmSM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			/CN7CHAVyaosf4bXVc5GxzC2Zbc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IBrsi/T3mBKZccejeg+eKu+eLC8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a2Crth0x+hSdaEGM4YAiNqZxYKg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a2Crth0x+hSdaEGM4YAiNqZxYKg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			d90ZsiztKcH6gT0M2GNhS9budLg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			wH6Vf08sJ8KKd++qPXetaXtSSEg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			dzPNIKMH551ZsL1tfx25Qj7g2fs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ltpfHSCCPpO3bQBP56jjt96+d90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6zdaseAWNL3CgzM6rhievkeYLBY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			jVVTwNkGABxQFM8h9aPRJ1fS2Pk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			4d5pvCFyTpDBY/o+3RsxJ8zqLdI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7AbLARiohKfq0KKpDOoXJxq3L38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QTbqZVk6kNI316gFQkdgXuOnQpw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Sf0rT0s1gLnj2sAnAlTyCH4vbP0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash</key>
			<data>
			lC5m5fk6gdtQtz8GphGC/3aPagU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/Info.plist</key>
		<data>
		JiDmmGmRPnSYTsMNEjGlLnskFQc=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		CpWtzKsH3QKqmGfwL2KKCQFPNLI=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		nXl8TsFR8z48EEubJBhtjL+kdW4=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/Info.plist</key>
		<data>
		kKGcQrmx+B7Ut05PZx0eoTz79o8=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6REKbJ61VeJGxEVgfCgqAzWWfA4=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		mxfCbow/EHo9PCtgLj3Q/lrtzZE=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		iQebk9wAOBOG3nh//3koV7uChJk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeResources</key>
		<data>
		drGFi3SryMBdRmZ77HI27qVqIoo=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/Info.plist</key>
		<data>
		frqq2x0WtTzEdjhge4RNBawj2uQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		ieGiL7/WP6pDkHNHwZT3PYL7Ucc=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		c2K0y2DUYWPrCTYut6m+18t5vkE=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/Info.plist</key>
		<data>
		3WABsHbEMjhmbQImAFqH1rcZllc=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		YsCUjhc/lFtcojUDXa8p9zhvpHE=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		AYGLC1SvLtAFXzae8bNR4SNSSUc=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/Info.plist</key>
		<data>
		fuCUelJRpsKfogN4eKmccKp0yW8=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		KKOG2ROfu2I9xrFcRkqzK+pY+mg=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		sYl2H6CP4arpyLJnEwCQzq6P5Ag=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/Info.plist</key>
		<data>
		rXNPS/ww7d5YcwwlZbPSFsoQJ58=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		JTGYQTSkuJ7uGfD1SfjQTq3ngyk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		T5wGPW1imGfw0+5SFa7VSRcV0gs=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		YveilQFkAD36WHudvN2C3ao5gP8=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeResources</key>
		<data>
		Vsl+we5n4R/NkIA513bsIjs/YEs=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/Info.plist</key>
		<data>
		X/1b58om3kah7Nh+VuzI96tgdS8=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6iBlSEWzrD6zouHx4NqeDhjKSGQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		dARxWr7cIyokT40qUD5VN38JRQM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		q/q7GuNv7W/28lrV79488tPLQio=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeResources</key>
		<data>
		g97GyooyBe06Yvsi0wY6xADNl1M=
		</data>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/Info.plist</key>
		<data>
		d7+izxL7+rTZlIF/TUrcsuKtcc4=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		g7Mu9E5yQgr4xj5lu4Xos3uMptQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		1U7hYSGbOjPp78u+Bp510Ab74pk=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		8Di7a1oskOfWqi+9Rj0JZoQBPg8=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeResources</key>
		<data>
		4xBjsjjtI+biJN3YOWZdOzAhhrQ=
		</data>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Info.plist</key>
		<data>
		kiti0fQYcTrXckn2zVJ7iZmZjOs=
		</data>
		<key>IngredientScanner.debug.dylib</key>
		<data>
		kCA7nrGCLTE3pFWJyMKupIxHqZE=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Promises_FBLPromises.bundle/Info.plist</key>
		<data>
		9xRqUR7Wb73o5NQ4gTMnOxKmSVc=
		</data>
		<key>Promises_FBLPromises.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZajnvEs/MYRS3X4TPLAhBWi8mc4=
		</data>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		8aMqAi6wF9XiInPnb2cIt3SdIYY=
		</data>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		UDeJHGBgbubAsUdkQnLTPCHvisQ=
		</data>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeResources</key>
		<data>
		V7VEfEjjYxtmBXmEblwrPsGfyPU=
		</data>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>__preview.dylib</key>
		<data>
		TIMPOAuU1aXKL1O7APho08Xk9TU=
		</data>
		<key>abseil_abslWrapper.bundle/Info.plist</key>
		<data>
		ZHqdQejI5Zw7o73YzwA2Ay5AXTg=
		</data>
		<key>abseil_abslWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		/1wMut5/ge/PBrRmM4qHdKkUkjI=
		</data>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		hPW0o659PGdoqsU8FZHDIZihWUw=
		</data>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		dKXXQCKEwFvwte7T/LJiHmI9w7s=
		</data>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeResources</key>
		<data>
		0DP8tfiQvcei4GDZAh8I4pBTpe0=
		</data>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>api-keys.plist</key>
		<data>
		NCPPZU0nwwtwJRPWqJzrr7YbWMs=
		</data>
		<key>gRPC_grpcWrapper.bundle/Info.plist</key>
		<data>
		+osNJq1N+fhGwzoc6KTP2DFDo1c=
		</data>
		<key>gRPC_grpcWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		U7WM6H59FffP9Htm/es+F6Fe4q4=
		</data>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		79DqKfWJ2Ra/82+Ap5wgHXgPrsY=
		</data>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		DpvnDS+RZkkXa3RLB9QjPNQu18A=
		</data>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeResources</key>
		<data>
		iZ2e6KIXoj78XKmRIZbkq2TwjLY=
		</data>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>gRPC_grpcppWrapper.bundle/Info.plist</key>
		<data>
		FG9u0QJB4mooq4DapLzBDespR2s=
		</data>
		<key>gRPC_grpcppWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		U7WM6H59FffP9Htm/es+F6Fe4q4=
		</data>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		OEcdsb/HELVbnIsL8uTa63/lQJM=
		</data>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		n1+IRQ5uj9p/RY8MkwS+FLzyQ1c=
		</data>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeResources</key>
		<data>
		iZ2e6KIXoj78XKmRIZbkq2TwjLY=
		</data>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>gRPC_opensslWrapper.bundle/Info.plist</key>
		<data>
		1vy/Nvg5wiGwJdnsKUNHuWs60ec=
		</data>
		<key>gRPC_opensslWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		U7WM6H59FffP9Htm/es+F6Fe4q4=
		</data>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		0VyFr1EKPkMXfL2RNxclpFnvNPo=
		</data>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		FUm2qZnZDFTDwmluCx5Dg+86GkQ=
		</data>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeResources</key>
		<data>
		iZ2e6KIXoj78XKmRIZbkq2TwjLY=
		</data>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>leveldb_leveldb.bundle/Info.plist</key>
		<data>
		nVgutzki+rurBom8ThX76zNHaWc=
		</data>
		<key>leveldb_leveldb.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		JTGYQTSkuJ7uGfD1SfjQTq3ngyk=
		</data>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		4SD2GIj7KrNMD4UesTH1K+H/7NM=
		</data>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		P5xxHDZSTh8JBxjAyBGSnYVNtqs=
		</data>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeResources</key>
		<data>
		Vsl+we5n4R/NkIA513bsIjs/YEs=
		</data>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>nanopb_nanopb.bundle/Info.plist</key>
		<data>
		S4F1tJ+4DRx0X0kVyfpaogfkdS0=
		</data>
		<key>nanopb_nanopb.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		KY5lfwC2TvsgFj4wt7hkMmainbs=
		</data>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeDirectory</key>
		<data>
		NIGNICLlVw7EL/u59cthqk5z/C8=
		</data>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeRequirements-1</key>
		<data>
		jqEP33tPQm+ugfOXxf0g/h7xEEE=
		</data>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeResources</key>
		<data>
		Xdm5Fm/A+gjo1onfASHL504z6HI=
		</data>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppAuth_AppAuth.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			S6Ic+DTXG3S3QS0zfCURQu02mheVeQbBb3WbErAt9ZU=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Wt6WL6rqHt273QbEz4qHLOxVT2TFWgDgATYATTeOHO0=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			kEoJ/ENKhKvlTDIG69Rhq42T+y1mgccn9hjCTebIx0U=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			GMIVB+OzeYNaI+ZXtxWRKWTUQC8avLqyy6s5Irpwt+Y=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Fu2rRv8biCBMaUK+eT3nc/88kDw5netXmXTJVX4cYpE=
			</data>
		</dict>
		<key>AppAuth_AppAuth.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XBKQP3eWOvO7x5eEERwz0phiIanFuitcUVRslki+hzg=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Wt6WL6rqHt273QbEz4qHLOxVT2TFWgDgATYATTeOHO0=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			T0mARTzL74a7vqLB6c7pMuO0pfmLVlhuj4Zr7XemoTk=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			yaC88CMuCLx42AnDe7rOv5YE5McgtDqmnrap/W1O4fw=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Fu2rRv8biCBMaUK+eT3nc/88kDw5netXmXTJVX4cYpE=
			</data>
		</dict>
		<key>AppAuth_AppAuthCore.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			CR6JOSuNZNOIuE6825nN2h571oWhaf7aFidQXCE5Ozo=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			fPRLoauebrfiEEtRMZluPgXzeJgMpineRzG+r/bJe6k=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BMFi6vpJss7QZjIxqJzJ9Xk1BsgCtoSGcZtdoGCK79U=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			y7Faln+C1gfiwVQFaD24PLcNJFUlJUzmbATRUWDYHvE=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			/COuB0RmDPssFBNdTEJZR/0EaTsGzyS0IkeWsRjSQdA=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			b3R/iELWe1q85qC/NBbVIw9NB+0O/sn+C3TpMFDv6c8=
			</data>
		</dict>
		<key>Firebase_FirebaseAuth.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HVPfnkjMbalRY13EmDHN2uo7jjJ0ugB8WzmGJq0iHxw=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			EeMfX2tg6A69WQFUn85QQ/mvPmg/h0AilFAAtAUwbD8=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			/WNazs9DJLCEhZ0Jz3gZQH6DgbXQzOES+GoOHhd71kM=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			xodYqNVBuzS9Aad+GJ5lZN7bkcFKSP8HMA2TGVYwfNQ=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			s4viTRdjGf7Khw01ZuV+j94fym4IJqKLZlteQMWkPCQ=
			</data>
		</dict>
		<key>Firebase_FirebaseCore.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			0ddh8i+xhRWeyamo2CZPbRG4E9OvIay3WKvHGrsH2IY=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			DMje92DiJUNNDqjQsVicohNAOYjC4BVy5yva8eCFSTw=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			D3kOMggvyyQm/VNPiu4NeqIYwS5o7tQEuQFR8n0LiYY=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreExtension.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			RBekbL/AChJgUl/0P+phJpG8KLPXdWLVF1wNQzQS16Y=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			W3/peUI97ePgivwppC8A9ghiddxUioTCl3QjWGPu0+8=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			GqzqrT5xviLXC/8b7eHXHeVENf/EhC4KV1ERbk9S7do=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			W2h/xWqdWTqUakaYqia8hdzvW+eWW+pmb42/mq3EvNY=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			a4S/FiHIoIZHgzvtJfGPFMQgWRPL27JczKhj2S47at4=
			</data>
		</dict>
		<key>Firebase_FirebaseCoreInternal.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/QjP4YXwep/upyPhNH4qGAlAYAJRCEnNUAcKfOGX3w8=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			69V+MnnmlZG69oktVTGtD6Km7HPoqTkHtG0hMJCQ4LM=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			ayilzZx5zUJQ6yn352fS0olsS52c/rT5QyrZXN1MCCI=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4uv1O1FxshKaMuXiKoHohOinOpu0GpwAF4UTkECKFKk=
			</data>
		</dict>
		<key>Firebase_FirebaseFirestore.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XVoW4NoFZ3r2sM0KfyXqz8lj2PgbSB7YXuay0ow8txs=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			UxcTUPyk36gVfBjvdp2eSlj661kdlbjXhNQHpkXsHC4=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			XFI4kIM9QrxgyQtqlLWWa0CQxPO8uPN056lu0aq5cd8=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4uv1O1FxshKaMuXiKoHohOinOpu0GpwAF4UTkECKFKk=
			</data>
		</dict>
		<key>Firebase_FirebaseInstallations.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Frameworks/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash2</key>
			<data>
			yF7CAfL8Xz/B6EnpJi2bm38HaY9uwX63MC8qGGUYwo0=
			</data>
		</dict>
		<key>Frameworks/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AC53QGfcr4zZHgUQsVnoqCuHBLBgZa0cH+l7BxUXE+4=
			</data>
		</dict>
		<key>Frameworks/FirebaseAnalytics.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ociHRNKJLFw+tzNqNLbHmvsBLxvu2g86C4i7lENeFmM=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal</key>
		<dict>
			<key>hash2</key>
			<data>
			Ha3iYUwfYTU0vlFhpmqhYYQCcHCDnwFL9HhDGaTwzYo=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			NQlJqFlYoBxdZdIgMt6EvRSD4zQNZapbFS1Us15SUOU=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/FirebaseFirestoreInternal_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			z7s8T3ambVNpi66R9xEMAPIUjm5vE619MlkpCbwBDlE=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			O1YZfFq9dFwWzkRr+NOzuu5EvO48xIEONS+p5z6+Ybk=
			</data>
		</dict>
		<key>Frameworks/FirebaseFirestoreInternal.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			D3JXHgREOyjo+rI6Vd0PbbFDCcK5Gjl53yrh/J1FMcM=
			</data>
		</dict>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/GoogleAdsOnDeviceConversion</key>
		<dict>
			<key>hash2</key>
			<data>
			sVyRzFT8NZ9cgos1yPcfO4i0ONYN5+43enKYb8e/kLo=
			</data>
		</dict>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			t2vkW/pyg6oOTJ2of/u2X+hNrMdI/yYO4t5znH8ZvHg=
			</data>
		</dict>
		<key>Frameworks/GoogleAdsOnDeviceConversion.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			fjY60xkhAXjYiBqskQImP1uPTf+r1OSZiYuuLySaM1w=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash2</key>
			<data>
			zC4BvweijuuE/xeRnhX24NFBVkaH7P3oPQei4s+ZvKw=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			doDY9S+ugeMIir5CGMGdQrP7AXxOeNKyXHCsFmeci8U=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurement.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			85UNaAYTNyPEWPWLviWW/mmlj5EH0VEvGtezxGKGyP0=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash2</key>
			<data>
			xaW/zQKLTA+w7RFfLrvqfXPbSC9NbI732MrgBbnBI+o=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CRQbj/4wv3H1pezd9tB/IG5R2MuD52NyvH1lCzhQ79c=
			</data>
		</dict>
		<key>Frameworks/GoogleAppMeasurementIdentitySupport.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			pKl+JBeTLFfGjCG18ieLZI9Q3HuRU9vqPOTQ/FDOeGA=
			</data>
		</dict>
		<key>Frameworks/absl.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/RPXdNxUMBch5ELyBRU26FEqBULntBIYRQS5FKif0ls=
			</data>
		</dict>
		<key>Frameworks/absl.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			Ew7Rhkg5tn3MaKGvYb1Q02DyBYFbzTaeuycLTX6qQSg=
			</data>
		</dict>
		<key>Frameworks/absl.framework/absl</key>
		<dict>
			<key>hash2</key>
			<data>
			HBcFMqZ1gcWkM2WQ5dbi2T6rXrp3Jt9Zh5IlQVRA8aQ=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Lz4HtWENPgwb5XVAJm+f7jLXxAGgQYG40OUJtt4kkcM=
			</data>
		</dict>
		<key>Frameworks/absl.framework/xcprivacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			8jIheunt8qtqVBoo7/UM/gUwPCtHVv/5XMpy7MvDuJg=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yg2+PU6TfI3xNWEnQJx4b2xwUKZQ3P0b/0W+ZAE0wxU=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			CJ/0YHQsY651QCBgRT/tNR3veTrFrZ45shGGXviIJeY=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			khm3mkpDJ9NtK8a91tUvDhqRdhhn6VEp1Wt93ZFJKus=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+EyBg615h8fiFUcwQ18MMBH8M8q7BQEv7a725+xXN5U=
			</data>
		</dict>
		<key>Frameworks/grpc.framework/grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			hzD1my+DD+uP86LOkIPzsfyFCvvVnsfwfGEmJzslDAQ=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			byXDu2hxfDdMpm0ylgeBeq1B2SNfLLLFwoLYOvoDvOk=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp</key>
		<dict>
			<key>hash2</key>
			<data>
			AKJ0V1HkbzWfmf1qgYRRoJ7EdfHSfLlFocKujV9IkPg=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AxRP90yQRLB+4QOsFk1qnkbNQxCgh5Turpd/yQyPQ0g=
			</data>
		</dict>
		<key>Frameworks/grpcpp.framework/grpcpp.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ac8enRTuHBo5KhKAujiaNEniDo7ILULHTI0Fu62grjc=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			z9/A5jXCmNUSrX908G0UYoQPfFyWqr/hK2mHiVhB+pA=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc</key>
		<dict>
			<key>hash2</key>
			<data>
			2aad1WoIytwlbQdbzZk/GQwrQ8fSrGfpx0rJ0gux0u8=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			rVqhaGyQJuquOW6bXmMRLBr3exUMdIVVnYDKjPMcPvk=
			</data>
		</dict>
		<key>Frameworks/openssl_grpc.framework/openssl_grpc.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			A7LHCDOjMaKx79Ef8WjtAqjq39Xn0fvzDuzHUJpK6kc=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVYIyiUZoac15i9/fNDSM0jZhdlDeGxdtdQys9MK2PA=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			WpuPwM3bECAbtHzCgEs/AExyUUdmItJb/E61TtRuEIQ=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			B/8Au2AR7oHVUIFX1KJI6WzG/gDUuG5Fy1rBUpJmpDY=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			F4f8SaezEcdCmfNEWzpCNnY+Mwc7tJP8w1um/tk8dyU=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			91JAzcAtYT2VeVXO1efXTYbdbO77RFUsk3sgSZ9M9lk=
			</data>
		</dict>
		<key>GTMAppAuth_GTMAppAuth.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WLFs5We9GEig6e/5FWmd8p7BdR3bJxFFLbNbNmKC8Hk=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			PkqTy+hqzvfdfgY6KMhJmS9Vbn9SytxfN8HosOG1RoY=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			4wgwEfT67jcDlM20O9wJeRZw1mkHBiBNNsKjTS7pWSM=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			6BDQdLjkdhCcZJg55nIOxfHzOZdsVVa7ZyTQrsCeP68=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			4lHB5rHor4MeEoX4/dIUMXmNUPkMxDSs1vVEntTv0sI=
			</data>
		</dict>
		<key>GTMSessionFetcher_GTMSessionFetcherCore.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			hIk+eKYqlqI/FX5IS5QvToYeoKF4tgVb1QaUA1C3X+4=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			hsVbwi23oeRMy5vilJCI7dwmcOLTcBiYwjF5DSZSyAo=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			kwCXKFMJb31XDWxKS/cVKjbHmOicTw2B07TGtd+t39A=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/Roboto-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			WU10pJ4we+fMnh7l8QI2hOaCDPEbzJaL7lkDkeGtWlo=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			vICxwIvB2BaMMEAn2m5GUQAC+bX+mAAN4ThGMhu/JT0=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			NFTGEAUtB45+mK0tYB1sPcUq/tpxVcj//qKk5V2EHy0=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			1QxgS1iPfuzMgqLsO0pdQCqZ9M0EisNqCtGHFWorOdY=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ar.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ss+ejZ/IrQ+WELPlTvC4tJTwIbf6I6zvYJSyIFq+jHI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ca.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			M+iP9QDcPbJGLirJM8EcKJKfrTUfV7Pm1irERhCtYao=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/cs.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UdoIYkwqed5HyBaDsp6KE0SkeIdWO+JW4ad6Z/a+NUQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/da.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6CYblkbY6on3v3phpzrtzjD+UfaKqUfsvf6Mx+BNt9o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/de.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Fs1BPfGrd+UJ33b4c6Wmh//PNgsfFQdD2TTFqzN3wig=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/el.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nMDBNJz2Zfx3MzDTGiYo9gCkPKqLXIgLA0pWZ/jw+rw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/en.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xyajnDguPxp8H1js9HD4EtomN0n5MNqH98DDqg7GSMI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/en_GB.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			or14KgUbGgs8Q37Gox6b9/Z3iZWnKswyoOtKY5mtGsI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/es.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UB5ExPO7nAWxMpfGAkD9Dhbnn4pj5vslGYII3LlCHKY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/es_MX.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hNMnxxl7flSaHp6Ni7PhywLgKCxjI+X7Pd4KRzla9jQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DpnzGonq2MwPxyrWq9zISr0m9pkmXu/2GoibNznadzs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tVE4d6zy15yPDq/3/N5KoWXzq7AWEbYGESYC/UQ3d7o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/fr_CA.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			9xCSzz/bp+PgPQ2ZdIN4sRGUfKd6mRgw3dJMOZIrCG4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/google.png</key>
		<dict>
			<key>hash2</key>
			<data>
			H2IokCB8oNkRwgBLBEZLd0w6oN+iN1BU/iAZdGyDMKU=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			Mjq4n6IBYa2yGeFxUDX5MS6Ty2kwi1TyDkPS11aFwLI=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/<EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			vuNrWCRZeBKrexqD2NEDZOScuuIxG5X6EYoYNxvcJlU=
			</data>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/he.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KtB1FmX6YQB2EQfECgjfBJBnQA7X2NP7QuPuxFZ6YLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3UHXJzarX0qX0J3bFMSADuQLm+0jadZPDF/6uWpiRh4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			JA+ymYzp46eWqXkG/G+WClYRwHhNxzsWyukU+u0KW/4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/hu.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GoRF+Zxo+7BfNxT4D3DtCIpiNxtbmIXjErRRbwfYnk8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/id.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PyRv43MvgTyToElSNlECqWWiJIriTlBPGVMTmFiLK/k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/it.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			TtxpfPqTYA6LaQTYcihANVBXvLLpxiQmqXzstkL2M8g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ja.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hk3OYj6xqZvup+cqHbkAhhDEA8l1hXBMH3gLIIk4ERQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ko.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AhqCmUXH5He26Jp82dpoP1opwP/AwBLug5uvUO96wBM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ms.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			y2wRSAv0XEuZrBwtz9tnpfgqeZkXomMNT7PpYTV7XJM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/nb.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AqDORhPvDCPsPxYdWdXEk+neYmyBsEvpMYChGkLK6rA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/nl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SQUYZcAGQO3tTDWbHoIOEdHxkQVdu0gFkfk5fTerXg0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pl.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zB70RS/MmKy+7SKVC9fwoipXngJaj1DHj9u2AO045V0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4m5LhoUwf1F8mYZJzxXPRsQnjz0AOVugKWHC6rrzO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt_BR.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			h4m5LhoUwf1F8mYZJzxXPRsQnjz0AOVugKWHC6rrzO4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/pt_PT.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VgBP67jGl9/ChEtJOy0f2pT31xiBxen0V91QtDRgDpk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ro.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			r4dFHw1YRkncNtW9F0rb86p6SQ53QicvL33YbiJi490=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/ru.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			HTeXAqlR7PmIQ9lXCSqMSv7j7v6GamABZD47d3Vdxdw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/sk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			P/cihlo8LmENm4TLBV2TYyC4PWbK+iTI64GTaNQdXaw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/sv.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EB6TqcbOigHnqGM3qiEvaIEEVBHW5FJwF5/vJhhhQx4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/th.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			87AIPzi3JhCduSGnkEqY3kpQF7l4xbCahIDFRHo8T8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/tr.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			DUnrVChJprfGiaclYvcgTKdFHOG+K+qtB0NSDZurgNI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/uk.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Jhxu6B2f/rMv7vqp4r9JDKbb+lbniEMqlbSjM+LHru8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/vi.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XdgQI6jed2yICWt5Lgc2Nmjlld6tS+jlIZu73Yg5j+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/zh_CN.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			UnRDQSf3cJY9kq+HAKK4OMrkkbt2CGQtT/KlF/uB1nc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleSignIn_GoogleSignIn.bundle/zh_TW.lproj/GoogleSignIn.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8K9LpcAznoYMsbk4Z5+PTNH5EEAbvqGHz7sjmrE7t+U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7qtOfeo10cDxJIhOaulFT5zHEJ83VpUzbcOKpktweO4=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			DVQOdVw6SIgt5FMmAlCDszW70rHSgwLy8WW7DgSghj8=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			wMbzKaE1G7xzMhsUhkJgO5VGEiEO0766z+IOWK6lsyo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-AppDelegateSwizzler.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			48VvUV0LpNFl7rvd39i+BrtPVMGBEzASrucZaq/77fw=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			sIJz1oc3k5R5L3HJTBCDYWoEr4r6o5bYBYmA9ETPs9Q=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			8cwp+MNH2gPt/P/gPBOeXK/fkvnLt6tT/cl722c+n94=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			BmsY9iFXxsT//NmCEkdfNroqihIcAzRFegSZ3a9ZqHY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			J7Mfwlmz7kyPb50YZegis6Owni17ZmqB12G1a6126mw=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Environment.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ljHmv14meJzpV0jtpZA1im9ZfU0XkD2cR+3hyWK9CaE=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			7ICPP2EZ/tpH8RjpXwxtVBcATHf9KoZNR/Gagr9Ef1E=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			ArqyxzdAwxNMnCQR1k79ziyS/g0MbI4XuxkzI1headg=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Logger.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			****************************************+jo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			dIi42ssnV+YbR1zjDuNqUZe+JXAXZ1SAx1XpkL5Ancw=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			i5iPGmUX4egdgt9XKCvSysCxRSb7gtSIy5juG2yCbc4=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-MethodSwizzler.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			A155U97TseFum5lVUa853vNIDd3tJUkcReBzSWzNwMQ=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			H/KRnFaiu8LnQjKxjsV5XkfEoLpxwb08uQcWpX1cJr0=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			aW9Z4BMyxoY8N+2+fzVkVX1BqSguaGoNgrp16zNI/UE=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-NSData.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YbzKzswtJuOBqoWEj/hAZSR+BCYGmrNwuve+l8nerrA=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CsAKC37/AHnc4lLimrQdcc9YxslP5OROoRDO9cYpcOE=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			10Xn7ef3iTf3IjzG7JaVujAxBr+zVAgTizg6jVKkwWQ=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			1IZ6JEJde7CilEouOfZMZ03hAMA5uojtsLv0UmUN6/Q=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			wTbWnUBaA2aQZKCOVKNia+VvFWSRgP+K6GbijLx0pOE=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Network.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			QbpcjjniUyc9qPMbOABjKyrkZidE8DfVvzpQpYqzUrM=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			E3fszt/4+DIPeAQOnJ0+F+zA6laVlLVq8O8XvkQZfHo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			LdVgs9JlnFiqI/STiwA40weXE5i1s1ufIvl5VKEm6Jc=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			zEopx3hzJ+vmY/MOoxCiZ1FtMkxEmwPmKlfK+z3KLTE=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			P1fNGSTesOI/356TRiPbyUjtoMHFFr2DRGzVX09NZqo=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-Reachability.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7KNUJ9YZLCKskbrj2CBxMj3DHbBkfgXDG3gp5PkFJ1o=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			+sUHxRCrp4hOgF+1/v8aio0Mf0LAHRdPknor36pxMLQ=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			OGOyamEHgeiVV2N9M8PqenjzzdkAvcK1IjRxaxrHsN4=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			pkM8WGE6YQMnTfUikLDbLM3lhG18WTvoFsiaPHx8/7s=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			a0OarUH6gedUP4KPYKwvdWFg+RbtVLcurAbwF8wCD1c=
			</data>
		</dict>
		<key>GoogleUtilities_GoogleUtilities-UserDefaults.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>IngredientScanner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			jSncFDUqyMTT6R1BtPFlsEGE28oqfCKHD3xY31A7bAk=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			O5E3dsyc40mJlLHvcR6xk1zm3RccjaXKwLwjoLWSF/4=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			dLDNcvwjwe8wLyLuJ1P2GBfNxa8P96fy0GMrUk+4rOo=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			mKjb/kXUcjSgysucul0AAiFl5FXLgS4JGrYMSDC+W2E=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			tMsi2tC071f2CRBlMb9ng/4jR4a9tsZssYhsxqUES2Q=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			+yMX+ttysAer2DGVVZrRCnkLJNkGykOe5yFyG58I+4g=
			</data>
		</dict>
		<key>Promises_FBLPromises.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			8z76/Dt+JmElqMV4r+lqOqsUeA3UmaVTsrz6hg5jgLE=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HygsgZve8Ex8A1gaVOnheT0NBkiCfdwhJx7+svgH+mM=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			d0leynC0pf0GEIoiuyaD3rwNsl7LO96KVGLjIa6+y5o=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			AOaBq/vBQsAfz8Ed4AeRQOu3WQ2zmkojV7Yj9YSeL2Y=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			1aCFKcqPPxqSiSPlSjZ/6zlofL1tdOsnfzoplVIZ9Sw=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			VPohPoPYPYaM6FMqNMIXBSPoqXiAWJDGP8ThjqllFXM=
			</data>
		</dict>
		<key>abseil_abslWrapper.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>api-keys.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uEwKZfOgLFKWZoUwPWPqJaN0sdhq7Y4zpAPfjNJWisw=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			IhFJmzQikHSj553vGk9veUxzmDmxkCIv7swpysdp5pk=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			SMwLNyeKt9zx4V4Bagi8Dg1kPZyuLQYcTZQlPy1SyIo=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			UQK1Ekw15bjiAR5IixWnTuGqd81SB08qe+elNRGNx3U=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			6Az2vGAdxHc+waWxpEex+sSU+KLHpqkrGK5/nHp+OQU=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ySZg6svLIssDXcbuDp4tgUgyqdSOUwPhIufx4WKFi18=
			</data>
		</dict>
		<key>gRPC_grpcWrapper.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eeZiz0EX1s9kEWpGC7/t/RwMHqSPKmj+dwgl1oe7k2Y=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			SMwLNyeKt9zx4V4Bagi8Dg1kPZyuLQYcTZQlPy1SyIo=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			hPkwwJIi1HNJ2jSV/hun8mYFMnP1XlOpt9dU7Fr5eYc=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			Wcl6wk1jsdSq8w8EWho3wh9uMUj/nESgsQFwVA4OnsE=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ySZg6svLIssDXcbuDp4tgUgyqdSOUwPhIufx4WKFi18=
			</data>
		</dict>
		<key>gRPC_grpcppWrapper.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			59xJMmg0PXiG/1wX1kYi09eny/f8iIAq1uPNWwcr6lg=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			SMwLNyeKt9zx4V4Bagi8Dg1kPZyuLQYcTZQlPy1SyIo=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			ofFF8I5yuMom/P/V3hG/j1C2zwtePP7UL+ILbFZeNoE=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			uanUPHCX9yGTLymUpr5YMYglt2XrWTqpbJbScrnlxfY=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ySZg6svLIssDXcbuDp4tgUgyqdSOUwPhIufx4WKFi18=
			</data>
		</dict>
		<key>gRPC_opensslWrapper.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			FIc5D7xvFETO7nOj2VyPzHjBjLY3JwNVKdX+hV1Z+ds=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CsAKC37/AHnc4lLimrQdcc9YxslP5OROoRDO9cYpcOE=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			QQo2ICs98s0s9ZnBiD+31e51AOwe0PLtjM5Y92vZSEE=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			YSpiUgcn5yDFYPe66rNtmuXCE6/Va934Dcrea7val3c=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			wTbWnUBaA2aQZKCOVKNia+VvFWSRgP+K6GbijLx0pOE=
			</data>
		</dict>
		<key>leveldb_leveldb.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			DreufK18t4f/Xdg7f70A88yamq3umVUn2n6iiAuXk4E=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			cpujy9D0WMeM1h7fFzUO2v4ONMqG4xTsZMjLIszSG1Q=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash2</key>
			<data>
			0xyjJ8dHnbPPTDukgAKLSwc3dZJ2LggKI+EN55OfXVE=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash2</key>
			<data>
			qV2Jpgz1lo+H+c1ZnT0pOSFpdOQ3EjmcYM+z7V7loFo=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			iNSHXvTsDmBKDKBMjE7mEAKwt4n9RLY2YKcygwUC7mM=
			</data>
		</dict>
		<key>nanopb_nanopb.bundle/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
