# Accessibility (a11y) Checklist

Scope: Recipe Generator UI (GenerateButton, QuickResultsView, ConfigurationSummary)

- Labels/Hints
  - GenerateButton has localized accessibilityLabel and Hint
  - QuickResultsView states have localized labels (idle/loading/loaded/failed)
  - ConfigurationSummary has label and hint
- Traits
  - <PERSON><PERSON> controls expose .isButton where applicable
- Dynamic Type
  - Uses system text styles (body, subheadline, headline)
  - Layout verified to avoid truncation in Large/ExtraLarge
- Contrast and Dark Mode
  - Uses system colors and materials for backgrounds and text
- Hit Target
  - Buttons min height >= 44pt

Tests: Consider adding UI tests with VoiceOver disabled to assert identifiers and labels when feasible.

