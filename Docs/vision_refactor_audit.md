# Phase 0 — Preflight Usage Audit for Vision APIs

Date: 2025-08-20
Owner: Augment Agent
Scope: detectTextAndLabels, batchAnalyze(images:), processImage(_:), resizeImageIfNeeded(_:) usages

## Findings
- StagingViewModel: uses prepareImagesForVisionAPI + batchAnalyzeWithTrueBatching — OK.
- ConcurrencyManager: legacy usage found; refactored to use prepareImagesForVisionAPI + batchAnalyzeWithTrueBatching.
- No other production call sites found.

## Actions Taken
- ConcurrencyManager.processBatchImages and processImagePair updated to the true batch pipeline.

## Next Steps
- Proceed to Phase 1 — remove legacy methods and rename batchAnalyzeWithTrueBatching -> batchAnalyze.

