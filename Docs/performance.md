# UI Performance Monitoring

- A lightweight PerformanceMonitor (Utils/PerformanceMonitor.swift) collects:
  - First paint (ms) from view appearing to first frame
  - Average FPS during the screen lifetime
- It reports via TelemetryService.ui_performance with fields:
  - screen: String
  - avgFps: Int
  - firstPaintMs: Int
- Integrated into RecipeGeneratorView using `.task { start }` and `.onDisappear { stopAndReport }`.
- Follow-ups:
  - Consider adding navigation lifecycle hooks for more screens
  - Expand to measure specific interactions if needed

