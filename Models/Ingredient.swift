import Foundation
import SwiftUI

struct Ingredient: Identifiable, Co<PERSON>ble, <PERSON><PERSON>le, Sendable {
    let id: UUID
    var name: String
    var category: PantryCategory
    var dateAdded: Date = Date()
    // Purchase date for expiration checks (defaults to dateAdded if not set)
    var purchaseDate: Date = Date()
    // Number of completed 7-day notification cycles
    var notificationCycle: Int = 0

    /// Initialize a new ingredient with a generated UUID
    init(name: String, category: PantryCategory, dateAdded: Date = Date(), purchaseDate: Date? = nil) {
        self.id = UUID()
        self.name = name
        self.category = category
        self.dateAdded = dateAdded
        self.purchaseDate = purchaseDate ?? dateAdded
        self.notificationCycle = 0
    }

    /// Initialize an ingredient using a pre-existing persistent ID
    init(id: UUID, name: String, category: PantryCategory, dateAdded: Date = Date(), purchaseDate: Date? = nil, notificationCycle: Int = 0) {
        self.id = id
        self.name = name
        self.category = category
        self.dateAdded = dateAdded
        self.purchaseDate = purchaseDate ?? dateAdded
        self.notificationCycle = notificationCycle
    }
}

// MARK: - Shared Error Types for iOS 17 Modernization

/// Shared cancellation error for .task(id:) modernization across all ViewModels
struct TaskCancellationError: LocalizedError, Sendable {
    var errorDescription: String? {
        return "Task was cancelled"
    }
}