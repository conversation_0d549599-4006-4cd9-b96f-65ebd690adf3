import Foundation
import SwiftUI

@Observable
@MainActor
final class RegenerateModeState {
    static let shared = RegenerateModeState()

    // Selection mode flag
    var isSelecting: Bool = false

    // Selected MealSlot IDs
    var selectedSlotIds: Set<UUID> = []

    // Optional pending title to preselect when entering Recipes tab from detail
    var pendingPreselectTitle: String?

    enum SlotStatus: Equatable {
        case idle
        case selected
        case regenerating
        case replaced
        case notReplaced
    }

    // Per-slot status for UI feedback
    var slotStatuses: [UUID: SlotStatus] = [:]

    private init() {}

    func beginSelection() {
        isSelecting = true
    }

    func endSelection() {
        isSelecting = false
        selectedSlotIds.removeAll()
        slotStatuses.removeAll()
        pendingPreselectTitle = nil
    }

    func toggleSelection(_ slotId: UUID) {
        if selectedSlotIds.contains(slotId) {
            selectedSlotIds.remove(slotId)
            slotStatuses[slotId] = .idle
        } else {
            selectedSlotIds.insert(slotId)
            slotStatuses[slotId] = .selected
        }
    }

    func setStatus(_ slotId: UUID, _ status: SlotStatus) {
        slotStatuses[slotId] = status
    }
}

