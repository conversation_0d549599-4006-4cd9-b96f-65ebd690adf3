import Foundation

enum RetentionPolicy {
    public static let plansWeeksToKeep: Int = 4

    /// Compute the Monday start for a given date.
    static func weekStart(for date: Date, calendar input: Calendar = .current) -> Date {
        let calendar = input
        let day = calendar.startOfDay(for: date)
        let weekday = calendar.component(.weekday, from: day) // 1=Sun,2=Mon,...7=Sat
        let deltaToMonday: Int = (weekday == 1) ? -6 : (2 - weekday)
        let start = calendar.date(byAdding: .day, value: deltaToMonday, to: day) ?? day
        return calendar.startOfDay(for: start)
    }

    /// Returns a pruned list of DayPlan keeping only the most recent N calendar weeks.
    /// Days belonging to older weeks are removed entirely.
    static func trimToRecentWeeks(_ days: [DayPlan], keepWeeks: Int = plansWeeksToKeep, calendar: Calendar = .current) -> (days: [DayPlan], removedWeeks: Int) {
        guard keepWeeks > 0 else { return ([], uniqueWeekStarts(in: days, calendar: calendar).count) }

        // Group day plans by week start
        var grouped: [Date: [DayPlan]] = [:]
        for d in days {
            let ws = weekStart(for: d.date, calendar: calendar)
            grouped[ws, default: []].append(d)
        }
        let allWeekStarts = grouped.keys.sorted()
        if allWeekStarts.count <= keepWeeks {
            // Nothing to remove
            let merged = allWeekStarts.flatMap { grouped[$0] ?? [] }
            return (merged, 0)
        }

        // Keep the most recent `keepWeeks` week starts
        let keep = Array(allWeekStarts.suffix(keepWeeks))
        let keptDays = keep.flatMap { grouped[$0] ?? [] }
        let removed = allWeekStarts.count - keep.count
        return (keptDays, removed)
    }

    private static func uniqueWeekStarts(in days: [DayPlan], calendar: Calendar) -> Set<Date> {
        var set: Set<Date> = []
        for d in days { set.insert(weekStart(for: d.date, calendar: calendar)) }
        return set
    }
}
