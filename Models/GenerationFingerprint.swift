import Foundation

struct GenerationFingerprint: Codable, Equatable, Sendable {
    let selectedMeals: Set<MealType>
    let days: Int
    let mealConfigs: [String: MealConfig]
    let cuisines: [String]
    let additionalRequest: String?
    let startDate: Date
    let equipment: [String]

    static func current(
        from configuration: CustomConfiguration,
        startDate: Date,
        equipment: [String]
    ) -> Self {
        let mealConfigMap = Dictionary(uniqueKeysWithValues: configuration.mealConfigurations.map { entry in
            (entry.key.rawValue, entry.value)
        })

        let normalizedCuisines = configuration.cuisines.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        let sortedCuisines = Array(Set(normalizedCuisines)).sorted()
        let sortedEquipment = Array(Set(equipment)).sorted()

        return Self(
            selectedMeals: configuration.selectedMeals,
            days: configuration.days,
            mealConfigs: mealConfigMap,
            cuisines: sortedCuisines,
            additionalRequest: configuration.additionalRequest.isEmpty ? nil : configuration.additionalRequest,
            startDate: Calendar.current.startOfDay(for: startDate),
            equipment: sortedEquipment
        )
    }
}

extension GenerationFingerprint {
    private static let storageKey = "lastGenerationFingerprint"

    func store(using defaults: UserDefaults = .standard) {
        guard let data = try? JSONEncoder().encode(self) else { return }
        defaults.set(data, forKey: Self.storageKey)
    }

    static func load(using defaults: UserDefaults = .standard) -> Self? {
        guard let data = defaults.data(forKey: Self.storageKey) else { return nil }
        return try? JSONDecoder().decode(Self.self, from: data)
    }
}
