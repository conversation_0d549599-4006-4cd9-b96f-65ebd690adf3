import Foundation

/// Manage actions supported in batch selection flows (Quick | Plans)
enum ManageAction: CaseIterable, Equatable {
    case viewDetails
    case addToFavorites
    case removeFromFavorites
    case regenerate
    case share
    case delete

    var title: String {
        switch self {
        case .viewDetails: return "View Details"
        case .addToFavorites: return "Add to Favorites"
        case .removeFromFavorites: return "Remove Favorites"
        case .regenerate: return "Regenerate"
        case .share: return "Share"
        case .delete: return "Delete"
        }
    }

    var isDestructive: Bool { self == .delete }
}

