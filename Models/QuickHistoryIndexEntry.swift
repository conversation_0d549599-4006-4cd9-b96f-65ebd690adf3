import Foundation

/// Preview entry for quick history index (lightweight metadata).
public struct QuickHistoryIndexEntry: Codable, Equatable, Sendable {
    public let id: UUID
    public let mealType: MealType
    public let generatedAt: Date
    public let recipeCount: Int
    public let recipeTitles: [String]
    /// File key for full JSON payload on disk.
    public let dataKey: String
    
    public init(id: UUID, mealType: MealType, generatedAt: Date, recipeCount: Int, recipeTitles: [String], dataKey: String) {
        self.id = id
        self.mealType = mealType
        self.generatedAt = generatedAt
        self.recipeCount = recipeCount
        self.recipeTitles = recipeTitles
        self.dataKey = dataKey
    }
}
