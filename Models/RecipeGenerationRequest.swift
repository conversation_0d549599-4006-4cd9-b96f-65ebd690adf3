import Foundation

// MARK: - Recipe Generation Request (PRD v3)

struct RecipeGenerationRequest: Codable, Equatable {
    let apiVersion: String
    let mode: UIMode
    let pantryContext: PantryContext
    let requestDetails: RequestDetails?

    init(mode: UIMode, pantryItemCount: Int, requestDetails: RequestDetails? = nil) {
        self.apiVersion = "v1"
        self.mode = mode
        self.pantryContext = PantryContext(hasItems: pantryItemCount > 0, itemCount: pantryItemCount)
        self.requestDetails = requestDetails
    }
}

struct PantryContext: Codable, Equatable {
    let hasItems: Bool
    let itemCount: Int
}

struct RequestDetails: Codable, Equatable {
    let schedule: Schedule
    let meals: [MealRequest]
    let preferences: Preferences
}

struct Schedule: Codable, Equatable {
    let startDate: String // YYYY-MM-DD
    let days: Int // 1-7
}

struct MealRequest: Codable, Equatable {
    let type: String // "breakfast"|"lunch"|"dinner"
    let maxCookingTimeMinutes: Int // 5-120
    let numberOfDishes: Int // 1-6
}

struct Preferences: Codable, Equatable {
    let cuisines: [String]
    let additionalRequest: String?
    let equipmentOwned: [String]
}

// MARK: - Builder Helpers

extension RequestDetails {
    /// Build a RequestDetails for a single meal type with specific count.
    /// Useful for per-meal batch generation feeding the structured plan generator.
    static func singleMeal(mealType: MealType,
                           count: Int,
                           maxTime: Int,
                           cuisines: [String],
                           additionalRequest: String?,
                           equipmentOwned: [String] = []) -> RequestDetails {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let startDate = formatter.string(from: Date())
        let schedule = Schedule(startDate: startDate, days: 1)
        let meal = MealRequest(type: mealType.rawValue,
                               maxCookingTimeMinutes: max(5, min(120, maxTime)),
                               numberOfDishes: max(1, min(6, count)))
        let prefs = Preferences(cuisines: cuisines,
                                additionalRequest: additionalRequest,
                                equipmentOwned: equipmentOwned)
        return RequestDetails(schedule: schedule, meals: [meal], preferences: prefs)
    }
    static func build(from config: CustomConfiguration, equipmentOwned: [String] = []) -> RequestDetails {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let startDate = formatter.string(from: Date())
        let schedule = Schedule(startDate: startDate, days: config.days)
        let meals = config.selectedMeals.compactMap { mealType -> MealRequest? in
            guard let mealConfig = config.mealConfigurations[mealType] else { return nil }
            return MealRequest(
                type: mealType.rawValue,
                maxCookingTimeMinutes: mealConfig.cookingTimeMinutes,
                numberOfDishes: mealConfig.numberOfDishes
            )
        }
        let prefs = Preferences(
            cuisines: config.cuisines,
            additionalRequest: config.additionalRequest.isEmpty ? nil : config.additionalRequest,
            equipmentOwned: equipmentOwned
        )
        return RequestDetails(schedule: schedule, meals: meals, preferences: prefs)
    }

    static func build(from quick: QuickConfiguration, equipmentOwned: [String] = []) -> RequestDetails {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let startDate = formatter.string(from: Date())
        let schedule = Schedule(startDate: startDate, days: 1)
        let meal = MealRequest(type: quick.mealType.rawValue, maxCookingTimeMinutes: max(5, min(120, quick.totalTimeMinutes)), numberOfDishes: max(1, min(6, quick.numberOfDishes)))
        let prefs = Preferences(
            cuisines: quick.cuisines,
            additionalRequest: quick.additionalRequest.isEmpty ? nil : quick.additionalRequest,
            equipmentOwned: equipmentOwned
        )
        return RequestDetails(schedule: schedule, meals: [meal], preferences: prefs)
    }
}
