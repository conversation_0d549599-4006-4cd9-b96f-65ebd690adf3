import Foundation

// MARK: - Regenerate (Phase 3)

struct RegenerateRequest: Codable, Equatable {
    struct TargetSlot: Codable, Equatable {
        let slotId: UUID
        let dayIndex: Int
        let mealType: MealType
    }

    struct Exclusions: Codable, Equatable {
        let titles: [String]
    }

    let targetSlots: [TargetSlot]
    let originalInputsDays: Int
    let originalSelectedMeals: [MealType]
    let cuisines: [String]
    let additionalRequest: String?
    let pantryContext: PantryContext
    let exclusions: Exclusions
}

