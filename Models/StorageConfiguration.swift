import Foundation

/// Centralized configuration for storage-related settings.
public enum StorageConfiguration {
    // MARK: - Quick History
    public static let quickHistoryIndexKey = "quick.history.index.v1"
    public static let legacyQuickHistoryKey = "quick.history.v1" // for migration
    public static let quickHistoryFolderName = "QuickHistory"
    public static let quickHistoryCapacity = 10

    // MARK: - Image Cache
    public static let imageCacheFolderName = "ImageCache"
    public static let imageCacheTTL: TimeInterval = 48 * 60 * 60 // 48 hours

    // MARK: - JSON Encoding
    public static let jsonEncoder: JSONEncoder = {
        let e = JSONEncoder()
        e.dateEncodingStrategy = .iso8601
        return e
    }()

    public static let jsonDecoder: JSONDecoder = {
        let d = JSONDecoder()
        d.dateDecodingStrategy = .iso8601
        return d
    }()

    // MARK: - Queues
    public static let storageQueueLabel = "com.ingredientscanner.history.storage"
}

