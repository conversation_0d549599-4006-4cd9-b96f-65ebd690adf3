import Foundation

/// Represents a Monday–Sunday week window used by the Plans calendar.
/// Provides convenience for computing the 7 dates in the week and formatting.
struct WeeklyMealPlan {
    let weekStart: Date   // Monday (start of day)
    let weekEnd: Date     // Sunday (start of day)
    let days: [Date]      // 7 dates from Monday to Sunday (start of day)

    init(referenceDate: Date = Date(), calendar inputCalendar: Calendar = Calendar.current) {
        let calendar = inputCalendar
        // Normalize to start of day to avoid DST/time issues
        let today = calendar.startOfDay(for: referenceDate)

        // Force Monday as week start independent of locale settings
        // Default Calendar.weekday: 1 = Sunday, 2 = Monday, ... 7 = Saturday
        let weekday = calendar.component(.weekday, from: today)
        let deltaToMonday: Int = (weekday == 1) ? -6 : (2 - weekday)
        guard let start = calendar.date(byAdding: .day, value: deltaToMonday, to: today) else {
            self.weekStart = today
            self.weekEnd = today
            self.days = [today]
            return
        }
        self.weekStart = calendar.startOfDay(for: start)
        let end = calendar.date(byAdding: .day, value: 6, to: weekStart) ?? weekStart
        self.weekEnd = calendar.startOfDay(for: end)

        var arr: [Date] = []
        for i in 0..<7 {
            if let d = calendar.date(byAdding: .day, value: i, to: weekStart) {
                arr.append(calendar.startOfDay(for: d))
            }
        }
        self.days = arr
    }

    static func formatRange(_ start: Date, _ end: Date, calendar: Calendar = .current) -> String {
        let df = DateFormatter()
        df.locale = Locale.current
        df.calendar = calendar
        df.dateFormat = "MMM d"
        let yearFmt = DateFormatter()
        yearFmt.locale = Locale.current
        yearFmt.calendar = calendar
        yearFmt.dateFormat = "yyyy"
        let sameYear = calendar.component(.year, from: start) == calendar.component(.year, from: end)
        let left = df.string(from: start)
        let right = df.string(from: end)
        if sameYear {
            return "\(left)–\(right), \(yearFmt.string(from: end))"
        } else {
            return "\(left), \(yearFmt.string(from: start)) – \(right), \(yearFmt.string(from: end))"
        }
    }
}

