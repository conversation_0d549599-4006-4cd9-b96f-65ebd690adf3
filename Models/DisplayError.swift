import Foundation

enum DisplayError: LocalizedError, Equatable {
    case networkError(message: String)
    case serviceError(message: String)
    case invalidInput(message: String)
    case pantryEmpty
    case pantryError(String)
    case configurationError(String)
    case unknownError

    var errorDescription: String? {
        switch self {
        case .networkError:
            return NSLocalizedString("error_network", comment: "")
        case .serviceError:
            return NSLocalizedString("error_service", comment: "")
        case .invalidInput:
            return NSLocalizedString("error_invalid_input", comment: "")
        case .pantryEmpty:
            return NSLocalizedString("error_pantry_empty", comment: "")
        case .pantryError:
            return NSLocalizedString("error_pantry", comment: "")
        case .configurationError:
            return NSLocalizedString("error_configuration", comment: "")
        case .unknownError:
            return NSLocalizedString("error_unknown", comment: "")
        }
    }

    enum RecoveryAction: String {
        case retry
        case reconfigure
        case gotoPantry
    }
}

