import Foundation

/// Lightweight model for a single Quick generation history entry
/// Ke<PERSON> intentionally similar to `LastQuick` for compatibility, but with a stable ID
public struct QuickResultHistory: Codable, Equatable, Identifiable, Sendable {
    public let id: UUID
    public let mealType: MealType
    public let numberOfDishes: Int
    public let totalCookTime: Int
    public let cuisines: [String]
    public let additionalRequest: String?
    public let generatedAt: Date
    public let recipes: [RecipeUIModel]

    public init(
        id: UUID = UUID(),
        mealType: MealType,
        numberOfDishes: Int,
        totalCookTime: Int,
        cuisines: [String],
        additionalRequest: String?,
        generatedAt: Date = Date(),
        recipes: [RecipeUIModel]
    ) {
        self.id = id
        self.mealType = mealType
        self.numberOfDishes = numberOfDishes
        self.totalCookTime = totalCookTime
        self.cuisines = cuisines
        self.additionalRequest = additionalRequest
        self.generatedAt = generatedAt
        self.recipes = recipes
    }
}

