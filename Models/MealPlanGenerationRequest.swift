import Foundation

/// V6: Structured Meal Plan Generation Request
/// Captures the exact intent for slot-based generation over a bounded date range.
struct MealPlanGenerationRequest: Sendable {
    /// Inclusive start date (defaults to today)
    let startDate: Date
    /// Number of days to generate (1...7 enforced by builder)
    let days: Int
    /// Set of meals to include per day
    let selectedMeals: Set<MealType>
    /// Per-meal configuration for time/count
    let slotConfigs: [MealType: MealConfig]
    /// Optional user preferences passed through to generation
    let cuisines: [String]
    let additionalRequest: String?
    let equipmentOwned: [String]

    init(startDate: Date,
         days: Int,
         selectedMeals: Set<MealType>,
         slotConfigs: [MealType: MealConfig],
         cuisines: [String] = [],
         additionalRequest: String? = nil,
         equipmentOwned: [String] = []) {
        self.startDate = startDate
        self.days = days
        self.selectedMeals = selectedMeals
        self.slotConfigs = slotConfigs
        self.cuisines = cuisines
        self.additionalRequest = additionalRequest
        self.equipmentOwned = equipmentOwned
    }
}

