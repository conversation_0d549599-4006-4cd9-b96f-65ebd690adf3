import Foundation

enum PantryCategory: String, Codable, CaseIterable, Sendable {
    // Kept
    case bakingAndSweeteners = "Baking & Sweeteners"
    case oilsVinegarsAndCondiments = "Oils, Vinegars & Condiments"
    case spicesAndSeasonings = "Spices & Seasonings"
    case proteins = "Proteins"
    case produce = "Produce"
    case seafood = "Seafood"
    case other = "Other" // A fallback category

    // Added/renamed
    case dairy = "Dairy"
    case plantBasedAlternatives = "Plant-based Alternatives"
    case grainsPastaLegumes = "Grains, Pasta & Legumes"
    case nutsAndSeeds = "Nuts & Seeds"
    case cannedAndBroths = "Canned & Broths"
    case bakery = "Bakery"
    case snacks = "Snacks"

    var icon: String {
        switch self {
        case .produce:
            return "🥕"
        case .proteins:
            return "🍗"
        case .seafood:
            return "🐟"
        case .dairy:
            return "🥛"
        case .plantBasedAlternatives:
            return "🌱"
        case .bakingAndSweeteners:
            return "🍯"
        case .oilsVinegarsAndCondiments:
            return "🫒"
        case .spicesAndSeasonings:
            return "🧂"
        case .grainsPastaLegumes:
            return "🍚"
        case .nutsAndSeeds:
            return "🥜"
        case .cannedAndBroths:
            return "🥫"
        case .bakery:
            return "🥖"
        case .snacks:
            return "🍿"
        case .other:
            return "📦"
        }
    }
}