import Foundation

/// Detailed recipe information returned by Gemini API
struct RecipeDetail: Codable, Equatable {
    let title: String
    let servings: Int
    let totalTimeMinutes: Int
    let difficulty: String
    let ingredients: [String]
    let steps: [String]
    let nutrition: RecipeNutrition?
    
    enum CodingKeys: String, CodingKey {
        case title
        case servings
        case totalTimeMinutes
        case difficulty
        case ingredients
        case steps
        case nutrition
    }
}

/// Nutrition information for a recipe
struct RecipeNutrition: Codable, Equatable {
    let calories: Int?
    let protein: String?
    let carbs: String?
    let fat: String?
    
    enum CodingKeys: String, CodingKey {
        case calories
        case protein
        case carbs
        case fat
    }
}

// MARK: - Extensions

extension RecipeDetail {
    /// Create a sample recipe detail for testing/preview
    static let sample = RecipeDetail(
        title: "Garlic Butter Pasta",
        servings: 4,
        totalTimeMinutes: 25,
        difficulty: "easy",
        ingredients: [
            "1 lb spaghetti or linguine",
            "6 cloves garlic, minced",
            "1/2 cup butter",
            "1/4 cup olive oil",
            "1/2 cup fresh parsley, chopped",
            "1/2 cup grated Parmesan cheese",
            "Salt and black pepper to taste",
            "Red pepper flakes (optional)"
        ],
        steps: [
            "Bring a large pot of salted water to boil. Cook pasta according to package directions until al dente.",
            "While pasta cooks, heat olive oil and butter in a large skillet over medium heat.",
            "Add minced garlic to the skillet and sauté for 1-2 minutes until fragrant, being careful not to burn.",
            "Reserve 1 cup of pasta cooking water, then drain the pasta.",
            "Add the drained pasta to the skillet with garlic and butter.",
            "Toss pasta with the garlic butter, adding pasta water gradually to create a silky sauce.",
            "Remove from heat and add fresh parsley and Parmesan cheese.",
            "Season with salt, pepper, and red pepper flakes to taste.",
            "Serve immediately while hot, with additional Parmesan if desired."
        ],
        nutrition: RecipeNutrition(
            calories: 520,
            protein: "18g",
            carbs: "68g",
            fat: "22g"
        )
    )
    
    /// Format total time as a readable string
    var formattedTime: String {
        if totalTimeMinutes < 60 {
            return "\(totalTimeMinutes) min"
        } else {
            let hours = totalTimeMinutes / 60
            let minutes = totalTimeMinutes % 60
            if minutes == 0 {
                return "\(hours)h"
            } else {
                return "\(hours)h \(minutes)m"
            }
        }
    }
    
    /// Get difficulty color for UI
    var difficultyColor: String {
        switch difficulty.lowercased() {
        case "easy":
            return "green"
        case "medium":
            return "orange"
        case "hard":
            return "red"
        default:
            return "gray"
        }
    }
    
    /// Get estimated prep time (roughly 1/3 of total time)
    var estimatedPrepTimeMinutes: Int {
        max(5, totalTimeMinutes / 3)
    }
    
    /// Get estimated cook time (roughly 2/3 of total time)
    var estimatedCookTimeMinutes: Int {
        totalTimeMinutes - estimatedPrepTimeMinutes
    }
}

extension RecipeNutrition {
    /// Format nutrition info as a readable string
    var summary: String {
        var parts: [String] = []
        
        if let calories = calories {
            parts.append("\(calories) cal")
        }
        
        if let protein = protein {
            parts.append(protein + " protein")
        }
        
        if let carbs = carbs {
            parts.append(carbs + " carbs")
        }
        
        if let fat = fat {
            parts.append(fat + " fat")
        }
        
        return parts.joined(separator: " • ")
    }
}
