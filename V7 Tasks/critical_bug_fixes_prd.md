# V7 Critical Bug Fixes - Product Requirements Document

**Date**: 2025-01-09  
**Version**: V7.0  
**Branch**: haotask3done → v7-critical-fixes  
**Status**: Ready for Implementation  
**Priority**: Critical (P0) and High (P1) Bug Resolution

## Executive Summary

This PRD addresses 8 critical and high-priority issues identified through user reports and technical analysis. The fixes are organized into 5 implementation bundles for maximum development efficiency and minimal regression risk. The scope includes critical crashes, data consistency issues, UX improvements, and workflow optimizations.

**Business Impact**: Resolving these issues will restore core functionality, improve user retention, and eliminate forced app restarts, directly improving user satisfaction and app store ratings.

## Implementation Strategy - Bundle-Based Approach

Based on technical analysis, issues are grouped into cohesive development phases:

### Bundle Prioritization
1. **Bundle A** (P0): Scan Pipeline Stability - Critical user workflow blocker
2. **Bundle E** (P0): Meal Plan Generation Stability - Critical feature blocker  
3. **Bundle C** (P0/P1): Quick History Lifecycle - Critical management issues
4. **Bundle B** (P1): Generator UX - User experience improvements
5. **Bundle D** (P1): Favorites Data Integrity - Data consistency

---

## Bundle A — Scan Pipeline Stability 🚨 **CRITICAL**

**Issues**: Camera Crash (1.1), Ingredient Name Formatting (2.3)  
**Business Priority**: P0 - Primary user workflow completely blocked  
**Estimated Effort**: 2-3 days  
**Dependencies**: None

### A.1 Camera Crash Fix

**Problem**: App crashes immediately when user taps "Scan Receipts and Ingredients"

**Root Cause**: Missing `NSCameraUsageDescription` in `Info.plist`

**Technical Requirements**:
- Add camera usage description to `Info.plist`
- Implement device availability checks in all camera access paths
- Handle simulator fallback gracefully

**Implementation Details**:
```xml
<!-- Add to Info.plist -->
<key>NSCameraUsageDescription</key>
<string>This app uses the camera to scan ingredient labels and receipts for automatic pantry management.</string>
```

**Code Changes Required**:
- **File**: `Info.plist`
  - Add `NSCameraUsageDescription` entry
- **File**: `StagingViewModel.swift` (line ~177)
  - Add `UIImagePickerController.isSourceTypeAvailable(.camera)` check before showing camera
- **File**: `ImagePicker.swift`  
  - Ensure consistent availability checking across all camera access methods

**Acceptance Criteria**:
- ✅ iPhone 14 Pro opens camera successfully
- ✅ Simulator opens Photo Library without crash or message
- ✅ Permission denial handled gracefully with Settings navigation
- ✅ No crashes on any device configuration

**Testing Requirements**:
- Physical device with camera permissions granted/denied
- iOS Simulator testing  
- Various device models and iOS versions

### A.2 Ingredient Name Formatting

**Problem**: Scan results show ingredient names in lowercase, inconsistent with pantry formatting

**Root Cause**: Scan results bypass `NameCanonicalizer` title-case formatting used in pantry

**Technical Requirements**:
- Apply consistent formatting across scan results and pantry storage
- Preserve existing Pantry formatting rules (descriptors, acronyms, special cases)
- Maintain dual formatting (display + storage)

**Implementation Details**:
- **File**: `GeminiAPIService.swift`
  - Apply `NameCanonicalizer.canonicalize()` in `parseIngredientsResponse()`
- **File**: `ResultsView.swift`  
  - Ensure displayed names match pantry formatting standards

**Acceptance Criteria**:
- ✅ Scan results display proper title-case names (e.g., "Whole Milk", "BBQ Sauce")
- ✅ Saved pantry items match scan result formatting exactly
- ✅ Special cases preserved (acronyms, descriptors, stopwords)
- ✅ No regression in existing pantry name formatting

---

## Bundle E — Meal Plan Generation Stability 🚨 **CRITICAL**

**Issues**: Meal Plan Generator Crash (1.2)  
**Business Priority**: P0 - Core feature completely unusable  
**Estimated Effort**: 2-3 days  
**Dependencies**: None

### E.1 Generator Crash Prevention

**Problem**: App crashes when user taps "Generate" button in meal plan generator

**Root Cause**: Unhandled exceptions in `RecipeServiceAdapter.generate()` calls

**Technical Requirements**:
- Add comprehensive error handling around adapter calls
- Validate inputs before generation attempts
- Graceful failure with user-friendly messages
- Maintain app stability under all input conditions

**Implementation Details**:
- **File**: `StructuredMealPlanGenerator.swift` (line ~49)
  - Wrap `adapter.generate()` calls in try-catch blocks
  - Add input validation for `RecipeGenerationRequest`
  - Handle empty pantry and invalid configuration scenarios
- **File**: `RecipeServiceAdapter.swift`
  - Add input sanitization and validation
  - Return graceful failures instead of throwing exceptions

**Acceptance Criteria**:
- ✅ No crashes across all meal plan configurations
- ✅ Empty pantry handled with informative user notice
- ✅ Invalid date ranges handled gracefully  
- ✅ Network failures display appropriate error messages
- ✅ User remains in app with ability to retry/adjust inputs

**Error Messages** (Non-blocking notices):
- "Unable to generate meal plan. Please add more ingredients to your pantry."
- "Meal plan generation failed. Please check your preferences and try again."
- "Network error. Please check your connection and retry."

---

## Bundle C — Quick History Lifecycle Integrity ⚠️ **HIGH PRIORITY**

**Issues**: History Counting (3.1), Delete Counter Bug (4.1), Manage Alert Loop (4.2)  
**Business Priority**: P0 (Alert Loop) + P1 (Data Consistency)  
**Estimated Effort**: 3-4 days  
**Dependencies**: Bundle A, E (recommended)

### C.1 Manage Page Alert Loop Fix 🚨 **CRITICAL**

**Problem**: Infinite alert loop forces users to restart app

**Root Cause**: Improper alert binding state management in `ManageSelectionView`

**Technical Requirements**:
- Replace blocking alerts with non-blocking toast messages
- Implement proper state cleanup after batch operations
- Maintain UI interactivity throughout operations

**Implementation Details**:
- **File**: `ManageSelectionView.swift` (lines 34-38)
  - Remove complex `Binding(get:set:)` alert pattern
  - Replace with toast-based feedback system
  - Add proper `resultMessage` state reset
- **File**: `BatchOperationManager.swift`
  - Ensure async operations properly handle state transitions

**Acceptance Criteria**:
- ✅ No infinite alert loops under any operation
- ✅ One toast message per batch operation
- ✅ UI remains interactive during and after operations
- ✅ Accurate count reporting (e.g., "Deleted 3 items; 1 skipped (favorited)")

### C.2 Delete Counter Bug Fix

**Problem**: Deleting individual recipes doesn't update counter; empty entries persist

**Root Cause**: Empty `QuickResultHistory` entries not cleaned up; UI refresh gap

**Technical Requirements**:
- Auto-cleanup empty history entries
- Trigger parent view refresh after deletions
- Maintain counter accuracy

**Implementation Details**:
- **File**: `QuickResultPageView.swift` (line 57, `deleteRecipe()`)
  - Add empty entry cleanup logic
  - Filter out entries with zero recipes in `replaceAll()`
  - Trigger `QuickHistoryView` refresh via notification/callback
- **File**: `QuickHistoryView.swift`
  - Add automatic reload after recipe deletions

**Acceptance Criteria**:
- ✅ Counter decreases when last recipe deleted from entry
- ✅ Empty entries automatically removed from manage page
- ✅ UI updates immediately reflect changes
- ✅ Capacity calculations remain accurate

### C.3 History Counting Logic Implementation

**Problem**: User confusion about generation vs recipe counting

**Root Cause**: UI doesn't clearly communicate session-based counting

**Decision**: Keep capacity = 10 generation sessions (approved)

**Technical Requirements**:
- Update UI copy to clarify session-based counting
- Add generation badges to Quick cards
- Implement consistent messaging

**Implementation Details**:
- **File**: `CapacityIndicatorView.swift` (line 21)
  - Update capacity display from "[n/10]" to "[n/10] generations" 
- **File**: `QuickHistoryView.swift`
  - Add "x dishes" badge to each Quick card
- **File**: `RecipeGeneratorViewModel.swift`
  - Update save toast: "Saved 2 dishes (1 generation) to Quick."

**Acceptance Criteria**:
- ✅ Capacity indicators show generation count everywhere
- ✅ Quick cards display dish count badges  
- ✅ Save messages clarify generation vs dish count
- ✅ User understanding improved through consistent terminology

---

## Bundle B — Generator UX and Navigation 📱 **HIGH PRIORITY**

**Issues**: Toast Design (2.1), Post-Generation Navigation (2.2)  
**Business Priority**: P1 - User experience and workflow optimization  
**Estimated Effort**: 2-3 days  
**Dependencies**: Bundle C (recommended)

### B.1 Generator Toast Design Enhancement

**Problem**: Toast is too small, poorly positioned, lacks context

**Root Cause**: Minimal toast design doesn't provide adequate user feedback

**Requirements**:
- Centered, medium-size card design
- Show 2-3 top recipes with cooking times
- Clear visual hierarchy with prominent actions

**Implementation Details**:
- **File**: `RecipeGeneratorView.swift` (GeneratorToastView struct at line 353)
  - Change from `.overlay(alignment: .top)` to centered positioning
  - Increase size and padding for better visibility
  - Add cooking time display to recipe previews
  - Implement "+N more" overflow indicator
  - Improve button styling (Regenerate: bordered, Good: prominent)

**UI Specifications**:
- **Position**: Center of screen with backdrop
- **Size**: Medium card (75% screen width, auto height)
- **Content**: Title + 2-3 recipe previews + actions
- **Accessibility**: VoiceOver support for all elements

**Acceptance Criteria**:
- ✅ Readable on all device sizes (iPhone SE to Pro Max)  
- ✅ VoiceOver announces recipe titles and cooking times
- ✅ Clear visual distinction between Regenerate/Good actions
- ✅ "+2 more" indicator when >3 recipes generated

### B.2 Post-Generation Navigation Enhancement  

**Problem**: After "Good" tap, user doesn't land in Quick section of Recipes tab

**Root Cause**: Missing sub-tab targeting in navigation flow

**Requirements**:
- Direct navigation to Recipes > Quick sub-tab
- Visual orientation with "New" badge on latest entry
- Consistent behavior regardless of prior tab state

**Implementation Details**:
- **File**: `RecipeGeneratorViewModel.swift` (line 263, `acceptQuickPreview()`)
  - Set `@AppStorage("recipes.selectedTab") = "quick"` before `switchToTab(3)`
  - Add 3-second "New" badge to latest Quick card
- **File**: `RecipeHistoryTabView.swift`
  - Ensure proper sub-tab selection handling

**Acceptance Criteria**:
- ✅ User lands on Recipes > Quick sub-tab after "Good"
- ✅ Newest card shows "New" badge for 3 seconds
- ✅ Behavior consistent regardless of previous sub-tab
- ✅ Smooth transition with no UI glitches

---

## Bundle D — Favorites Data Integrity 📊 **HIGH PRIORITY**

**Issues**: Favorites Display Corruption (4.3)  
**Business Priority**: P1 - Core feature restoration  
**Estimated Effort**: 2-3 days  
**Dependencies**: None

### D.1 Favorites Display Corruption Fix

**Problem**: Favorites show "numbers and letters" instead of recipe information; no tap interaction

**Root Cause**: `FavoritesManager.resolveUIModel()` failures cause fallback to raw data display

**Requirements**:
- Implement graceful degradation for missing recipe data
- Store minimal UI snapshots as backup data
- Provide clear user actions for corrupted entries

**Implementation Details**:
- **File**: `FavoritesManager.swift` (line 80, `resolveUIModel()`)
  - Add data validation and error handling
  - Implement UI snapshot storage during favorite creation
- **File**: `FavoritesView.swift` (line 35)
  - Add fallback rendering for unresolved items
  - Show "Unavailable" label with remove action for corrupted entries
  - Implement proper error recovery

**Fallback UI Design**:
- **Available Recipe**: Normal navigation to recipe detail
- **Unavailable Recipe**: Info icon + "Unavailable" label + "Remove" button
- **Corrupted Data**: Generic "Recipe unavailable" with remove option

**Acceptance Criteria**:
- ✅ No corrupted text displays in favorites list
- ✅ All rows either navigate to recipe detail OR show remove option
- ✅ Clear visual distinction between available/unavailable recipes  
- ✅ User can clean up corrupted favorites easily
- ✅ No crashes when accessing favorites with missing data

---

## Cross-Bundle Requirements

### Testing Strategy

**Regression Testing**:
- Full smoke test after each bundle completion
- Cross-bundle integration testing before release
- Performance impact validation

**Device Testing Matrix**:
- iPhone 14 Pro, 15 Pro Max (iOS 17+ compatible)
- iOS Simulator (iOS 17+)
- Physical devices with various permission states

**User Scenario Testing**:
- Complete scan → results → pantry workflow
- Full meal plan generation cycle  
- Quick recipe generation → save → management
- Favorites add/remove/view workflows

### Success Metrics

**Crash Reduction**:
- 0% crash rate on camera access
- 0% crash rate on meal plan generation
- 0% forced app restarts from manage page

**User Experience**:
- 100% navigation success rate (Generator → Recipes > Quick)
- 95%+ user satisfaction with toast design improvements
- Consistent ingredient name formatting across all flows

**Data Integrity**:
- 0% empty history entries in manage view
- 100% accurate counter displays
- 100% favorites displaying valid information

### Release Readiness Criteria

**Bundle A**: ✅ Camera works on all devices, formatting consistency achieved  
**Bundle E**: ✅ Meal plan generation stable across all configurations  
**Bundle C**: ✅ No alert loops, accurate counters, clear messaging  
**Bundle B**: ✅ Improved toast UX, correct navigation targeting  
**Bundle D**: ✅ Favorites display valid data with proper fallbacks

### Post-Release Monitoring

**Telemetry Collection** (Anonymous, No PII):
- Camera access success/failure rates
- Meal plan generation completion rates
- Generator choice rates (Regenerate vs Good)
- Manage operation outcomes (deleted/replaced/skipped)
- Favorites resolution failure rates

**User Feedback Tracking**:
- App Store rating improvements
- Support ticket reduction in affected areas
- User retention metrics post-fix

---

## Implementation Timeline

**Week 1**: Bundle A (Scan Pipeline) + Bundle E (Meal Plan)  
**Week 2**: Bundle C (Quick History Lifecycle)  
**Week 3**: Bundle B (Generator UX) + Bundle D (Favorites)  
**Week 4**: Integration testing, regression validation, release preparation

**Total Estimated Effort**: 12-16 development days  
**Target Release**: End of Week 4  
**Rollout Strategy**: Progressive release with monitoring

---

## Risk Assessment

**High Risk**: Bundle C (Manage Alert Loop) - Complex state management  
**Medium Risk**: Bundle A (Camera) - Platform-specific behavior  
**Low Risk**: Bundle B, D, E - Well-defined scope and implementation

**Mitigation**: Prioritize P0 issues first, maintain comprehensive testing coverage, implement feature flags for gradual rollout if needed.

---

**Document Approval**: Ready for development team assignment and sprint planning.