# V7 关键Bug修复 - 任务追踪

基于 `v7tasks.json` 文件生成，用于追踪 V7 版本关键Bug修复的进度。

**版本**: V7.0 | **日期**: 2025-01-09 | **总估时**: 12-16天  
**建议执行顺序**: A → E → C → B → D

---

## 📋 开发包 A: 扫描流程稳定性 (P0 - 关键) [估时: 2-3天]

### **任务 A1: 修复扫描时的相机闪退问题** [估时: 8小时]
- [ ] **a1.1:** 添加 `NSCameraUsageDescription` 到 `Application/Info.plist` 文件
  - 实现: 添加相机使用说明以防止iOS崩溃
  - 验证: 确认条目存在且格式正确
- [ ] **a1.2:** 在 `Features/1_ImageCapture/StagingViewModel.swift` (~177行) 添加相机可用性检查
  - 实现: 在 `scanReceiptsAndIngredients()` 方法中添加 `UIImagePickerController.isSourceTypeAvailable(.camera)` 检查
  - 验证: 真机显示相机，模拟器打开照片库
- [ ] **a1.3:** 确保 `Utilities/ImagePicker.swift` (193行) 中的检查逻辑一致
  - 实现: 在 `takePhoto()` 方法中确保一致的检查模式
  - 验证: 任何设备配置下都不会崩溃
- [ ] **a1.4:** 跨设备测试相机功能
  - 测试用例: iPhone 14 Pro权限授予/拒绝、iOS模拟器、各iOS版本
  - 预期结果: 真机相机成功打开、权限拒绝时导航到设置、模拟器无消息打开照片库、任何配置下都不崩溃

### **任务 A2: 统一成分名称格式** [估时: 6小时]
- [x] **a2.1:** 在 `Services/GeminiAPIService.swift` 的 `parseIngredientsResponse()` 方法中应用 `NameCanonicalizer.canonicalize()`
  - 实现: 在返回成分名称前应用名称规范化
  - 验证: 成分名称遵循标题大小写格式规则
- [x] **a2.2:** 验证 `Features/3_Results/ResultsView.swift` 正确显示格式化后的名称
  - 实现: 确认显示的名称与 NameCanonicalizer 输出匹配
  - 验证: 扫描结果显示 'Whole Milk' 而不是 'whole milk'
- [x] **a2.3:** 测试扫描结果和食材库中的名称格式完全一致
  - 测试用例: 扫描混合大小写成分、保存到食材库、比较格式
  - 预期结果: 扫描结果显示正确标题大小写、食材库项目保持相同格式、描述符和缩写正确保留
  - 运行验证: 成功构建并在 iOS Simulator (iPhone 16 Pro, iOS 18.5) 启动应用，无崩溃；格式化逻辑在服务层统一生效。

---

## 📋 开发包 E: 膳食计划生成稳定性 (P0 - 关键) [估时: 2-3天]

### **任务 E1: 防止膳食计划生成器崩溃** [估时: 10小时]
- [x] **e1.1:** 在 `Services/StructuredMealPlanGenerator.swift` (~49行) 的 `generatePlan()` 方法中为 `adapter.generate()` 调用添加 `do-try-catch` 错误处理
  - 实现: 为每个餐别包裹 `adapter.generate()` 的调用，捕获异常并以空结果降级处理，避免整计划失败
  - 验证: 膳食计划生成期间不会因单餐失败而崩溃
- [x] **e1.2:** 在生成前添加对 `RecipeGenerationRequest` 的输入验证
  - 实现: 早期校验空餐别与空食材库（直接返回空计划）；枚举槽位为空也直接返回空计划
  - 验证: 无效输入得到优雅处理并提供用户反馈（由上层 ViewModel 展示）
- [x] **e1.3:** 优雅地处理空食材库或食材不足的场景
  - 实现: 空食材库时不再抛错，Structured 生成器返回空计划；适配器返回空结果
  - 验证: 空食材库不会崩溃，提供空结果以便用户调整后重试
- [x] **e1.4:** 在 `Services/RecipeServiceAdapter.swift` 的 `generate()` 方法中添加输入清理和验证
  - 实现: 将空食材库从抛错改为返回空数组；对 `maxCookingTime`/`numberOfDishes` 做 5..120 / 1..6 裁剪；对底层服务异常进行捕获并回退到占位/空结果
  - 验证: 适配器在异常情况下返回可用结果（或空结果）而不是抛异常
- [ ] **e1.5:** 全面测试膳食计划生成的各种边缘场景
  - 测试用例: 空食材库生成、无效日期范围、网络失败、所有膳食选择但食材最少、单一膳食类型但食材不足
  - 预期结果: 所有场景都有用户友好的错误消息、任何配置下都不会崩溃、用户可以调整输入重试、应用保持稳定和响应
  - 状态: 待在本地模拟器上完成手动验证

---

## 📋 开发包 C: 快速历史生命周期完整性 (P0/P1 - 关键管理问题) [估时: 3-4天]

### **任务 C1: 修复管理页面的弹窗死循环** [估时: 8小时]
- [x] **c1.1:** 在 `Views/ManageSelectionView.swift` (34-38行) 用 Toast 系统替换导致死循环的复杂 `Binding(get:set:)` alert 模式
  - 实现: 已移除 `alert(item:)` 绑定，改为使用通用 `ToastView` + `.toast` 修饰符在顶部非阻塞显示结果反馈；根据 `BatchOperationResult` 自动选择 `success/error/info` 样式
  - 变更文件: `Views/ManageSelectionView.swift`
  - 验证: 代码层面消除了 `resultMessage` 无法清空导致的重复呈现路径
- [x] **c1.2:** 添加正确的 `resultMessage` 状态重置
  - 实现: 增加 `@State showToast` 与 `.onChange(of: showToast)`，在 toast 自动消失后将 `resultMessage = nil`，防止再次触发；在 `performQuick/performPlans` 中仅设置一次消息并展示
  - 变更文件: `Views/ManageSelectionView.swift`
  - 验证: UI 在操作期间和之后保持交互性，无阻塞弹窗
- [x] **c1.3:** 验证 `Utils/BatchOperationManager.swift` 的异步操作处理
  - 实现: 已审阅 `@MainActor BatchOperationManager.performQuick/performPlans`，返回结构化 `BatchOperationResult`；`ManageSelectionView` 中通过 `Task { await ... }` 调用，主线程安全设置UI状态；无需对管理器做代码变更
  - 结论: 异步流程与状态转换安全，反馈信息明确
- [ ] **c1.4:** 广泛测试所有管理操作的稳定性（待本地模拟器验证）
  - 测试用例: 删除混合收藏状态的多个项目、添加多个项目到收藏、从收藏中移除项目、快速连续操作、有网络延迟的操作
  - 预期结果: 每个操作一个 toast 并准确计数、无UI冻结或无限循环、操作成功完成、收藏项目保护的清晰反馈

### **任务 C2: 修复删除计数器Bug** [估时: 6小时]
- [x] **c2.1:** 在 `Views/QuickResultPageView.swift` (57行) 的 `deleteRecipe()` 方法中添加空条目清理
  - 实现: 在调用 `replaceAll()` 前过滤掉空食谱数组的条目；当当前条目删除到0道菜时不再保留该条目
  - 验证: 空条目自动从管理视图中移除（通过全量替换时额外过滤零菜谱条目）
- [x] **c2.2:** 添加父视图刷新触发器
  - 实现: 在 `replaceAll()` 后通过 `NotificationCenter.default.post(name: .quickHistoryDidChange, ...)` 通知父视图刷新
  - 验证: 最后一个食谱删除后计数器立即更新
- [x] **c2.3:** 增强 `Features/Recipes/QuickHistoryView.swift` 的自动重载能力
  - 实现: 监听 `.quickHistoryDidChange` 通知并调用 `reload()` 自动刷新数据
  - 验证: UI 反映变化无需手动刷新
- [ ] **c2.4:** 测试删除计数器的准确性
  - 测试用例: 从多食谱条目中删除所有食谱、从多个条目中删除食谱、删除食谱后导航到管理页面、验证删除后的容量计算
  - 预期结果: 条目变空时计数器减少、管理页面显示更新的条目计数、容量指示器保持准确、没有零食谱的幽灵条目

### **任务 C3: 实现历史计数逻辑** [估时: 4小时]
- [x] **c3.1:** 在 `Features/Recipes/CapacityIndicatorView.swift` (21行) 更新容量显示
  - 实现: 将 `Text('[\\(count)/\\(capacity)]')` 更新为 `Text('[\\(count)/\\(capacity)] generations')`，并同步更新可访问性标签
  - 验证: 构建通过（iOS Simulator: iPhone 16 Pro），标题清楚地指示基于“generation”的计数
- [x] **c3.2:** 为 `Features/Recipes/QuickHistoryView.swift` 中的快速卡片添加菜品计数徽章
  - 实现: 在水平分页视图上方右侧叠加 'x dishes' 胶囊徽章，随当前卡片切换更新
  - 验证: 徽章准确显示 `numberOfDishes`，无数据时不显示；构建通过
- [x] **c3.3:** 在 `Features/RecipeGenerator/RecipeGeneratorViewModel.swift` 中更新保存 toast 消息
  - 实现: 新增 `saveToastMessage`，在 `acceptQuickPreview()` 保存后设置为 `Saved X dishes (1 generation) to Quick.`；在 `RecipeGeneratorView.swift` 使用通用 `.toast` 展示（成功样式）
  - 验证: 保存后出现清晰的成功提示，并在隐藏后自动清理消息；构建通过
- [ ] **c3.4:** 测试整个UI的计数一致性
  - 测试用例: 生成多个食谱并验证所有UI元素、检查容量指示器、卡片徽章、保存消息、验证管理页面显示生成计数
  - 预期结果: 到处都有一致的'generation'术语、菜品和生成之间的清晰区别、通过清晰标签改善用户理解

---

## 📋 开发包 B: 生成器用户体验与导航 (P1 - 高优先级) [估时: 2-3天]

### **任务 B1: 优化生成器的 Toast (轻提示) 设计** [估时: 8小时]
- [x] **b1.1:** 在 `Features/RecipeGenerator/RecipeGeneratorView.swift` (353行) 重新设计 `GeneratorToastView` 的位置和大小
  - 实现: 将 `.overlay(alignment: .top)` 改为居中覆盖；加入半透明幕布 `Color.black.opacity(0.25).ignoresSafeArea()`；卡片使用 `frame(maxWidth: min(480, width*0.75))` 控制为约 75% 屏宽；动画由顶部进入改为 `scale + opacity`。
  - 验证: 代码层面满足“居中+幕布+中等尺寸”规范，布局自适应不同屏宽
- [x] **b1.2:** 在 toast 的食谱预览中添加烹饪时间
  - 实现: 在 `ToastPreviewView` 中显示 `estimatedTime`（如 "30 min"），并保持与标题同组对齐
  - 验证: 预览列表每项均可见烹饪时间（若有）
- [x] **b1.3:** 当食谱超过3个时，实现 `+N more` 溢出指示器
  - 实现: 保留并完善现有 `+N more` 指示；对 4+ 条目显示正确剩余计数
  - 验证: 4+食谱时溢出指示器显示正确计数
- [x] **b1.4:** 改进"重新生成"和"很好"按钮的样式和层次
  - 实现: 按钮样式为 重新生成 `.bordered`、很好 `.borderedProminent` + `.tint(.accentColor)`；统一 `.controlSize(.large)` 提升可点击性
  - 验证: 视觉主次清晰，主要操作“很好”突出
- [x] **b1.5:** 为 Toast 添加完整的 VoiceOver 可访问性支持
  - 实现: 为每条预览添加可访问性标签（“标题，cooking time X minutes”）；为按钮添加明确的可访问性标签；为覆盖层添加 `.accessibilityAddTraits(.isModal)` 并隐藏幕布可访问性
  - 验证: VoiceOver 可朗读列表项与操作，结构清晰
- [ ] **b1.6:** 在不同尺寸设备上测试新的 Toast 设计
  - 测试用例: iPhone 14 Pro (标准屏幕)、iPhone 15 Pro Max (大屏幕)、仅iOS 17+兼容设备、横向和纵向方向
  - 预期结果: 所有设备尺寸上可读、正确居中和大小、触摸目标大小合适、内容不被截断或重叠

### **任务 B2: 优化生成后的导航** [估时: 4小时]
- [x] **b2.1:** 在 `Features/RecipeGenerator/RecipeGeneratorViewModel.swift` (263行) 的 `acceptQuickPreview()` 方法中添加子标签定位
  - 实现: 在 `switchToTab(3)` 前通过 `UserDefaults.standard.set("quick", forKey: "recipes.selectedTab")` 设置子标签为 Quick
  - 变更文件: `Features/RecipeGenerator/RecipeGeneratorViewModel.swift`
  - 验证: 点击"很好"后用户到达食谱 > 快速子标签
- [x] **b2.2:** 为最新的快速卡片添加3秒"新"徽章
  - 实现: 新增通知 `Notification.Name.quickHistoryNewEntryAdded`；在保存 Quick 条目后发送包含 `id` 的通知；`QuickHistoryView` 监听并对最新卡片显示“NEW”徽章，3秒后自动移除
  - 变更文件: `Services/QuickHistoryManager.swift`、`Features/Recipes/QuickHistoryView.swift`
  - 验证: 新徽章正确出现和消失
- [x] **b2.3:** 确保 `RecipeHistoryTabView.swift` 正确处理外部子标签变化
  - 实现: 已存在 `@AppStorage("recipes.selectedTab")` 监听并在变化时 `withAnimation` 切换；无需代码改动
  - 验证: 子标签响应外部选择变化
- [ ] **b2.4:** 测试导航的一致性
  - 测试用例: 从不同标签导航(食材库、生成器、个人资料)、不同的先前食谱子标签状态、快速导航(多次快速生成)、应用后台/前台导航
  - 预期结果: 无论先前状态如何都到达食谱 > 快速、新徽章一致出现、平滑过渡无UI故障、所有场景行为一致
  - 状态: 已在 iOS 模拟器 (iPhone 16 Pro, iOS 18.5) 成功构建；交互验证待手动运行

---

## 📋 开发包 D: 收藏夹数据完整性 (P1 - 高优先级) [估时: 2-3天]

### **任务 D1: 修复收藏夹显示损坏问题** [估时: 10小时]
- [x] **d1.1:** 在 `Services/FavoritesManager.swift` (80行) 的 `resolveUIModel()` 方法中添加数据验证
  - 实现: 新增最小完整性检查（非空id、非空标题）；仅返回有效的 `RecipeUIModel`
  - 验证: 缺失/损坏数据时返回 `nil`，不再错误导航
- [x] **d1.2:** 在收藏食谱时实现UI快照存储
  - 实现: 新增 `FavoriteSnapshot`（id/title/subtitle/estimatedTime/savedAt）；在新增收藏时自动捕获并持久化到 `FavoritesStore`
  - 验证: 快照可用于后备渲染，跨重启可用
- [x] **d1.3:** 在 `Features/Recipes/FavoritesView.swift` (35行) 为无法解析的收藏项添加后备渲染
  - 实现: 当 `resolveUIModel` 失败时显示快照数据，标注“Unavailable”，并提供“Remove”按钮和侧滑删除
  - 验证: 损坏收藏显示清晰信息与恢复操作，无“数字和字母”乱码
- [x] **d1.4:** 添加食谱ID去重逻辑
  - 实现: `FavoritesManager.allItems()` 增加去重集合，按 `FavoritesStore` 新旧顺序保序去重
  - 验证: 列表无重复项
- [x] **d1.5:** 实现损坏条目移除功能
  - 实现: 新增 `FavoritesManager.removeFavorite(id:)` 清理收藏与快照；UI 中提供移除入口并刷新
  - 验证: 用户可一键清理不可用收藏
- [ ] **d1.6:** 添加全面的收藏夹测试
  - 测试用例: 收藏后从快速/计划删除、损坏ID、混合可用性、全不可用列表、移除后清理
  - 预期结果: 不显示乱码、要么可导航要么可移除、区分清晰、无崩溃
  - 状态: 待本地模拟器/设备完成手测

---

## 📋 跨Bundle任务

### **集成测试** [估时: 8小时] - 依赖: 所有Bundle完成
- [ ] **integration.1:** 完整工作流测试
  - 测试场景: 完整扫描→结果→食材库工作流、完整膳食计划生成周期、快速食谱生成→保存→管理→收藏、跨标签导航和状态一致性
- [ ] **integration.2:** 回归测试
  - 关注领域: 现有食材库功能、个人资料和设置功能、所有标签间导航、性能影响验证
- [ ] **integration.3:** 设备矩阵验证
  - 设备: iPhone 14 Pro (iOS 17+)、iPhone 15 Pro Max (iOS 17+)、iOS模拟器 (iOS 17+)

### **性能验证** [估时: 4小时]
- [ ] **perf.1:** 内存使用验证
  - 关注领域: Toast内存管理、历史清理效率、收藏夹数据处理
- [ ] **perf.2:** UI响应性测试
  - 关注领域: 快速历史操作、管理页面交互、Toast动画

### **发布准备** [估时: 6小时]
- [ ] **release.1:** 成功指标验证
  - 验证点: 相机访问和膳食计划生成0%崩溃率、管理页面0%强制应用重启、到食谱>快速100%导航成功率、收藏夹100%显示有效信息
- [ ] **release.2:** 遥测实现
  - 数据点: 相机访问成功/失败率、膳食计划生成完成率、生成器选择率(重新生成vs很好)、管理操作结果、收藏夹解析失败率
- [ ] **release.3:** 发布说明准备
  - 内容: 修复扫描成分时的相机崩溃、解决膳食计划生成器稳定性问题、改进快速食谱管理和导航、增强生成器toast设计和可用性、修复收藏夹显示和数据完整性问题

---

## 📊 成功标准

### **崩溃消除**
- ✅ 所有设备和配置上相机访问0%崩溃率
- ✅ 所有输入场景下膳食计划生成0%崩溃率
- ✅ 无限alert循环导致的管理页面0%强制应用重启

### **用户体验**
- ✅ 生成器→食谱>快速导航100%成功率
- ✅ 改进的toast设计95%+用户满意度
- ✅ 所有功能中100%准确的计数器和显示

### **数据完整性**
- ✅ 收藏夹100%显示有效信息，优雅后备
- ✅ 0%幽灵条目，准确计数器显示
- ✅ 所有流程中100%一致的成分名称格式

---

## 📝 实施说明

1. **严格按照建议顺序执行**: A → E → C → B → D
2. **每个Bundle完成后进行烟雾测试**
3. **所有Bundle完成后进行完整集成测试**
4. **在真机和模拟器上验证所有修复**
5. **确保所有验收标准在发布前得到满足**
