#!/bin/bash

echo "🚀 Generating IngredientScanner Xcode Project..."

# Check if xcodegen is installed
if ! command -v xcodegen &> /dev/null; then
    echo "❌ xcodegen is not installed. Installing via Homebrew..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "❌ Homebrew is not installed. Please install Homebrew first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    brew install xcodegen
fi

# Generate the Xcode project
echo "📦 Generating Xcode project..."
xcodegen generate

# Check if generation was successful
if [ -d "IngredientScanner.xcodeproj" ]; then
    echo "✅ Xcode project generated successfully!"
    echo "📱 You can now open IngredientScanner.xcodeproj in Xcode"
    echo ""
    echo "⚠️  Don't forget to:"
    echo "   1. Add your API keys in Utilities/APIKeys.swift"
    echo "   2. Select a development team in project settings"
    echo "   3. Build and run the project (⌘+R)"
else
    echo "❌ Failed to generate Xcode project"
    echo "Trying alternative method with Swift Package Manager..."
    
    # Try using swift package generate-xcodeproj as fallback
    swift package generate-xcodeproj
    
    if [ -d "IngredientScanner.xcodeproj" ]; then
        echo "✅ Xcode project generated successfully using Swift Package Manager!"
    else
        echo "❌ Failed to generate Xcode project. Please ensure you have Xcode installed."
    fi
fi 