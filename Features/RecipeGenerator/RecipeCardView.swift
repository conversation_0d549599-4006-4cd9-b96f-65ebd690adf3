import SwiftUI

struct RecipeCardView: View {
    let recipeIdea: RecipeIdea
    @State private var showingDetail = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with status
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(recipeIdea.recipe.recipeTitle)
                        .font(.headline)
                        .lineLimit(2)
                    
                    Text(recipeIdea.recipe.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                Spacer()
                
                StatusBadge(status: recipeIdea.status)
            }
            
            // Nutrition info
            HStack(spacing: 16) {
                NutritionTag(label: "Cal", value: recipeIdea.recipe.nutrition.calories)
                NutritionTag(label: "Protein", value: recipeIdea.recipe.nutrition.protein)
                NutritionTag(label: "Carbs", value: recipeIdea.recipe.nutrition.carbs)
                NutritionTag(label: "Fat", value: recipeIdea.recipe.nutrition.fat)
            }
            .font(.caption)
            
            // Missing ingredients section (if any)
            if recipeIdea.status == .almostThere && !recipeIdea.missingIngredients.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Missing Ingredients:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)

                    ForEach(recipeIdea.missingIngredients, id: \.self) { ingredient in
                        Text("• \(ingredient)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.top, 4)
            }
            
            // View Recipe button
            Button(action: { showingDetail = true }) {
                Text("View Recipe")
                    .font(.caption)
                    .fontWeight(.medium)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(recipeIdea.status == .readyToCook ? Color.green : Color.orange)
                    .foregroundColor(.white)
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        .sheet(isPresented: $showingDetail) {
            GeneratedRecipeDetailView(recipe: recipeIdea.recipe)
        }
    }
}

struct StatusBadge: View {
    let status: RecipeIdea.RecipeStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: status == .readyToCook ? "checkmark.circle.fill" : "cart.badge.plus")
                .font(.caption)
            
            Text(status == .readyToCook ? "Ready" : "Almost")
                .font(.caption)
                .fontWeight(.semibold)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 4)
        .background(status == .readyToCook ? Color.green : Color.orange)
        .foregroundColor(.white)
        .cornerRadius(12)
    }
}

struct NutritionTag: View {
    let label: String
    let value: String
    
    var body: some View {
        VStack(spacing: 2) {
            Text(value)
                .fontWeight(.semibold)
            Text(label)
                .foregroundColor(.secondary)
        }
    }
} 