import SwiftUI

struct ConfigurationSummaryCard: View {
    let summary: String
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.blue)
                    .font(.title3)
                
                Text("Configuration Summary")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }
            
            if isExpanded || summary.count < 100 {
                Text(summary)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .animation(.easeInOut(duration: 0.2), value: summary)
            } else {
                Text(String(summary.prefix(100)) + "...")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
        }
        .padding()
        .background(.blue.opacity(0.05), in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.blue.opacity(0.2), lineWidth: 1)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Configuration Summary")
        .accessibilityValue(summary)
        .accessibilityHint("Double tap to expand or collapse")
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                isExpanded.toggle()
            }
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        ConfigurationSummaryCard(summary: "Planning 3 days of Lunch and Dinner, style: Healthy, using Prefer pantry ingredients")
        ConfigurationSummaryCard(summary: "Please select at least one meal to get started...")
        ConfigurationSummaryCard(summary: "This is a very long configuration summary that should be truncated when collapsed and fully shown when expanded. It contains a lot of details about the user's meal planning preferences and cooking requirements.")
    }
    .padding()
}
