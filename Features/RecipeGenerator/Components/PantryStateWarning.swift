import SwiftUI

struct PantryStateWarning: View {
    let pantryState: PantryState
    let onGoToPantry: () -> Void
    
    var body: some View {
        switch pantryState {
        case .empty:
            emptyPantryWarning
        case .loading:
            loadingPantryView
        case .error(let message):
            errorPantryView(message: message)
        case .hasItems:
            EmptyView() // No warning needed
        }
    }
    
    private var emptyPantryWarning: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(NSLocalizedString("pantry_empty_warning", comment: "Pantry empty warning"))
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text("Add ingredients to get personalized recipe suggestions.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            
            HStack {
                Spacer()
                Button(action: onGoToPantry) {
                    HStack(spacing: 8) {
                        Image(systemName: "basket")
                        Text(NSLocalizedString("pantry_empty_action", comment: "Go to Pantry"))
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
        }
        .padding()
        .background(.orange.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.orange.opacity(0.3), lineWidth: 1)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(NSLocalizedString("pantry_empty_warning", comment: "Pantry empty warning"))
        .accessibilityHint("Tap to go to pantry and add ingredients")
    }
    
    private var loadingPantryView: some View {
        HStack(spacing: 12) {
            ProgressView()
                .scaleEffect(0.8)
            
            Text(NSLocalizedString("pantry_loading", comment: "Checking your pantry..."))
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private func errorPantryView(message: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.circle.fill")
                .foregroundColor(.red)
                .font(.title3)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(NSLocalizedString("pantry_error", comment: "Unable to access your pantry"))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(.red.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.red.opacity(0.3), lineWidth: 1)
        )
    }
}

#Preview {
    VStack(spacing: 16) {
        PantryStateWarning(pantryState: .empty) { }
        PantryStateWarning(pantryState: .loading) { }
        PantryStateWarning(pantryState: .error("Network connection failed")) { }
    }
    .padding()
}
