import SwiftUI

struct ModeSelector: View {
    @Binding var selectedMode: UIMode
    var onChange: ((UIMode) -> Void)? = nil

    var body: some View {
        Picker("Mode", selection: $selectedMode) {
            Text("Quick").tag(UIMode.quick)
            Text("Meal Plan").tag(UIMode.custom)
        }
        .pickerStyle(.segmented)
        .onChange(of: selectedMode) { oldValue, newValue in
            onChange?(newValue)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(Text("Mode selector"))
        .accessibilityHint(Text("Switch between Quick and Meal Plan modes"))
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .frame(minHeight: 44)
    }
}

#Preview {
    StatefulPreviewWrapper(UIMode.quick) { binding in
        ModeSelector(selectedMode: binding)
            .padding()
    }
}

// Helper to preview bindings
struct StatefulPreviewWrapper<Value, Content: View>: View {
    @State var value: Value
    var content: (Binding<Value>) -> Content
    init(_ initialValue: Value, content: @escaping (Binding<Value>) -> Content) {
        _value = State(wrappedValue: initialValue)
        self.content = content
    }
    var body: some View { content($value) }
}

