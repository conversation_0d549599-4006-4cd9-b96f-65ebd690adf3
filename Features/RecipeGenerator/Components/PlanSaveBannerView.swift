import SwiftUI

struct PlanSaveBannerView: View {
    let message: String
    let ctaTitle: String
    var onTapCTA: () -> Void

    var body: some View {
        HStack(alignment: .firstTextBaseline, spacing: 12) {
            Text(message)
                .font(.subheadline.weight(.medium))
                .foregroundStyle(.primary)
                .multilineTextAlignment(.leading)
                .accessibilityLabel(message)

            Spacer(minLength: 12)

            <PERSON><PERSON>(ctaTitle, action: onTapCTA)
                .buttonStyle(.borderedProminent)
        }
        .padding(16)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12, style: .continuous))
        .accessibilityElement(children: .combine)
        .accessibilityHint(NSLocalizedString("meal_plan_save_banner_hint", comment: "Shows the generated meal plan for the selected week"))
    }
}

#Preview("Plan Save Banner") {
    PlanSaveBannerView(
        message: "Added: Mon Lunch ×2, Tue Dinner ×1",
        ctaTitle: "View",
        onTapCTA: {}
    )
    .padding()
}
