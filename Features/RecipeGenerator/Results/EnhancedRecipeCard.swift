import SwiftUI

struct EnhancedRecipeCard: View {
    let item: RecipeUIModel
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Main recipe info
            HStack(alignment: .top, spacing: 12) {
                // Recipe image placeholder or icon
                RoundedRectangle(cornerRadius: 8)
                    .fill(.gray.opacity(0.2))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: "fork.knife")
                            .font(.title2)
                            .foregroundColor(.gray)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                        .lineLimit(2)
                    
                    if let subtitle = item.subtitle {
                        Text(subtitle)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(isExpanded ? nil : 2)
                    }
                    
                    // Recipe metadata
                    HStack(spacing: 12) {
                        if let time = item.estimatedTime {
                            Label("\(time) min", systemImage: "clock")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        
                        if let difficulty = item.difficulty {
                            Label(difficulty.capitalized, systemImage: "star")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                        
                        if let servings = item.servings {
                            Label("\(servings) servings", systemImage: "person.2")
                                .font(.caption)
                                .foregroundStyle(.secondary)
                        }
                    }
                }
                
                Spacer()
                
                // Expand/collapse button
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Expanded content
            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    // Pantry ingredients
                    if let pantryIngredients = item.ingredientsFromPantry, !pantryIngredients.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: "basket.fill")
                                    .foregroundColor(.green)
                                    .font(.caption)
                                Text("From your pantry:")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)
                            }
                            
                            Text(pantryIngredients.joined(separator: ", "))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading, 16)
                        }
                    }
                    
                    // Additional ingredients
                    if let additionalIngredients = item.additionalIngredients, !additionalIngredients.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: "cart")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                                Text("You may need:")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.orange)
                            }
                            
                            Text(additionalIngredients.joined(separator: ", "))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading, 16)
                        }
                    }
                    
                    // Cuisine info
                    if let cuisine = item.cuisine {
                        HStack {
                            Image(systemName: "globe")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text("Cuisine: \(cuisine)")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(.gray.opacity(0.2), lineWidth: 1)
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel(item.title)
        .accessibilityValue(item.subtitle ?? "")
        .accessibilityHint("Double tap to expand details")
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.2)) {
                isExpanded.toggle()
            }
        }
    }
}

#Preview {
    VStack(spacing: 16) {
        EnhancedRecipeCard(
            item: RecipeUIModel(
                id: "1",
                title: "Creamy Mushroom Pasta",
                subtitle: "A rich and satisfying pasta dish with fresh mushrooms and herbs",
                estimatedTime: 25,
                ingredientsFromPantry: ["Pasta", "Mushrooms", "Garlic", "Onion"],
                additionalIngredients: ["Heavy Cream", "Parmesan Cheese", "Fresh Herbs"],
                difficulty: "Medium",
                mealType: .dinner,
                dayIndex: 0,
                servings: 4,
                cuisine: "Italian"
            )
        )
        
        EnhancedRecipeCard(
            item: RecipeUIModel(
                id: "2",
                title: "Quick Breakfast Scramble",
                subtitle: "Protein-packed morning meal",
                estimatedTime: 10,
                ingredientsFromPantry: ["Eggs", "Bell Pepper"],
                additionalIngredients: ["Cheese"],
                difficulty: "Easy",
                mealType: .breakfast,
                dayIndex: 0,
                servings: 2
            )
        )
    }
    .padding()
}
