import SwiftUI

struct GroupedResultsView: View {
    let items: [RecipeUIModel]
    let days: Int
    let selectedMeals: Set<MealType>

    var body: some View {
        let result = RecipeGrouper.group(items, days: days, selectedMeals: selectedMeals)

        VStack(alignment: .leading, spacing: 16) {
            // Generation Summary
            GenerationSummaryView(summary: result.summary)

            // Grouped Results
            List {
                ForEach(result.sections) { day in
                    Section(day.displayDate) {
                        ForEach(day.meals) { meal in
                            MealSectionView(meal: meal)
                        }
                    }
                }
            }
            .listStyle(.insetGrouped)
        }
    }
}

struct MealSectionView: View {
    let meal: MealSection

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Meal header with time and pantry usage
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(meal.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack(spacing: 12) {
                        Label("\(meal.totalTime) min", systemImage: "clock")
                            .font(.caption)
                            .foregroundStyle(.secondary)

                        Label("\(meal.dishes.count) dish\(meal.dishes.count == 1 ? "" : "es")", systemImage: "fork.knife")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }

                Spacer()

                // Pantry utilization indicator
                VStack(alignment: .trailing, spacing: 4) {
                    Text(String(format: "%.0f%% pantry", meal.pantryUsage.utilizationRate * 100))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundStyle(.secondary)

                    ProgressView(value: meal.pantryUsage.utilizationRate)
                        .tint(meal.pantryUsage.utilizationRate > 0.7 ? .green : meal.pantryUsage.utilizationRate > 0.4 ? .orange : .red)
                        .frame(width: 60)
                }
            }

            // Recipe cards
            LazyVStack(spacing: 8) {
                ForEach(meal.dishes, id: \.id) { item in
                    EnhancedRecipeCard(item: item)
                }
            }
        }
        .padding(.vertical, 8)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(meal.displayName) meal with \(meal.dishes.count) recipes")
        .accessibilityValue("Total cooking time: \(meal.totalTime) minutes, Pantry utilization: \(Int(meal.pantryUsage.utilizationRate * 100))%")
    }
}

struct GenerationSummaryView: View {
    let summary: GenerationSummary

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Meal Plan Summary")
                .font(.headline)
                .foregroundColor(.primary)

            HStack(spacing: 20) {
                SummaryItem(
                    icon: "fork.knife",
                    title: "Recipes",
                    value: "\(summary.totalRecipes)",
                    color: .blue
                )

                SummaryItem(
                    icon: "calendar",
                    title: "Days",
                    value: "\(summary.totalDays)",
                    color: .green
                )

                SummaryItem(
                    icon: "clock",
                    title: "Avg Time",
                    value: "\(summary.avgCookingTime)m",
                    color: .orange
                )

                SummaryItem(
                    icon: "basket",
                    title: "Pantry Use",
                    value: "\(Int(summary.pantryUtilization * 100))%",
                    color: .purple
                )
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

struct SummaryItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    let items: [RecipeUIModel] = [
        RecipeUIModel(
            id: "1",
            title: "Creamy Mushroom Pasta",
            subtitle: "Rich and satisfying",
            estimatedTime: 25,
            ingredientsFromPantry: ["Pasta", "Mushrooms", "Garlic"],
            additionalIngredients: ["Cream", "Parmesan"],
            difficulty: "Medium",
            mealType: .dinner,
            dayIndex: 0,
            servings: 4,
            cuisine: "Italian"
        ),
        RecipeUIModel(
            id: "2",
            title: "Fresh Garden Salad",
            subtitle: "Light and healthy",
            estimatedTime: 10,
            ingredientsFromPantry: ["Lettuce", "Tomatoes"],
            additionalIngredients: ["Feta", "Olive Oil"],
            difficulty: "Easy",
            mealType: .lunch,
            dayIndex: 0,
            servings: 2,
            cuisine: "Mediterranean"
        )
    ]
    return GroupedResultsView(items: items, days: 2, selectedMeals: [.lunch, .dinner])
}

