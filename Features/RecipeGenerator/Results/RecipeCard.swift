import SwiftUI

struct RecipeItemCard: View {
    let item: RecipeUIModel

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(item.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    if let sub = item.subtitle, !sub.isEmpty {
                        Text(sub)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                Spacer(minLength: 8)
                if let mins = item.estimatedTime {
                    Label("\(mins) min", systemImage: "clock")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            if let fromPantry = item.ingredientsFromPantry, !fromPantry.isEmpty {
                Text("ingredients_from_pantry_prefix")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(fromPantry.joined(separator: ", "))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            if let add = item.additionalIngredients, !add.isEmpty {
                Text("additional_ingredients_prefix")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(add.joined(separator: ", "))
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        .accessibilityElement(children: .combine)
        .accessibilityLabel(Text(item.title))
        .accessibilityValue(Text(item.subtitle ?? ""))
        .accessibilityHint(Text("a11y_recipe_card_hint"))
    }
}

#Preview {
    RecipeItemCard(item: .init(id: "1", title: "Tomato Pasta", subtitle: "Simple and tasty", estimatedTime: 20, imageURL: nil, ingredientsFromPantry: ["Tomato", "Pasta"], additionalIngredients: ["Basil"], difficulty: "easy"))
        .padding()
}

