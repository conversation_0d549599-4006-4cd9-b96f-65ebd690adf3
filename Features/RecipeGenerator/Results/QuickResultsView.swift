import SwiftUI

struct QuickResultsView: View {
    let state: ViewState
    var onCancel: (() -> Void)? = nil
    var onRetry: (() -> Void)? = nil

    var body: some View {
        Group {
            switch state {
            case .idle:
                idleView
            case .loading:
                loadingView
            case .loaded(let items):
                loadedView(items)
            case .failed(let error):
                failedView(error)
            }
        }
    }

    private var idleView: some View {
        VStack(spacing: 12) {
            Image(systemName: "sparkles")
                .font(.system(size: 44))
                .foregroundStyle(.secondary)
            Text("results_idle_quick")
                .font(.body)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, minHeight: 180)
        .accessibilityLabel(Text("a11y_results_idle"))
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
            Text("results_loading")
                .foregroundStyle(.secondary)
            if let onCancel { Button("action_cancel", action: onCancel) }
        }
        .redacted(reason: .placeholder)
        .frame(maxWidth: .infinity, minHeight: 180)
        .accessibilityLabel(Text("a11y_results_loading"))
    }

    private func loadedView(_ items: [RecipeUIModel]) -> some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(items, id: \.id) { item in
                    RecipeItemCard(item: item)
                }
            }
            .padding(.vertical, 4)
        }
        .accessibilityLabel(Text("a11y_results_loaded"))
    }

    private func failedView(_ error: DisplayError) -> some View {
        VStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 44))
                .foregroundStyle(.red)
            Text(error.errorDescription ?? "Unknown error")
                .multilineTextAlignment(.center)
            if let onRetry {
                Button("action_retry", action: onRetry)
                    .buttonStyle(.bordered)
            }
        }
        .frame(maxWidth: .infinity, minHeight: 180)
        .accessibilityLabel(Text("a11y_results_failed"))
    }
}

#Preview {
    VStack(spacing: 16) {
        QuickResultsView(state: .idle)
        QuickResultsView(state: .loading)
        QuickResultsView(state: .loaded([
            .init(id: "1", title: "Pasta", subtitle: "Tasty", estimatedTime: 20, imageURL: nil, ingredientsFromPantry: ["Pasta"], additionalIngredients: ["Basil"], difficulty: "easy")
        ]))
        QuickResultsView(state: .failed(.serviceError(message: "Oops")))
    }
    .padding()
}

