import SwiftUI

enum RecipeHistoryTab: Hashable, CaseIterable {
    case quick
    case plans
    case favorites

    var title: String {
        switch self {
        case .quick: return "Quick"
        case .plans: return "Plans"
        case .favorites: return "Favorites"
        }
    }
}

struct RecipeHistoryTabView: View {
    @State private var selected: RecipeHistoryTab = .quick
    @AppStorage("recipes.selectedTab") private var selectedTabPref: String = "quick"
    @State private var perfMonitor = PerformanceMonitor()

    var body: some View {
        VStack(spacing: 12) {
            Picker("Recipes", selection: $selected) {
                ForEach(RecipeHistoryTab.allCases, id: \.self) { tab in
                    Text(tab.title).tag(tab)
                }
            }
            .pickerStyle(.segmented)
            .padding(.horizontal)
            .onAppear {
                // Restore preferred tab when entering Recipes
                if let pref = RecipeHistoryTab(from: selectedTabPref) {
                    selected = pref
                }
                // Begin listening for memory pressure and capture initial paint
                MemoryManager.shared.start()
                perfMonitor.start(screen: "Recipes/\(selected.title)")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    perfMonitor.stopAndReport()
                }
            }
            .onChange(of: selected) { _, newValue in
                // Persist user choice for next navigation
                selectedTabPref = newValue.stringValue
                // Measure tab switch first paint
                perfMonitor.stopAndReport()
                perfMonitor.start(screen: "Recipes/\(newValue.title)")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    perfMonitor.stopAndReport()
                }
            }
            .onChange(of: selectedTabPref) { _, newRaw in
                // React to external requests (e.g., Favorites empty state switching tabs)
                if let pref = RecipeHistoryTab(from: newRaw) {
                    withAnimation { selected = pref }
                }
            }

            ZStack(alignment: .topLeading) {
                QuickHistoryView()
                    .opacity(selected == .quick ? 1 : 0)
                    .accessibilityHidden(selected != .quick)
                    .allowsHitTesting(selected == .quick)

                PlansHistoryView()
                    .opacity(selected == .plans ? 1 : 0)
                    .accessibilityHidden(selected != .plans)
                    .allowsHitTesting(selected == .plans)

                FavoritesView()
                    .opacity(selected == .favorites ? 1 : 0)
                    .accessibilityHidden(selected != .favorites)
                    .allowsHitTesting(selected == .favorites)
            }
        }
        .navigationTitle("Recipes")
    }
}

private extension RecipeHistoryTab {
    init?(from raw: String) {
        switch raw {
        case "quick": self = .quick
        case "plans": self = .plans
        case "favorites": self = .favorites
        default: return nil
        }
    }
    var stringValue: String {
        switch self {
        case .quick: return "quick"
        case .plans: return "plans"
        case .favorites: return "favorites"
        }
    }
}

#Preview("Recipe History Tabs") {
    NavigationStack { RecipeHistoryTabView() }
}
