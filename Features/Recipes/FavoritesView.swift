import SwiftUI

struct FavoritesView: View {
    @State private var items: [FavoriteItem] = []
    @AppStorage("recipes.selectedTab") private var selectedRecipesTab: String = "quick"

    var body: some View {
        Group {
            if items.isEmpty {
                FavoritesEmptyStateView()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .accessibilityLabel("Favorites empty state")
            } else {
                List {
                    Section("Favorites") {
                        ForEach(items) { item in
                            if let ui = FavoritesManager.shared.resolveUIModel(for: item.id) {
                                NavigationLink(destination: GeneratedRecipeDetailView(recipeUIModel: ui)) {
                                    row(for: item)
                                }
                                .buttonStyle(.plain)
                            } else {
                                fallbackRow(for: item)
                            }
                        }
                    }
                }
                .listStyle(.insetGrouped)
                .accessibilityLabel("Favorites list")
            }
        }
        .onAppear { reload() }
    }

    private func row(for item: FavoriteItem) -> some View {
        HStack(alignment: .center, spacing: 12) {
            Image(systemName: "heart.fill").foregroundStyle(.red)
            VStack(alignment: .leading, spacing: 2) {
                Text(item.title)
                    .font(.headline)
                if let ctx = item.primaryContext {
                    Text(contextSummary(ctx))
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                }
            }
            Spacer()
        }
        .contentShape(Rectangle())
        .contextMenu {
            // Quick jump actions back to origin tabs
            if item.contexts.contains(where: { if case .quick = $0 { return true } else { return false } }) {
                Button("View in Quick", systemImage: "clock") { selectedRecipesTab = "quick" }
            }
            if item.contexts.contains(where: { if case .plan = $0 { return true } else { return false } }) {
                Button("View in Plans", systemImage: "calendar") { selectedRecipesTab = "plans" }
            }
        }
    }

    private func fallbackRow(for item: FavoriteItem) -> some View {
        HStack(alignment: .center, spacing: 12) {
            Image(systemName: "heart.slash").foregroundStyle(.orange)
            VStack(alignment: .leading, spacing: 2) {
                Text(item.snapshot?.title ?? item.title)
                    .font(.headline)
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.triangle.fill").font(.caption2)
                    Text("Unavailable")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                .accessibilityLabel("Recipe unavailable")
                if let subtitle = item.snapshot?.subtitle, !subtitle.isEmpty {
                    Text(subtitle)
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                }
                if let time = item.snapshot?.estimatedTime {
                    Text("~ \(time) min")
                        .font(.caption2)
                        .foregroundStyle(.secondary)
                }
            }
            Spacer()
            Button("Remove") {
                FavoritesManager.shared.removeFavorite(id: item.id)
                reload()
            }
            .buttonStyle(.bordered)
            .accessibilityLabel("Remove unavailable favorite")
        }
        .contentShape(Rectangle())
        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
            Button(role: .destructive) {
                FavoritesManager.shared.removeFavorite(id: item.id)
                reload()
            } label: {
                Label("Remove", systemImage: "trash")
            }
        }
    }

    private func contextSummary(_ origin: FavoriteOrigin) -> String {
        switch origin {
        case .quick(_, let generatedAt):
            let f = DateFormatter(); f.dateStyle = .medium; f.timeStyle = .short
            return "From Quick • " + f.string(from: generatedAt)
        case .plan(_, let date, let meal):
            let f = DateFormatter(); f.dateStyle = .medium
            return "From Plans • " + f.string(from: date) + " • " + meal.displayName
        }
    }

    private func reload() { items = FavoritesManager.shared.allItems() }
}

#Preview("Favorites") {
    NavigationStack { FavoritesView() }
}
