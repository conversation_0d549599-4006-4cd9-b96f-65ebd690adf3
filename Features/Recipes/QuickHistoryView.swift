import SwiftUI
import Combine

struct QuickHistoryView: View {
    @State private var quickItems: [QuickResultHistory] = []
    @State private var showManage: Bool = false
    @State private var currentIndex: Int = 0
    @State private var newEntryId: UUID? = nil

    var body: some View {
        VStack(spacing: 8) {
            // Capacity header always visible
            CapacityIndicatorView(
                count: quickItems.count,
                capacity: QuickHistoryManager.shared.capacity,
                onManage: { showManage = true }
            )
            .padding(.horizontal)
            .padding(.top, 12)

            Group {
                if quickItems.isEmpty {
                    QuickEmptyStateView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.vertical, 24)
                } else {
                    // Horizontal paging cards with vertical scrolling inside pages
                    HorizontalQuickResultsView(items: quickItems, currentIndex: $currentIndex)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .overlay(alignment: .topLeading) {
                            if let first = quickItems.first,
                               currentIndex == 0,
                               newEntryId == first.id {
                                Text("NEW")
                                    .font(.caption2.weight(.heavy))
                                    .foregroundStyle(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.accentColor, in: Capsule())
                                    .padding(.top, 8)
                                    .padding(.leading, 12)
                                    .accessibilityLabel("Newly added")
                                    .transition(.opacity)
                            }
                        }
                        .overlay(alignment: .topTrailing) {
                            if currentIndex < quickItems.count {
                                let dishes = max(0, quickItems[currentIndex].numberOfDishes)
                                if dishes > 0 {
                                    Text("\(dishes) dish\(dishes == 1 ? "" : "es")")
                                        .font(.caption2.weight(.semibold))
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(.ultraThinMaterial, in: Capsule())
                                        .overlay(
                                            Capsule().stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                                        )
                                        .padding(.top, 8)
                                        .padding(.trailing, 12)
                                        .accessibilityLabel("\(dishes) dishes in this generation")
                                }
                            }
                        }
                        .accessibilityIdentifier("quick_horizontal_pager")

                    // Page indicator (dots by default)
                    PageIndicatorView(count: quickItems.count, currentIndex: currentIndex, mode: .dots)
                        .padding(.bottom, 8)
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
        .onAppear { reload() }
        .onReceive(NotificationCenter.default.publisher(for: .quickHistoryDidChange)) { _ in
            reload()
        }
        .onReceive(NotificationCenter.default.publisher(for: .quickHistoryNewEntryAdded)) { note in
            if let id = note.userInfo?["id"] as? UUID {
                newEntryId = id
                // Auto-clear the badge after 3 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    withAnimation { newEntryId = nil }
                }
            } else {
                // Fallback: show badge on first item briefly
                newEntryId = quickItems.first?.id
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    withAnimation { newEntryId = nil }
                }
            }
        }
        .onChange(of: quickItems.count) { _, _ in
            // Ensure index stays in range when data changes
            currentIndex = min(max(currentIndex, 0), max(quickItems.count - 1, 0))
        }
        .animation(.default, value: quickItems.count)
        .sheet(isPresented: $showManage) {
            ManageSelectionView(context: .quick(items: quickItems)) {
                reload()
            }
        }
        .accessibilityLabel("Quick history horizontal view")
    }

    private func reload() {
        quickItems = QuickHistoryManager.shared.all()
        currentIndex = 0
    }
}

#Preview("Quick History") {
    NavigationStack { QuickHistoryView() }
}
