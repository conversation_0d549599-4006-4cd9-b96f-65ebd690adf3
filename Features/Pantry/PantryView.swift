import SwiftUI

struct PantryView: View {
    @Environment(PantryService.self) var pantryService: PantryService

    var body: some View {
        PantryContentView(pantryService: pantryService)
    }
}

struct PantryContentView: View {
    @State(initialValue: nil) private var viewModel: PantryViewModel?
    let pantryService: PantryService
    @Environment(ExpirationAlertManager.self) private var alertMgr: ExpirationAlertManager

    init(pantryService: PantryService) {
        self.pantryService = pantryService
    }

    // Computed search suggestions based on ingredient library only
    private var searchSuggestions: [String] {
        IngredientLibrary.shared.allIngredientNames
    }

    var body: some View {
        Group {
            if let viewModel = viewModel {
                ZStack {
                    VStack {
                    List {
                        if pantryService.pantryItems.isEmpty {
                            emptyPantryView
                        } else {

                            // Needs Attention section
                            let due = alertMgr.dueItems(from: pantryService.pantryItems)
                            if !due.isEmpty {
                                Section(header: HStack { Text("Needs Attention").font(.headline); Text("(\(due.count))").font(.caption).foregroundColor(.secondary) }) {
                                    ForEach(due) { ingredient in
                                        HStack {
                                            Text(ingredient.name)
                                                .frame(maxWidth: .infinity, alignment: .leading)
                                            Button("Still Fresh") { acknowledgeStillFresh(ingredient) }
                                                .buttonStyle(.bordered)
                                            Button(role: .destructive) { removeItem(ingredient) } label: { Text("Remove") }
                                                .buttonStyle(.bordered)
                                        }
                                    }
                                }
                            }

                            // Categorized Items with improved layout
                            ForEach(viewModel.categorizedPantryItems, id: \.category) { categoryGroup in
                                Section(header: categoryHeader(for: categoryGroup.category, count: categoryGroup.ingredients.count)) {
                                    // Use Grid for better ingredient organization
                                    ForEach(categoryGroup.ingredients) { ingredient in
                                        ingredientRow(ingredient: ingredient, category: categoryGroup.category)
                                    }
                                }
                            }
                        }

                    }
                    .listStyle(.plain)
                    .listSectionSpacing(.compact)
                }
                .navigationTitle("My Pantry")
                .navigationBarTitleDisplayMode(.large)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Menu {
                            Picker("Sort", selection: Binding(
                                get: { viewModel.sortMode },
                                set: { viewModel.sortMode = $0 }
                            )) {
                                ForEach(PantryViewModel.SortMode.allCases) { mode in
                                    Text(mode.rawValue).tag(mode)
                                }
                            }
                        } label: {
                            Label("Sort", systemImage: "arrow.up.arrow.down")
                        }
                    }

                    // Organizer feature removed in V5

                    ToolbarItem(placement: .navigationBarTrailing) {
                        HStack {
                            Button(action: {
                                viewModel.showingAddIngredient = true
                            }) {
                                Image(systemName: "plus")
                                    .font(.title2)
                            }
                            .sensoryFeedback(.impact, trigger: viewModel.showingAddIngredient)
                        }
                    }
                }
                .sheet(isPresented: Binding(
                    get: { viewModel.showingAddIngredient },
                    set: { viewModel.showingAddIngredient = $0 }
                )) {
                    AddToPantryView(viewModel: viewModel)
                }
                // Remove edit-ingredient sheet per request
                .overlay(expirationModalOverlay)

                // Organizer UI removed in V5
                } // End ZStack
            } else {
                ProgressView("Loading pantry...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .task {
            if viewModel == nil {
                viewModel = PantryViewModel(pantryService: pantryService)
            }
        }
    }

    // MARK: - Expiration Overlay
    private var expirationModalOverlay: some View {
        Group {
            if alertMgr.showModal {
                ExpirationAlertView(dueCount: alertMgr.dueItems(from: pantryService.pantryItems).count)
                    .transition(.opacity)
            }
        }
    }

    private func acknowledgeStillFresh(_ ingredient: Ingredient) {
        let cal = Calendar.current
        let today = cal.startOfDay(for: Date())
        let start = cal.startOfDay(for: ingredient.purchaseDate)
        let days = max(0, cal.dateComponents([.day], from: start, to: today).day ?? 0)
        let currentCycle = max(0, days / 7)
        Task { await pantryService.updateNotificationCycles([ingredient.id: currentCycle]) }
    }

    private func removeItem(_ ingredient: Ingredient) {
        Task { await pantryService.deleteIngredient(ingredient) }
    }

    // MARK: - Helper Views

    private var emptyPantryView: some View {
        VStack(spacing: 16) {
            Image(systemName: "cabinet")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.5))

            Text("Your pantry is empty")
                .font(.title2.weight(.medium))
                .foregroundColor(.secondary)

            Text("Start by scanning ingredients or add them manually")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button(action: {
                viewModel?.showingAddIngredient = true
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("Add First Ingredient")
                }
                .font(.headline)
                .contentMargins(.horizontal, 20)
                .contentMargins(.vertical, 12)
                .background(.tint, in: RoundedRectangle(cornerRadius: 10))
                .foregroundColor(.white)
            }
            .sensoryFeedback(.impact, trigger: viewModel?.showingAddIngredient ?? false)
        }
        .contentMargins(.all, 40)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }

    // Modern Grid Cell for ingredients
    private func ingredientGridCell(ingredient: Ingredient, isRecentlyAdded: Bool) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(ingredient.category.icon)
                    .font(.title2)

                VStack(alignment: .leading, spacing: 2) {
                    // Top line: name, single line only
                    Text(ingredient.name)
                        .font(.body.weight(.medium))
                        .lineLimit(1)

                    // Warning badge placed on its own line to avoid truncation
                    if let viewModel = viewModel, viewModel.isExpired(ingredient) {
                        HStack(spacing: 4) {
                            Text("⚠️").font(.caption).accessibilityHidden(true)
                            Text("May be expired")
                                .font(.caption2.weight(.semibold))
                        }
                        .foregroundColor(.yellow)
                    }

                    // Storage duration instead of calendar date (e.g., 1 day, 5 days, 1 week)
                    if let viewModel = viewModel {
                        Text(viewModel.storageDurationLabel(for: ingredient))
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }

                }

                Spacer()

                // remove trailing accessory
            }
        }
        .padding(10)
        .frame(minHeight: 64)
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 10))
        .onTapGesture {
            if viewModel?.isEditMode == true {
                viewModel?.toggleItemSelection(ingredient.id)
            }
        }
        .sensoryFeedback(.selection, trigger: viewModel?.selectedItems.contains(ingredient.id) ?? false)
    }

    private func categoryHeader(for category: PantryCategory, count: Int) -> some View {
        HStack {
            HStack(spacing: 8) {
                Text(category.icon)
                    .font(.title3)

                Text(category.rawValue)
                    .font(.headline)

                Text("(\(count))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            // 🔥 REMOVED: Category selection checkbox to prevent accidental mass selection
            // This was causing users to accidentally select entire categories when they only wanted individual items
        }
    }

    private func bulkActionControls(viewModel: PantryViewModel) -> some View {
        HStack {
            // 🔥 REMOVED: "Select All" button to prevent accidental clicks
            // Users were accidentally clicking this instead of "Delete Selected"

            Spacer()

            // 🔥 COMPLETE REWRITE: Simple, static delete button with snapshot
            Button("Delete Selected") {
                // 🔍 DETAILED DEBUG: Track state when delete button is clicked
                print("🔍 DEBUG: Delete Selected button clicked")
                print("🔍 DEBUG: selectedItems.count = \(viewModel.selectedItems.count)")
                print("🔍 DEBUG: selectedItems = \(viewModel.selectedItems)")

                // 🔥 Use snapshot mechanism to prevent state pollution
                viewModel.prepareDeletion()

                print("🔍 DEBUG: prepareDeletion called")
            }
            .disabled(viewModel.selectedItems.isEmpty)
            .foregroundColor(.red)
            .alert("Delete Items", isPresented: Binding(
                get: { viewModel.showDeleteConfirmation },
                set: { viewModel.showDeleteConfirmation = $0 }
            )) {
                Button("Delete", role: .destructive) {
                    viewModel.deleteSelectedItemsNow()
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                // 🔥 SNAPSHOT MECHANISM: Use captured snapshot data to prevent state pollution
                Text("Delete \(viewModel.getDeleteSnapshotCount()) item\(viewModel.getDeleteSnapshotCount() == 1 ? "" : "s")?\n\n\(viewModel.getDeleteSnapshotNames())")
            }
        }
        .padding(.horizontal)
    }

    private func deleteItems(from category: PantryCategory, at offsets: IndexSet) {
        guard let viewModel = viewModel else { return }
        // Get the ingredients for this category
        let ingredientsInCategory = viewModel.categorizedPantryItems
            .first(where: { $0.category == category })?.ingredients ?? []
        // Delete each ingredient at the given offsets
        for offset in offsets {
            if offset < ingredientsInCategory.count {
                let ingredient = ingredientsInCategory[offset]
                viewModel.deleteIngredient(ingredient, from: category)
            }
        }
    }

    // MARK: - New Row UI (one ingredient per row)
    private func ingredientRow(ingredient: Ingredient, category: PantryCategory) -> some View {
        let isRecent = pantryService.isRecentlyAdded(ingredient)
        return HStack(spacing: 12) {
            // Name (no per-item emoji)
            Text(ingredient.name)
                .font(.body)
                .lineLimit(1)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Duration (days/weeks)
            if let viewModel = viewModel {
                Text(viewModel.storageDurationLabel(for: ingredient))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }

            // Expiration indicator (only when expired)
            if let viewModel = viewModel, viewModel.isExpired(ingredient) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.yellow)
                    .font(.caption)
            }

            // Trash (instant delete)
            Button(action: {
                viewModel?.deleteIngredient(ingredient, from: category)
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
            .buttonStyle(.plain)
        }
        .contentShape(Rectangle())
        .padding(.vertical, 8)
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .fill(isRecent ? Color.accentColor.opacity(0.12) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 10, style: .continuous)
                .stroke(isRecent ? Color.accentColor.opacity(0.8) : Color.clear, lineWidth: isRecent ? 1 : 0)
        )
        .animation(.easeInOut(duration: 0.25), value: isRecent)
    }
}

// MARK: - Add to Pantry View

struct AddToPantryView: View {
    @Bindable var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(PantryService.self) private var pantryService: PantryService
    @State private var selectedCategory: PantryCategory? = nil
    @State private var selectedNames: Set<String> = []
    @State private var searchText: String = ""
    @State private var expandedCategories: Set<PantryCategory> = []
    @FocusState private var searchFocused: Bool
    @State private var addedPillText: String? = nil

    private var categories: [PantryCategory] {
        [
            .produce, .proteins, .seafood, .dairy, .plantBasedAlternatives, .bakery,
            .grainsPastaLegumes, .spicesAndSeasonings, .oilsVinegarsAndCondiments,
            .cannedAndBroths, .nutsAndSeeds, .snacks, .bakingAndSweeteners, .other
        ]
    }

    private func items(for category: PantryCategory) -> [String] {
        IngredientLibrary.shared.items(for: category).filter { item in
            searchText.isEmpty || item.lowercased().hasPrefix(searchText.lowercased())
        }
    }

    var body: some View {
        NavigationStack {
            content
                .navigationTitle("Add Ingredients")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancel") { dismiss() }
                    }
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Save") {
                            Task {
                                let ok = await viewModel.saveAddSheet(selectedNames: selectedNames)
                                if ok { dismiss() }
                            }
                        }
                        .disabled(selectedNames.isEmpty && viewModel.addSheetCustomInputs.isEmpty)
                    }
                }
                .navigationDestination(for: PantryCategory.self) { category in
                    CategoryIngredientsView(category: category, selectedNames: $selectedNames)
                }
        }
    }

    @ViewBuilder
    private var content: some View {
        VStack(spacing: 12) {
            // 搜索输入
            HStack {
                TextField("Type ingredient (e.g., 'avo')", text: $searchText)
                    .textFieldStyle(.roundedBorder)
                    .focused($searchFocused)
            }
            .padding(.horizontal)

            // 已选择（库内）与自定义输入 chips
            if !selectedNames.isEmpty || !viewModel.addSheetCustomInputs.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    if !selectedNames.isEmpty {
                        Text("Selected")
                            .font(.caption).foregroundColor(.secondary)
                            .padding(.horizontal)
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(Array(selectedNames).sorted(), id: \.self) { name in
                                    ingredientChip(name)
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                    if !viewModel.addSheetCustomInputs.isEmpty {
                        Text("Custom Inputs")
                            .font(.caption).foregroundColor(.secondary)
                            .padding(.horizontal)
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(viewModel.addSheetCustomInputs, id: \.self) { name in
                                    customInputChip(name)
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }
            }

            let trimmed = searchText.trimmingCharacters(in: .whitespaces)
            if !trimmed.isEmpty {
                let results = IngredientLibrary.shared.suggest(prefix: trimmed.lowercased(), limit: 200)
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 8) {
                        if results.isEmpty {
                            // 顶部行：添加自定义
                            Button(action: {
                                let q = trimmed
                                if !q.isEmpty {
                                    if !viewModel.addSheetCustomInputs.contains(where: { $0.caseInsensitiveCompare(q) == .orderedSame }) {
                                        viewModel.addSheetCustomInputs.append(q)
                                    }
                                    addedPillText = "Added: \(q)"
                                    searchText = ""
                                    // keep focus
                                    DispatchQueue.main.async { searchFocused = true }
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) { addedPillText = nil }
                                }
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill").foregroundStyle(.tint)
                                    Text("Add \"\(trimmed)\" as custom ingredient")
                                        .font(.body)
                                    Spacer()
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 10))
                            }
                            .buttonStyle(.plain)
                            .padding(.horizontal)
                        }

                        // 建议列表：点按立即加入并清空输入
                        ForEach(results, id: \.self) { name in
                            Button(action: {
                                selectedNames.insert(name)
                                addedPillText = "Added: \(name)"
                                searchText = ""
                                DispatchQueue.main.async { searchFocused = true }
                                DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) { addedPillText = nil }
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: "circle").foregroundColor(.secondary)
                                    Text(name)
                                        .font(.body)
                                        .lineLimit(1)
                                    Spacer()
                                }
                                .padding(.vertical, 6)
                                .padding(.horizontal, 10)
                                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
                            }
                            .buttonStyle(.plain)
                            .padding(.horizontal)
                        }
                    }
                    .padding(.top, 4)
                }
            } else {
                // 分类模式：展示大类，点击进入分类详情页
                categoryListNavigator
            }
        }
        .overlay(alignment: .top) {
            if let pill = addedPillText {
                Text(pill)
                    .font(.caption.weight(.semibold))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(.thinMaterial, in: Capsule())
                    .padding(.top, 6)
                    .transition(.opacity)
            }
        }
        .onAppear { searchFocused = true }
        .alert(
            "Gemini Error",
            isPresented: Binding(
                get: { viewModel.addSheetError != nil },
                set: { show in if !show { viewModel.addSheetError = nil } }
            ),
            actions: {
                Button("OK", role: .cancel) { viewModel.addSheetError = nil }
            },
            message: {
                Text(viewModel.addSheetError ?? "Unknown error")
            }
        )
    }

    @ViewBuilder
    private var categoryListNavigator: some View {
        List {
            ForEach(categories, id: \.self) { category in
                NavigationLink(value: category) {
                    HStack {
                        Text(category.icon)
                        Text(category.rawValue)
                            .font(.headline)
                        Spacer()
                        if selectedCount(for: category) > 0 {
                            Text("\(selectedCount(for: category)) selected")
                                .font(.footnote)
                                .foregroundStyle(.tint)
                        }
                    }
                }
            }
        }
        .listStyle(.insetGrouped)
    }

    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: 8), count: 3)
    }

    @ViewBuilder
    private func ingredientChip(_ name: String) -> some View {
        let isSelected = selectedNames.contains(name)
        Button(action: {
            if isSelected { selectedNames.remove(name) } else { selectedNames.insert(name) }
        }) {
            HStack(spacing: 6) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .green : .secondary)
                Text(name)
                    .lineLimit(1)
                    .font(.footnote)
            }
            .padding(8)
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(.plain)
    }

    private func selectedCount(for category: PantryCategory) -> Int {
        let set = Set(IngredientLibrary.shared.items(for: category))
        return selectedNames.filter { set.contains($0) }.count
    }

    @ViewBuilder
    private func customInputChip(_ name: String) -> some View {
        HStack(spacing: 6) {
            Text(name)
                .lineLimit(1)
                .font(.footnote)
            Button(action: {
                if let idx = viewModel.addSheetCustomInputs.firstIndex(where: { $0.caseInsensitiveCompare(name) == .orderedSame }) {
                    viewModel.addSheetCustomInputs.remove(at: idx)
                }
            }) {
                Image(systemName: "xmark.circle.fill").foregroundColor(.secondary)
            }
            .buttonStyle(.plain)
        }
        .padding(8)
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Edit Ingredient View

struct EditIngredientView: View {
    @Bindable var viewModel: PantryViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
            Form {
                Section(header: Text("Edit Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.editIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.saveEditedIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.editIngredientCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Edit Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    viewModel.cancelEditIngredient()
                    dismiss()
                },
                trailing: Button("Save") {
                    viewModel.saveEditedIngredient()
                    dismiss()
                }
                .disabled(viewModel.editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        .onAppear {
            isFocused = true
        }
    }
}

// MARK: - Category Ingredients View (Detail)

struct CategoryIngredientsView: View {
    let category: PantryCategory
    @Binding var selectedNames: Set<String>
    @State private var searchText: String = ""

    private var names: [String] {
        let base = IngredientLibrary.shared.items(for: category)
        let q = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        if q.isEmpty { return base }
        let lowerQ = q.lowercased()
        return base.filter { $0.lowercased().hasPrefix(lowerQ) }
    }

    var body: some View {
        ScrollView {
            if names.isEmpty {
                Text("No items")
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                LazyVGrid(columns: columns, spacing: 10) {
                    ForEach(names, id: \.self) { name in
                        chip(name)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 8)
            }
        }
        .searchable(text: $searchText, placement: .navigationBarDrawer(displayMode: .always), prompt: Text("Filter in \(category.rawValue)"))
        .navigationTitle(category.rawValue)
        .navigationBarTitleDisplayMode(.inline)
    }

    private func toggle(_ name: String) {
        if selectedNames.contains(name) {
            selectedNames.remove(name)
        } else {
            selectedNames.insert(name)
        }
    }

    @ViewBuilder
    private func chip(_ name: String) -> some View {
        let isSelected = selectedNames.contains(name)
        Button(action: { toggle(name) }) {
            Text(name)
                .font(.footnote.weight(.medium))
                .lineLimit(nil)
                .multilineTextAlignment(.center)
                .fixedSize(horizontal: false, vertical: true) // 允许多行，避免溢出重叠
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .foregroundColor(isSelected ? .white : .primary)
                .background(
                    RoundedRectangle(cornerRadius: 10, style: .continuous)
                        .fill(isSelected ? Color.accentColor : Color(uiColor: .systemGray6))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 10, style: .continuous)
                        .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 0)
                )
                .animation(.easeInOut(duration: 0.12), value: isSelected)
        }
        .buttonStyle(.plain)
    }

    private var columns: [GridItem] {
        // 自适应列，保证每个泡泡最小宽度，自动换行不重叠
        [GridItem(.adaptive(minimum: 120), spacing: 10)]
    }
}
