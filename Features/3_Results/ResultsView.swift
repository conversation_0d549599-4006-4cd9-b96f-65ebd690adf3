import SwiftUI

struct ResultsView: View {
    let ingredients: [Ingredient]
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    @Environment(PantryService.self) var pantryService: PantryService
    @State private var viewModel: ResultsViewModel
    @State private var loadingError: Error?
    @State private var showError = false
    @State private var isAddingToPantry = false
    
    init(ingredients: [Ingredient]) {
        self.ingredients = ingredients
        // Initialize with a placeholder, will be updated in .task
        self._viewModel = State(initialValue: ResultsViewModel(ingredients: ingredients, pantryService: ServiceContainer.shared.pantryService))
    }
    
    var body: some View {
        VStack {
            if viewModel.ingredients.isEmpty {
                EmptyIngredientsView()
            } else {
                // New grouped ingredient display using ingredientsByCategory
                List {
                    ForEach(Array(viewModel.ingredientsByCategory.keys).sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { category in
                        if let categoryIngredients = viewModel.ingredientsByCategory[category], !categoryIngredients.isEmpty {
                            Section(header:
                                HStack {
                                    Button(action: {
                                        viewModel.toggleCategorySelection(category)
                                    }) {
                                        HStack {
                                            Image(systemName: viewModel.isCategoryFullySelected(category) ? "checkmark.square.fill" : "square")
                                                .foregroundColor(viewModel.isCategoryFullySelected(category) ? .green : .gray)
                                                .font(.title3)

                                            Text(category.icon)
                                                .font(.title3)
                                            Text(category.rawValue)
                                                .font(.headline)
                                        }
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                    .accessibilityLabel("\(category.rawValue) category header")
                                    .accessibilityHint("Double tap to toggle selection for all items in this category")
                                    .accessibilityAddTraits(.isButton)

                                    Spacer()
                                    
                                    if viewModel.isLoadingAdditionalData {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    }
                                    
                                    Text("\(categoryIngredients.count)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            ) {
                                ForEach(categoryIngredients) { ingredient in
                                    NewIngredientRowView(
                                        ingredient: ingredient,
                                        isSelected: viewModel.selectedIngredientIds.contains(ingredient.id),
                                        enhancedData: viewModel.enhancedIngredients.first { $0.ingredient.id == ingredient.id },
                                        showAdditionalData: viewModel.additionalDataLoaded,
                                        onToggle: { viewModel.toggleSelection(for: ingredient) },
                                        onEdit: { viewModel.startEditing(ingredient: ingredient) }
                                    )
                                }
                            }
                        }
                    }
                }
                .listStyle(InsetGroupedListStyle())

                // New action buttons section
                VStack(spacing: 12) {
                    // Smart selection buttons
                    HStack(spacing: 12) {
                        Button("Select All") {
                            viewModel.selectAll()
                        }
                        .buttonStyle(.bordered)
                        .accessibilityLabel("Select all ingredients")
                        .accessibilityHint("Marks all ingredients as selected")

                        Button("Deselect All") {
                            viewModel.deselectAll()
                        }
                        .buttonStyle(.bordered)
                        .accessibilityLabel("Deselect all ingredients")
                        .accessibilityHint("Clears selection for all ingredients")
                    }

                    // Main action buttons as specified in task
                    HStack(spacing: 12) {
                        // Add Selected to Pantry button
                        Button(action: {
                            Task {
                                isAddingToPantry = true
                                await viewModel.addSelectedToPantry()
                                isAddingToPantry = false
                            }
                        }) {
                            HStack {
                                if isAddingToPantry {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "plus.circle.fill")
                                }
                                Text("Add Selected to Pantry (\(viewModel.selectedIngredientIds.count))")
                            }
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(viewModel.selectedIngredientIds.isEmpty ? Color.gray : Color.green)
                            .cornerRadius(10)
                        }
                        .disabled(viewModel.selectedIngredientIds.isEmpty || isAddingToPantry)
                        .accessibilityLabel("Add Selected to Pantry")
                        .accessibilityHint("Saves the selected ingredients to your pantry")
                        .accessibilityAddTraits(.isButton)
                        
                        // Restart Scanning button
                        Button(action: {
                            viewModel.restartScanning()
                        }) {
                            HStack {
                                Image(systemName: "arrow.counterclockwise")
                                Text("Restart Scanning")
                            }
                            .font(.headline)
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(10)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .accessibilityLabel("Restart Scanning")
                        .accessibilityHint("Clears current results and returns to scanning")
                        .accessibilityAddTraits(.isButton)
                    }
                    
                    // Legacy buttons for backward compatibility
                    Button("Generate Recipe") {
                        coordinator.navigateToRecipeGenerator()
                    }
                    .buttonStyle(.bordered)
                    
                    // Debug button (only in debug builds)
                    if viewModel.isDebugModeEnabled {
                        Button("View Debug Info") {
                            if let visionResponse = viewModel.visionResponse,
                               let geminiResponse = viewModel.geminiResponse {
                                coordinator.navigateToDebug(
                                    visionResponse: visionResponse,
                                    geminiResponse: geminiResponse,
                                    ingredients: ingredients
                                )
                            }
                        }
                        .buttonStyle(.bordered)
                        .padding(.bottom)
                    }
                }
                .padding()
            }
        }
        .navigationTitle("Identified Ingredients")
        .navigationBarTitleDisplayMode(.large)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Rescan") {
                    viewModel.restartScanning()
                }
            }
        }
        .alert("No Ingredients Detected", isPresented: $viewModel.showingNoIngredientsAlert) {
            Button("Rescan") {
                viewModel.restartScanning()
            }
        } message: {
            Text("We couldn't find any food items in your image. Please try again.")
        }
        .alert("Loading Error", isPresented: $showError, presenting: loadingError) { error in
            Button("Retry") {
                // Task will automatically restart
            }
            Button("OK", role: .cancel) {}
        } message: { error in
            Text(error.localizedDescription)
        }
        .sheet(isPresented: $viewModel.showingEditIngredient) {
            ResultsEditIngredientView(viewModel: viewModel)
        }
        .task(id: ingredients) { // Will restart if ingredients change
            do {
                // Update viewModel with current ingredients, pantry service, and navigation coordinator
                viewModel = ResultsViewModel(ingredients: ingredients, pantryService: pantryService, navigationCoordinator: coordinator)
                
                // Load additional ingredient data
                try await viewModel.loadAdditionalData()
            } catch {
                if !Task.isCancelled {
                    loadingError = error
                    showError = true
                }
            }
        }
    }

    // MARK: - Helper Views
}

// MARK: - New Ingredient Row View

private struct NewIngredientRowView: View {
    let ingredient: Ingredient
    let isSelected: Bool
    let enhancedData: EnhancedIngredient?
    let showAdditionalData: Bool
    let onToggle: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        HStack {
            // Checkbox selection
            Button(action: onToggle) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .green : .gray)
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())
            .accessibilityLabel(isSelected ? "Deselect \(ingredient.name)" : "Select \(ingredient.name)")
            .accessibilityHint("Double tap to toggle selection")
            .accessibilityAddTraits(.isButton)

            VStack(alignment: .leading, spacing: 4) {
                Text(ingredient.name)
                    .font(.headline)
                    .foregroundColor(.primary)

                if showAdditionalData, let enhanced = enhancedData {
                    Text(enhanced.storageTips)
                        .font(.caption)
                        .foregroundColor(.secondary)


                }
            }

            Spacer()

            Button("Edit") {
                onEdit()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
            .accessibilityLabel("Edit \(ingredient.name)")
            .accessibilityHint("Edit the name or category for this ingredient")
            .accessibilityAddTraits(.isButton)
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(ingredient.name), \(ingredient.category.rawValue)")
        .accessibilityValue(isSelected ? "Selected" : "Not selected")
        .padding(.vertical, 4)
    }
}

// MARK: - Legacy Ingredient Row View (for backward compatibility)

private struct IngredientRowView: View {
    let ingredient: Ingredient
    let enhancedData: EnhancedIngredient?
    let showAdditionalData: Bool
    let onToggle: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        HStack {
            Button(action: onToggle) {
                Image(systemName: "circle")
                    .foregroundColor(.gray)
                    .font(.title2)
            }
            .buttonStyle(PlainButtonStyle())

            VStack(alignment: .leading, spacing: 4) {
                Text(ingredient.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                if showAdditionalData, let enhanced = enhancedData {
                    Text(enhanced.storageTips)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    

                }
            }

            Spacer()

            Button("Edit") {
                onEdit()
            }
            .buttonStyle(.bordered)
            .controlSize(.small)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Empty State View

private struct EmptyIngredientsView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.orange)

            Text("No Ingredients Found")
                .font(.title2.weight(.semibold))

            Text("We couldn't identify any food items in your image. Please try scanning again with a clearer photo.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Edit Ingredient View for Results

struct ResultsEditIngredientView: View {
    @Bindable var viewModel: ResultsViewModel
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Edit Ingredient")) {
                    TextField("Ingredient name", text: $viewModel.editIngredientName)
                        .focused($isFocused)
                        .onSubmit {
                            viewModel.saveEditedIngredient()
                            dismiss()
                        }
                }

                Section(header: Text("Category")) {
                    Picker("Category", selection: $viewModel.editIngredientCategory) {
                        ForEach(PantryCategory.allCases, id: \.self) { category in
                            HStack {
                                Text(category.icon)
                                Text(category.rawValue)
                            }
                            .tag(category)
                        }
                    }
                    .pickerStyle(WheelPickerStyle())
                }
            }
            .navigationTitle("Edit Ingredient")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        viewModel.cancelEditIngredient()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        viewModel.saveEditedIngredient()
                        dismiss()
                    }
                    .disabled(viewModel.editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .onAppear {
                isFocused = true
            }
        }
    }
}