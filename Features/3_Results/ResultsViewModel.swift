import SwiftUI

@Observable
@MainActor
class ResultsViewModel {
    private let pantryService: PantryService
    private let navigationCoordinator: NavigationCoordinator

    private let telemetryService: TelemetryService = ServiceContainer.shared.telemetryService

    var ingredients: [Ingredient]
    var selectedIngredientIds: Set<UUID> = []
    var editingIngredient: Ingredient? = nil

    // Legacy properties for backward compatibility
    var showingNoIngredientsAlert = false
    var hasAddedToPantry = false
    var showingSuccessState = false
    var addedItemsCount = 0
    var showingEditIngredient = false
    var editIngredientName = ""
    var editIngredientCategory: PantryCategory = .other

    // Additional data loading properties
    var isLoadingAdditionalData = false
    var additionalDataLoaded = false
    var visionResponse: String?
    var geminiResponse: String?
    var isDebugModeEnabled: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }

    // Enhanced ingredient data
    var enhancedIngredients: [EnhancedIngredient] = []

    // Get ingredients grouped by category - updated to use dictionary format as specified in task
    var ingredientsByCategory: [PantryCategory: [Ingredient]] {
        Dictionary(grouping: ingredients) { $0.category }
    }

    // Legacy computed property for backward compatibility
    var categorizedIngredients: [(category: PantryCategory, ingredients: [Ingredient])] {
        let grouped = Dictionary(grouping: ingredients) { $0.category }

        // Sort categories in a logical order
        let categoryOrder: [PantryCategory] = [
            .produce,
            .proteins,
            .seafood,
            .dairy,
            .plantBasedAlternatives,
            .bakery,
            .grainsPastaLegumes,
            .spicesAndSeasonings,
            .oilsVinegarsAndCondiments,
            .cannedAndBroths,
            .nutsAndSeeds,
            .snacks,
            .bakingAndSweeteners,
            .other
        ]

        return categoryOrder.compactMap { category in
            guard let items = grouped[category], !items.isEmpty else { return nil }
            return (category: category, ingredients: items)
        }
    }

    init(ingredients: [Ingredient], pantryService: PantryService, navigationCoordinator: NavigationCoordinator) {
        self.ingredients = ingredients
        self.pantryService = pantryService
        self.navigationCoordinator = navigationCoordinator

        // Select all ingredients by default as specified in task
        self.selectedIngredientIds = Set(ingredients.map { $0.id })

        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }

    // Legacy init for backward compatibility
    init(ingredients: [Ingredient], pantryService: PantryService) {
        self.ingredients = ingredients
        self.pantryService = pantryService
        // Note: navigationCoordinator will be set by the view when available
        self.navigationCoordinator = NavigationCoordinator()

        // Select all ingredients by default
        self.selectedIngredientIds = Set(ingredients.map { $0.id })

        // Check if no ingredients were found
        if ingredients.isEmpty {
            showingNoIngredientsAlert = true
        }
    }

    // MARK: - New Selection Methods for Task 33

    /// Toggle selection for an ingredient
    func toggleSelection(for ingredient: Ingredient) {
        if selectedIngredientIds.contains(ingredient.id) {
            selectedIngredientIds.remove(ingredient.id)
        } else {
            selectedIngredientIds.insert(ingredient.id)
        }
    }

    /// Start editing an ingredient
    func startEditing(ingredient: Ingredient) {
        editingIngredient = ingredient
        editIngredientName = ingredient.name
        editIngredientCategory = ingredient.category
        showingEditIngredient = true
    }

    /// Update edited ingredient
    func updateIngredient(id: UUID, name: String, category: PantryCategory) {
        guard let index = ingredients.firstIndex(where: { $0.id == id }) else { return }
        ingredients[index] = Ingredient(id: id, name: name, category: category)
        editingIngredient = nil
        showingEditIngredient = false
    }

    /// Add selected ingredients to pantry
    func addSelectedToPantry() async {
        let selectedIngredients = ingredients.filter { selectedIngredientIds.contains($0.id) }
        // Track add to pantry
        telemetryService.trackAddToPantry(ingredientCount: selectedIngredients.count)
        // Phase 2: Use replace-on-add with canonicalization (atomic)
        await pantryService.addOrReplaceIngredients(selectedIngredients)
        // Switch to Pantry tab and reset the scan stack in the background without switching back
        navigationCoordinator.switchToPantryTabAndResetScan()
    }

    /// Restart scanning
    func restartScanning() {
        navigationCoordinator.resetScanFlow()
    }

    /// Load additional data for ingredients such as nutritional info, storage tips, etc.
    /// This method can be called by the view using .task(id:) for reactive data loading
    func loadAdditionalData() async throws {
        isLoadingAdditionalData = true
        additionalDataLoaded = false

        do {
            // Check for cancellation before starting
            if Task.isCancelled {
                throw TaskCancellationError()
            }

            // Simulate loading additional data (nutritional info, storage tips, etc.)
            try await Task.sleep(for: .milliseconds(500))

            // Check for cancellation after delay
            if Task.isCancelled {
                throw TaskCancellationError()
            }

            // Process ingredients to add additional data
            enhancedIngredients = ingredients.map { ingredient in
                EnhancedIngredient(
                    ingredient: ingredient,
                    storageTips: generateStorageTips(for: ingredient),
                    nutritionalHighlights: generateNutritionalHighlights(for: ingredient)
                )
            }

            additionalDataLoaded = true
            isLoadingAdditionalData = false

        } catch {
            isLoadingAdditionalData = false
            if Task.isCancelled {
                throw TaskCancellationError()
            } else {
                throw error
            }
        }
    }


    // MARK: - Smart Selection Methods

    func selectAll() {
        selectedIngredientIds = Set(ingredients.map { $0.id })
        // Legacy selection kept for compatibility but no longer mutated to avoid deprecation warnings
    }

    func deselectAll() {
        selectedIngredientIds.removeAll()
        // Legacy selection kept for compatibility but no longer mutated to avoid deprecation warnings
    }

    func toggleCategorySelection(_ category: PantryCategory) {
        let categoryIngredients = ingredients.filter { $0.category == category }
        let allSelected = categoryIngredients.allSatisfy { selectedIngredientIds.contains($0.id) }

        for ingredient in categoryIngredients {
            if allSelected {
                selectedIngredientIds.remove(ingredient.id)
            } else {
                selectedIngredientIds.insert(ingredient.id)
            }
        }

        // Legacy selection kept for compatibility but no longer mutated to avoid deprecation warnings
    }

    func isCategoryFullySelected(_ category: PantryCategory) -> Bool {
        let categoryIngredients = ingredients.filter { $0.category == category }
        return !categoryIngredients.isEmpty && categoryIngredients.allSatisfy { selectedIngredientIds.contains($0.id) }
    }

    // MARK: - Edit Individual Ingredient

    func startEditingIngredient(_ ingredient: Ingredient) {
        startEditing(ingredient: ingredient)
    }

    func saveEditedIngredient() {
        guard let ingredient = editingIngredient else { return }

        let trimmedName = editIngredientName.trimmingCharacters(in: .whitespacesAndNewlines)
        if !trimmedName.isEmpty {
            updateIngredient(id: ingredient.id, name: trimmedName, category: editIngredientCategory)
        }

        cancelEditIngredient()
    }

    func cancelEditIngredient() {
        editingIngredient = nil
        editIngredientName = ""
        editIngredientCategory = .other
        showingEditIngredient = false
    }

    // MARK: - Additional Data Generation Helpers

    private func generateStorageTips(for ingredient: Ingredient) -> String {
        switch ingredient.category {
        case .produce:
            return "Store in refrigerator crisper drawer"
        case .proteins:
            return "Keep refrigerated, use within 2-3 days"
        case .dairy, .plantBasedAlternatives:
            return "Refrigerate and check expiration date"
        case .grainsPastaLegumes:
            return "Store in cool, dry place in airtight container"
        default:
            return "Follow package instructions for storage"
        }
    }



    private func generateNutritionalHighlights(for ingredient: Ingredient) -> [String] {
        switch ingredient.category {
        case .produce:
            return ["High in vitamins", "Rich in fiber", "Low calories"]
        case .proteins:
            return ["High protein", "Essential amino acids", "Iron source"]
        case .dairy, .plantBasedAlternatives:
            return ["Calcium rich", "Protein source", "Vitamin D"]
        case .grainsPastaLegumes:
            return ["Complex carbohydrates", "B vitamins", "Energy source"]
        default:
            return ["Nutritional content varies"]
        }
    }
}

// MARK: - Enhanced Ingredient Model

struct EnhancedIngredient {
    let ingredient: Ingredient
    let storageTips: String
    let nutritionalHighlights: [String]
}

// MARK: - Custom Error Types are now in Models/Ingredient.swift
