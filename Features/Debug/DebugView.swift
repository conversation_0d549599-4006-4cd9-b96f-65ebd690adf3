import SwiftUI

#if DEBUG
struct DebugView: View {
    @State var viewModel: DebugViewModel
    
    var body: some View {
            VStack {
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Raw Google Vision API Output:")
                                .font(.headline.weight(.bold))
                            
                            Text(viewModel.visionResponse)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                        }
                        
                        Divider()
                        
                        VStack(alignment: .leading, spacing: 10) {
                            Text("Cleaned Gemini API Output:")
                                .font(.headline.weight(.bold))
                            
                            Text(viewModel.geminiResponse)
                                .font(.system(.body, design: .monospaced))
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                }
                
                Button(action: viewModel.continueToResults) {
                    Text("Continue to Results")
                        .font(.title2.weight(.semibold))
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 15)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding()
            }
            .navigationTitle("Debug Info")
            .navigationBarTitleDisplayMode(.large)
    }
}
#endif 