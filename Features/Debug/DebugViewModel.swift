import SwiftUI

#if DEBUG
@Observable
@MainActor
class DebugViewModel {
    private weak var navigationCoordinator: NavigationCoordinator?
    let visionResponse: String
    let geminiResponse: String
    let ingredients: [Ingredient]

    init(navigationCoordinator: NavigationCoordinator, visionResponse: String, geminiResponse: String, ingredients: [Ingredient]) {
        self.navigationCoordinator = navigationCoordinator
        self.visionResponse = visionResponse
        self.geminiResponse = geminiResponse
        self.ingredients = ingredients
    }

    func continueToResults() {
        // Use the properly categorized ingredients passed from Gemini processing
        navigationCoordinator?.navigateToResults(ingredients: ingredients)
    }
}
#endif 