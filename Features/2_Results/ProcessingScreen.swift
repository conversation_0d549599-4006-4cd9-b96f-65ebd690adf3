import SwiftUI
import UIKit

struct ProcessingScreen: View {
    let coordinator: NavigationCoordinator
    let images: [UIImage]

    @State private var errorMessage: String?

    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            ProgressView()
                .scaleEffect(1.5)
            Text("Processing images…")
                .font(.headline)
            if let msg = errorMessage {
                Text(msg)
                    .foregroundStyle(.red)
                Text("Tap back and try again")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            Spacer()
        }
        .navigationTitle("Processing")
        .navigationBarTitleDisplayMode(.inline)
        .task {
            await runPipeline()
        }
    }

    private func runPipeline() async {
        do {
            // Phase 1: Vision (exactly as StagingViewModel does)
            let prepResults = await ServiceContainer.shared.imagePreparationService.prepareImagesForVisionAPI(images)
            let preparedImages = prepResults.compactMap { try? $0.get() }
            // Reuse the same per-image Vision helper through a temporary VM instance
            let tempVM = StagingViewModel(navigationCoordinator: coordinator)
            let visionOutputs = try await tempVM.processPerImage(
                preparedImages: preparedImages,
                visionService: ServiceContainer.shared.googleVisionService,
                concurrency: 2
            )
            guard !visionOutputs.isEmpty else {
                throw ScanError.visionError("Could not process any images")
            }

            // Phase 2: Gemini (same call and params)
            let allowedCategories = PantryCategory.allCases.map { $0.rawValue }
            let ingredients = try await ServiceContainer.shared.geminiService.canonicalizeIngredients(
                visionOutputs: visionOutputs,
                allowedCategories: allowedCategories
            )

            // Navigate to final results
            coordinator.navigateToResults(ingredients: ingredients)
        } catch {
            errorMessage = error.localizedDescription
        }
    }
}

