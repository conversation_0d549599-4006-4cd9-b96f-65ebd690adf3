import SwiftUI
import Foundation

import UIKit
import AVFoundation
import PhotosUI

// MARK: - Processing Error Types

enum ScanError: Error, LocalizedError {
    case visionError(String)
    case geminiError(String)
    case partialVisionSuccess(successCount: Int, totalCount: Int)
    case networkError(String)
    case timeout
    case noImages
    case navigationFailed

    var errorDescription: String? {
        switch self {
        case .visionError(let message):
            return "Image analysis error: \(message)"
        case .geminiError(let message):
            return "Text processing error: \(message)"
        case .partialVisionSuccess(let successCount, let totalCount):
            return "Processed \(successCount) of \(totalCount) images successfully"
        case .networkError(let message):
            return "Network error: \(message)"
        case .timeout:
            return "Processing timed out. Please try again with fewer images."
        case .noImages:
            return "No images selected for processing."
        case .navigationFailed:
            return "Navigation to results failed."
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .visionError, .geminiError, .networkError:
            return "Please try again or use fewer images"
        case .partialVisionSuccess:
            return "Some images couldn't be processed. You can continue with partial results or try again"
        case .timeout:
            return "Try processing fewer images at once or check your internet connection."
        case .noImages:
            return "Add at least one image to process."
        case .navigationFailed:
            return "Please restart the app and try again."
        }
    }

    var canRetry: Bool {
        switch self {
        case .visionError, .geminiError, .networkError, .timeout:
            return true
        case .partialVisionSuccess:
            return true // Allow retry to try processing failed images again
        case .noImages, .navigationFailed:
            return false
        }
    }

    var allowsContinue: Bool {
        switch self {
        case .partialVisionSuccess:
            return true // Allow continuing with partial results
        default:
            return false
        }
    }
}

// Keep ProcessingError for backward compatibility
typealias ProcessingError = ScanError

@Observable
@MainActor
class StagingViewModel {
    private weak var navigationCoordinator: NavigationCoordinator?

    // MARK: - Service Dependencies

    private let visionService: GoogleVisionAPIService
    private let geminiService: GeminiAPIService


    // Telemetry
    private let telemetryService: TelemetryService = ServiceContainer.shared.telemetryService

    // MARK: - State Properties

    var selectedImages: [UIImage] = []
    var showingImagePicker = false
    var showingCamera = false
    var isProcessing = false
    var isLoadingImages = false
    var selectedImage: UIImage?
    // Camera permission prompt state
    var showCameraPermissionPrompt = false

    // MARK: - Enhanced Processing Flow Properties

    /// Processing error for the new direct flow
    var processingError: Error?

    /// Processing progress for overlay (0.0 to 1.0)
    var processingProgress: Double = 0.0

    /// Partial results from processing (for partial success scenarios)
    var partialResults: [Ingredient] = []

    /// Maximum number of images allowed (increased from 3 to 10 as per PRD)
    private let maxImages = 10

    // MARK: - Computed Properties

    var canAddMoreImages: Bool {
        selectedImages.count < maxImages
    }

    var remainingSlots: Int {
        maxImages - selectedImages.count
    }

    /// Check if Process button should be shown (≥1 image present)
    var canProcess: Bool {
        return !selectedImages.isEmpty && !isProcessing
    }

    var processButtonText: String {
        let count = selectedImages.count
        if count == 0 {
            return "Add Images to Process"
        } else if count == 1 {
            return "Process 1 Image"
        } else {
            return "Process \(count) Images"
        }
    }

    init(navigationCoordinator: NavigationCoordinator?,
         visionService: GoogleVisionAPIService = ServiceContainer.shared.googleVisionService,
         geminiService: GeminiAPIService = ServiceContainer.shared.geminiService) {
        self.navigationCoordinator = navigationCoordinator
        self.visionService = visionService
        self.geminiService = geminiService
    }

        // No-op. We rely on ServiceContainer singletons.

    // MARK: - Image Management

    func addImage(_ image: UIImage) {
        guard canAddMoreImages else { return }
        selectedImages.append(image)
    }

    func addImages(_ images: [UIImage]) {
        let availableSlots = remainingSlots
        let imagesToAdd = Array(images.prefix(availableSlots))
        selectedImages.append(contentsOf: imagesToAdd)
    }

    func removeImage(at index: Int) {
        guard index >= 0 && index < selectedImages.count else { return }
        selectedImages.remove(at: index)
    }

    /// Clear all images - useful for restarting the scan flow
    func clearImages() {
        selectedImages.removeAll()
        processingError = nil
        processingProgress = 0.0
    }

    // MARK: - Primary Button Actions

    /// Scan Receipts and Ingredients - Opens system camera for scanning
    func scanReceiptsAndIngredients() {
        guard canAddMoreImages else { return }
        // Prefer camera when available; otherwise gracefully fall back to library (e.g., Simulator)
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            // No alert/message per PRD; just open library
            showingImagePicker = true
            return
        }

        // Handle camera authorization for better UX (meets "Settings navigation" acceptance)
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            showingCamera = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                Task { @MainActor in
                    if granted {
                        self.showingCamera = true
                    } else {
                        // Offer Settings navigation or Library fallback
                        self.showCameraPermissionPrompt = true
                    }
                }
            }
        case .denied, .restricted:
            // Offer Settings navigation or Library fallback
            showCameraPermissionPrompt = true
        @unknown default:
            showCameraPermissionPrompt = true
        }
    }

    func chooseFromLibrary() {
        // PHPickerViewController doesn't require permissions - show directly
        showingImagePicker = true
    }

    // MARK: - Legacy Camera Support

    func takePhoto() {
        guard canAddMoreImages else { return }

        // Check if camera is available before showing
        guard UIImagePickerController.isSourceTypeAvailable(.camera) else {
            print("⚠️ Camera not available on this device")
            return
        }

        // UIImagePickerController handles camera permissions automatically
        showingCamera = true
    }

    // MARK: - Processing Flow

    /// Process images using the new direct Vision → Gemini pipeline with enhanced error handling
    /// This is the main processing method called by the Process button
    func processImages() async {
        // Always hand off to Processing screen which runs the automated pipeline
        navigationCoordinator?.navigateToProcessing(images: selectedImages)
    }
        /*
        // Telemetry: track scan start
        let startTime = Date()
        let imageCount = selectedImages.count
        telemetryService.trackScanStarted(imageCount: imageCount)

        // Show processing overlay (ensure on main thread)
        await MainActor.run {
            isProcessing = true
            processingError = nil
            processingProgress = 0.0
            partialResults = []
        }


            // Local tuple type for clearer generics
            typealias ProcessingResult = (ingredients: [Ingredient], successCount: Int)

        do {
            // Add timeout mechanism to prevent indefinite loading
            let timeoutTask = Task {
                try await Task.sleep(nanoseconds: 120_000_000_000) // 120 seconds timeout
                throw ScanError.timeout
            }

            let processingTask = Task<ProcessingResult, Error> {
                do {
                    // Step 1: Process images with Vision API
                    await MainActor.run { processingProgress = 0.1 }

                    // Telemetry: measure total Vision duration
                    let visionStart = Date()

                    // NEW: Async image preparation in background threads
                    let preparationStart = Date()
                    let prepResults = await ServiceContainer.shared.imagePreparationService.prepareImagesForVisionAPI(selectedImages)
                    let preparedImages = prepResults.compactMap { try? $0.get() }
                    let failedCount = prepResults.count - preparedImages.count
                    let preparationDuration = Date().timeIntervalSince(preparationStart)

                    // Track image preparation performance
                    telemetryService.trackImagePreparation(
                        imageCount: selectedImages.count,
                        duration: preparationDuration,
                        failedCount: failedCount
                    )

                    // NEW: True batch processing - single API call for all images
                    await MainActor.run { processingProgress = 0.6 }
                    // Per-image analysis with small concurrency (2)
                    let perImageStart = Date()
                    let visionOutputs = try await processPerImage(preparedImages: preparedImages, visionService: visionService, concurrency: 2)
                    let perImageDuration = Date().timeIntervalSince(perImageStart)

                    // Track processing performance (per-image)
                    telemetryService.trackBatchProcessingPerformance(
                        imageCount: selectedImages.count,
                        batchDuration: perImageDuration,
                        method: "per_image"
                    )

                    // Track total Vision performance (including preparation + API call)
                    let totalVisionDuration = Date().timeIntervalSince(visionStart)
                    telemetryService.trackVisionPerformance(imageCount: selectedImages.count, duration: totalVisionDuration)

                    // Check if we have results (no partial failure handling needed with true batching)
                    _ = selectedImages.count - visionOutputs.count

                    // Check if we have at least some results
                    if visionOutputs.isEmpty {
                        throw ScanError.visionError("Could not process any images")
                    }

                    // Continue with Gemini immediately
                    let allowedCategories = PantryCategory.allCases.map { $0.rawValue }
                    let gStart = Date()
                    let ingredients = try await geminiService.canonicalizeIngredients(
                        visionOutputs: visionOutputs,
                        allowedCategories: allowedCategories
                    )
                    let gDuration = Date().timeIntervalSince(gStart)
                    telemetryService.trackGeminiPerformance(
                        inputSize: visionOutputs.reduce(0) { $0 + $1.ocrText.count },
                        duration: gDuration
                    )
                    await MainActor.run { processingProgress = 0.9 }

                    // Return ingredients along with success count for telemetry
                    return (ingredients, visionOutputs.count)
                } catch let error as ScanError {
                    throw error
                } catch {
                    // Wrap errors with context
                    if error is GeminiError {
                        throw ScanError.geminiError(error.localizedDescription)
                    } else if let urlError = error as? URLError {
                        throw ScanError.networkError(urlError.localizedDescription)

                    } else if (error as NSError).domain == NSURLErrorDomain {
                        throw ScanError.networkError(error.localizedDescription)
                    } else {
                        throw ScanError.visionError(error.localizedDescription)
                    }
                }
            }

            // Race between processing and timeout
            let result: ProcessingResult = try await withThrowingTaskGroup(of: ProcessingResult.self, returning: ProcessingResult.self) { group in
                group.addTask { try await processingTask.value }
                group.addTask {
                    try await timeoutTask.value
                    return ([], 0) // This will never be reached due to timeout error
                }

                // Get the first completed task result
                let first = try await group.next()!
                group.cancelAll() // Cancel the remaining task
                return first
            }

            // Step 3: Navigate to Results (ensure on main thread)
            // Telemetry: overall scan completed
            let totalDuration = Date().timeIntervalSince(startTime)
            telemetryService.trackScanCompleted(
                imageCount: imageCount,
                successCount: result.successCount,
                duration: totalDuration,
                ingredientCount: result.ingredients.count
            )

            await MainActor.run {
                isProcessing = false
                processingProgress = 1.0
            }

            // Check if we have ingredients to display
            guard !result.ingredients.isEmpty else {
                await MainActor.run {
                    processingError = ScanError.geminiError("No ingredients could be identified from the images")
                }
                return
            }

            // Navigate to results
            guard let navigationCoordinator = navigationCoordinator else {
                await MainActor.run {
                    processingError = ScanError.navigationFailed
                }
                return
            }

            navigationCoordinator.navigateToResults(ingredients: result.ingredients)

        } catch let error as ScanError {
            // Telemetry: track error
            telemetryService.trackScanError(
                imageCount: selectedImages.count,
                errorType: String(describing: error)
            )
            // Handle ScanError with partial results support
            await MainActor.run {
                isProcessing = false
                processingError = error
                processingProgress = 0.0

                // For partial success, keep the partial results available
                if case .partialVisionSuccess = error {
                    // partialResults already set in the processing task
                }
            }
        } catch {
            // Telemetry: track error
            telemetryService.trackScanError(
                imageCount: selectedImages.count,
                errorType: String(describing: error)
            )
            // Handle other errors
            await MainActor.run {
                isProcessing = false
                processingError = ScanError.networkError(error.localizedDescription)
                processingProgress = 0.0

            }
        }
    }
        */




    /// Restart the scanning process - clear all state
    func restartScanning() {
        selectedImages.removeAll()
        processingError = nil
        processingProgress = 0.0
        isProcessing = false
        partialResults = []
        // Reset any other UI state if needed
        showingImagePicker = false
        showingCamera = false
        selectedImage = nil
        isLoadingImages = false
    }

    /// Retry processing with the same images after an error
    func retryProcessing() async {
        guard !selectedImages.isEmpty else { return }
        await processImages()
    }

    /// Clear error state without clearing images
    func clearError() {
        processingError = nil
        partialResults = []
    }

    /// Open the app's Settings page for permission management
    func openAppSettings() {
        guard let url = URL(string: UIApplication.openSettingsURLString) else { return }
        if UIApplication.shared.canOpenURL(url) {
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
    }

    /// Continue with partial results (for partial success scenarios)
    func continueWithPartialResults() {
        guard !partialResults.isEmpty else { return }

        // Navigate to results with partial results
        guard let navigationCoordinator = navigationCoordinator else {
            processingError = ScanError.navigationFailed
            return
        }

        navigationCoordinator.navigateToResults(ingredients: partialResults)

        // Clear the error and partial results
        processingError = nil
        partialResults = []
    }

    // MARK: - Image Handling

    func handleCameraImage(_ image: UIImage?) {
        showingCamera = false
        if let image = image {
            addImage(image)
        }
        selectedImage = nil // Clear after use
    }

    @MainActor
    func handlePickerImages(_ images: [UIImage]) {
        showingImagePicker = false
        isLoadingImages = true

        // Process images immediately on main thread
        addImages(images)
        isLoadingImages = false
        // Note: We don't clear selectedImages here as that's our main storage
        // The picker's selectedImages parameter is handled by the picker itself
    }
}

    // Static wrapper to reuse the exact per-image Vision path from StagingViewModel
    // Top-level helper to reuse the exact per-image Vision path from StagingViewModel

extension StagingViewModel {
    // MARK: - Per-image Vision helper
    func processPerImage(preparedImages: [PreparedImage], visionService: GoogleVisionAPIService, concurrency: Int) async throws -> [GoogleVisionAPIService.VisionOutput] {
        if preparedImages.isEmpty { return [] }
        let limit = max(1, concurrency)
        var outputs = Array<GoogleVisionAPIService.VisionOutput?>(repeating: nil, count: preparedImages.count)

        // Use a simple semaphore to bound concurrency
        let semaphore = AsyncSemaphore(value: limit)
        await withTaskGroup(of: (Int, Result<GoogleVisionAPIService.VisionOutput, Error>).self) { group in
            for (idx, prepared) in preparedImages.enumerated() {
                await semaphore.wait()
                group.addTask {
                    defer { Task { await semaphore.signal() } }
                    do {
                        let out = try await visionService.analyzeSingle(preparedImageBase64: prepared.base64String)
                        return (idx, .success(out))
                    } catch {
                        return (idx, .failure(error))
                    }
                }
            }

            for await (idx, result) in group {
                if case .success(let out) = result {
                    outputs[idx] = out
                }
            }
        }

        return outputs.compactMap { $0 }
    }
}

    // Lightweight async semaphore
    internal actor AsyncSemaphore {
        private var value: Int
        private var waiters: [CheckedContinuation<Void, Never>] = []
        init(value: Int) { self.value = value }
        func wait() async { if value > 0 { value -= 1; return } ; await withCheckedContinuation { cont in waiters.append(cont) }
        }
        func signal() {
            if !waiters.isEmpty {
                let cont = waiters.removeFirst()
                cont.resume()
            } else {
                value += 1
            }
        }
    }
