import SwiftUI
import AuthenticationServices

/// Minimal sign-in view with Apple, Google, and Email options
/// 
/// Features:
/// - Apple Sign-In (primary)
/// - Google Sign-In (secondary)  
/// - Email/Password (fallback)
/// - Create Account functionality
/// - Real-time form validation
/// - Enhanced loading states and error handling
/// - Full accessibility support
struct SignInView: View {
    
    // MARK: - Design Constants
    
    private enum Design {
        static let buttonHeight: CGFloat = 50
        static let cornerRadius: CGFloat = 8
        static let iconSize: CGFloat = 60
        static let progressScaling: CGFloat = 0.8
        static let overlayOpacity: Double = 0.7
        static let animationDuration: Double = 0.3
        static let horizontalPadding: CGFloat = 24
        static let verticalPadding: CGFloat = 32
        static let sectionSpacing: CGFloat = 32
        static let itemSpacing: CGFloat = 16
        static let fieldSpacing: CGFloat = 12
        static let headerSpacing: CGFloat = 8
        static let topPadding: CGFloat = 8
        static let errorPadding: CGFloat = 16
        static let errorVerticalPadding: CGFloat = 12
        static let validationSpacing: CGFloat = 4
        static let minimumPasswordLength = 6
        static let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    }
    
    // MARK: - Environment Objects
    
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - State Properties
    
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var showingEmailSignIn = false
    @State private var isCreatingAccount = false
    
    // Unified Loading State
    @State private var loadingState: LoadingState = .idle
    
    private enum LoadingState {
        case idle
        case appleSignIn
        case googleSignIn
        case emailAction
        
        var isLoading: Bool {
            self != .idle
        }
        
        var specificLoading: (apple: Bool, google: Bool, email: Bool) {
            (
                apple: self == .appleSignIn,
                google: self == .googleSignIn,
                email: self == .emailAction
            )
        }
    }
    
    // MARK: - Computed Properties for Validation
    
    private var isValidEmail: Bool {
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", Design.emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private var isValidPassword: Bool {
        password.count >= Design.minimumPasswordLength
    }
    
    private var passwordsMatch: Bool {
        !isCreatingAccount || password == confirmPassword
    }
    
    private var canSubmitEmailForm: Bool {
        !email.isEmpty && !password.isEmpty && isValidEmail && isValidPassword && passwordsMatch && !loadingState.isLoading
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: Design.sectionSpacing) {
            
            // Header
            headerSection
            
            // Sign-In Options
            signInOptionsSection
            
            // Email Sign-In Section
            if showingEmailSignIn {
                emailSignInSection
            }
            
            // Error Display
            if let error = authService.authError {
                errorSection(error)
            }
            
            Spacer()
            
            // Stay Anonymous Option
            stayAnonymousSection
        }
        .padding(.horizontal, Design.horizontalPadding)
        .padding(.vertical, Design.verticalPadding)
        .navigationTitle("Sign In")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Cancel") {
                    dismiss()
                }
                .disabled(loadingState.isLoading)
                .accessibilityLabel("Cancel sign in")
                .accessibilityHint("Dismisses the sign in screen")
            }
        }
        .task {
            clearPreviousErrors()
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: Design.itemSpacing) {
            Image(systemName: "person.circle.fill")
                .font(.system(size: Design.iconSize))
                .foregroundColor(.blue)
                .accessibilityHidden(true)
            
            VStack(spacing: Design.headerSpacing) {
                Text("Sign In to Sync")
                    .font(.title2.weight(.semibold))
                    .accessibilityAddTraits(.isHeader)
                
                Text("Save your preferences across all your devices")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .accessibilityLabel("Sign in to save your preferences across all your devices")
            }
        }
    }
    
    // MARK: - Sign-In Options Section
    
    @ViewBuilder
    private var signInOptionsSection: some View {
        VStack(spacing: Design.itemSpacing) {
            
            // Apple Sign-In Button
            appleSignInButton
            
            // Google Sign-In Button
            googleSignInButton
            
            // Email Sign-In Toggle
            emailToggleButton
            
            // Developer Login Button
            developerSignInButton
        }
    }
    
    @ViewBuilder
    private var appleSignInButton: some View {
        SignInWithAppleButton(
            .continue,
            onRequest: { request in
                print("🍎 Apple Sign-In button tapped")
                let appleIDRequest = authService.startSignInWithApple()
                request.requestedScopes = appleIDRequest.requestedScopes
                request.nonce = appleIDRequest.nonce
                print("🍎 Applied nonce and scopes to Apple Sign-In request")
            },
            onCompletion: { result in
                print("🍎 Apple Sign-In completed with result")
                handleAppleSignInResult(result)
            }
        )
        .signInWithAppleButtonStyle(.black)
        .frame(height: Design.buttonHeight)
        .cornerRadius(Design.cornerRadius)
        .disabled(loadingState.isLoading)
        .overlay(loadingOverlay(for: .appleSignIn))
        .accessibilityLabel("Sign in with Apple")
        .accessibilityHint("Use your Apple ID to sign in")
    }
    
    @ViewBuilder
    private var googleSignInButton: some View {
        Button {
            handleGoogleSignIn()
        } label: {
            HStack {
                if loadingState.specificLoading.google {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .primary))
                        .scaleEffect(Design.progressScaling)
                        .accessibilityLabel("Signing in with Google")
                } else {
                    Image(systemName: "globe")
                        .font(.title3)
                        .accessibilityHidden(true)
                }
                Text("Continue with Google")
                    .font(.body.weight(.medium))
            }
            .frame(maxWidth: .infinity)
            .frame(height: Design.buttonHeight)
            .background(loadingState.specificLoading.google ? Color(.systemGray4) : Color(.systemGray6))
            .foregroundColor(.primary)
            .cornerRadius(Design.cornerRadius)
        }
        .disabled(loadingState.isLoading)
        .accessibilityLabel("Sign in with Google")
        .accessibilityHint("Use your Google account to sign in")
    }
    
    @ViewBuilder
    private var emailToggleButton: some View {
        Button {
            withAnimation(.easeInOut(duration: Design.animationDuration)) {
                showingEmailSignIn.toggle()
                if !showingEmailSignIn {
                    resetEmailForm()
                }
            }
        } label: {
            HStack {
                Image(systemName: "envelope")
                    .font(.title3)
                    .accessibilityHidden(true)
                Text("Continue with Email")
                    .font(.body.weight(.medium))
            }
            .frame(maxWidth: .infinity)
            .frame(height: Design.buttonHeight)
            .background(Color(.systemGray6))
            .foregroundColor(.primary)
            .cornerRadius(Design.cornerRadius)
        }
        .disabled(loadingState.isLoading)
        .accessibilityLabel("Sign in with Email")
        .accessibilityHint(showingEmailSignIn ? "Hide email sign in form" : "Show email sign in form")
    }
    
    @ViewBuilder
    private var developerSignInButton: some View {
        Button {
            handleDeveloperSignIn()
        } label: {
            HStack {
                Image(systemName: "hammer.fill")
                    .font(.title3)
                    .accessibilityHidden(true)
                Text("Developer")
                    .font(.body.weight(.medium))
            }
            .frame(maxWidth: .infinity)
            .frame(height: Design.buttonHeight)
            .background(Color.orange)
            .foregroundColor(.white)
            .cornerRadius(Design.cornerRadius)
        }
        .disabled(loadingState.isLoading)
        .accessibilityLabel("Developer sign in")
        .accessibilityHint("Sign in as developer for testing purposes")
    }
    
    // MARK: - Email Sign-In Section
    
    @ViewBuilder
    private var emailSignInSection: some View {
        VStack(spacing: Design.itemSpacing) {
            
            // Toggle between Sign In and Create Account
            Picker("Action", selection: $isCreatingAccount) {
                Text("Sign In").tag(false)
                Text("Create Account").tag(true)
            }
            .pickerStyle(.segmented)
            .disabled(loadingState.isLoading)
            .accessibilityLabel("Choose sign in or create account")
            
            VStack(spacing: Design.fieldSpacing) {
                // Email Field with Validation
                emailInputField
                
                // Password Field with Validation
                passwordInputField
                
                // Confirm Password Field (only when creating account)
                if isCreatingAccount {
                    confirmPasswordInputField
                }
            }
            
            // Action Button
            emailActionButton
            
            // Forgot Password Link (only for sign in)
            if !isCreatingAccount {
                forgotPasswordButton
            }
        }
        .padding(.top, Design.topPadding)
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
    
    @ViewBuilder
    private var emailInputField: some View {
        ValidatedInputField(
            text: $email,
            placeholder: "Email",
            isValid: email.isEmpty || isValidEmail,
            errorMessage: "Please enter a valid email address",
            keyboardType: .emailAddress,
            textContentType: .emailAddress,
            isDisabled: loadingState.isLoading,
            customModifier: { field in
                AnyView(field.autocapitalization(.none))
            }
        )
    }
    
    @ViewBuilder
    private var passwordInputField: some View {
        ValidatedInputField(
            text: $password,
            placeholder: "Password",
            isValid: password.isEmpty || isValidPassword,
            errorMessage: "Password must be at least \(Design.minimumPasswordLength) characters",
            textContentType: isCreatingAccount ? .newPassword : .password,
            isDisabled: loadingState.isLoading,
            isSecure: true
        )
    }
    
    @ViewBuilder
    private var confirmPasswordInputField: some View {
        ValidatedInputField(
            text: $confirmPassword,
            placeholder: "Confirm Password",
            isValid: confirmPassword.isEmpty || passwordsMatch,
            errorMessage: "Passwords do not match",
            textContentType: .newPassword,
            isDisabled: loadingState.isLoading,
            isSecure: true
        )
    }
    
    @ViewBuilder
    private var emailActionButton: some View {
        Button {
            handleEmailAction()
        } label: {
            HStack {
                if loadingState.specificLoading.email {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(Design.progressScaling)
                        .accessibilityLabel(isCreatingAccount ? "Creating account" : "Signing in")
                } else {
                    Text(isCreatingAccount ? "Create Account" : "Sign In")
                        .font(.body.weight(.medium))
                }
            }
        }
        .buttonStyle(.borderedProminent)
        .frame(maxWidth: .infinity)
        .frame(height: Design.buttonHeight)
        .disabled(!canSubmitEmailForm)
        .accessibilityLabel(isCreatingAccount ? "Create account with email and password" : "Sign in with email and password")
    }
    
    @ViewBuilder
    private var forgotPasswordButton: some View {
        Button("Forgot Password?") {
            handleForgotPassword()
        }
        .font(.caption)
        .foregroundColor(.blue)
        .disabled(email.isEmpty || !isValidEmail || loadingState.isLoading)
        .accessibilityLabel("Forgot password")
        .accessibilityHint("Send password reset email to \(email.isEmpty ? "your email address" : email)")
    }
    
    // MARK: - Error Section
    
    @ViewBuilder
    private func errorSection(_ error: String) -> some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
                .accessibilityHidden(true)
            Text(error)
                .font(.caption)
                .foregroundColor(.red)
            Spacer()
            
            Button("✕") {
                clearError()
            }
            .foregroundColor(.red)
            .font(.caption.weight(.medium))
            .accessibilityLabel("Dismiss error message")
        }
        .padding(.horizontal, Design.errorPadding)
        .padding(.vertical, Design.errorVerticalPadding)
        .background(Color.red.opacity(0.1))
        .cornerRadius(Design.cornerRadius)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("Error: \(error)")
        .accessibilityAddTraits(.isStaticText)
    }
    
    // MARK: - Stay Anonymous Section
    
    @ViewBuilder
    private var stayAnonymousSection: some View {
        VStack(spacing: Design.headerSpacing) {
            Text("Don't want to sign in?")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Button("Continue without account") {
                dismiss()
            }
            .font(.caption)
            .foregroundColor(.blue)
            .disabled(loadingState.isLoading)
            .accessibilityLabel("Continue without signing in")
            .accessibilityHint("Use the app without creating an account")
        }
    }
    
    // MARK: - Helper Views
    
    @ViewBuilder
    private func loadingOverlay(for state: LoadingState) -> some View {
        Group {
            if loadingState == state {
                RoundedRectangle(cornerRadius: Design.cornerRadius)
                    .fill(Color.black.opacity(Design.overlayOpacity))
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(Design.progressScaling)
                    )
                    .accessibilityLabel("Loading")
            }
        }
    }
    
    // MARK: - Action Handlers
    
    private func handleAppleSignInResult(_ result: Result<ASAuthorization, Error>) {
        Task {
            loadingState = .appleSignIn
            defer { loadingState = .idle }
            
            switch result {
            case .success(let authorization):
                print("🍎 Apple Sign-In authorization received")
                do {
                    _ = try await authService.handleSignInWithApple(authorization: authorization)
                    print("🍎 Apple Sign-In completed successfully")
                    if authService.isAuthenticated {
                        dismiss()
                    }
                } catch {
                    print("❌ Apple Sign-In completion failed: \(error)")
                    authService.handleAuthError(error)
                    debugLog("Apple Sign-In completion failed: \(error)")
                }
            case .failure(let error):
                print("❌ Apple Sign-In failed with error: \(error)")
                
                // Handle specific Apple Sign-In errors
                if let appleError = error as? ASAuthorizationError {
                    switch appleError.code {
                    case .canceled:
                        print("🍎 User canceled Apple Sign-In")
                        // Don't show error for user cancellation
                        return
                    case .failed:
                        print("❌ Apple Sign-In failed")
                    case .invalidResponse:
                        print("❌ Apple Sign-In invalid response")
                    case .notHandled:
                        print("❌ Apple Sign-In not handled")
                    case .unknown:
                        print("❌ Apple Sign-In unknown error")
                    case .notInteractive:
                        print("❌ Apple Sign-In not interactive")
                    case .matchedExcludedCredential:
                        print("❌ Apple Sign-In matched excluded credential")
                    case .credentialImport:
                        print("❌ Apple Sign-In credential import error")
                    case .credentialExport:
                        print("❌ Apple Sign-In credential export error")
                    @unknown default:
                        print("❌ Apple Sign-In unknown error code: \(appleError.code.rawValue)")
                    }
                }
                
                authService.handleAuthError(error)
                debugLog("Apple Sign-In failed with error: \(error)")
            }
        }
    }
    
    private func handleGoogleSignIn() {
        Task {
            loadingState = .googleSignIn
            defer { loadingState = .idle }
            
            do {
                _ = try await authService.signInWithGoogle()
                if authService.isAuthenticated {
                    dismiss()
                }
            } catch {
                debugLog("Google Sign-In failed: \(error)")
            }
        }
    }
    
    private func handleEmailAction() {
        Task {
            loadingState = .emailAction
            defer { loadingState = .idle }
            
            do {
                if isCreatingAccount {
                    _ = try await authService.createAccount(email: email, password: password)
                } else {
                    _ = try await authService.signInWithEmail(email, password: password)
                }
                
                if authService.isAuthenticated {
                    dismiss()
                }
            } catch {
                debugLog("Email action failed: \(error)")
            }
        }
    }
    
    private func handleDeveloperSignIn() {
        // 模拟开发者登录 - 直接设置为已认证状态
        Task {
            loadingState = .emailAction
            defer { loadingState = .idle }
            
            // 模拟短暂延迟
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            
            // 设置开发者用户信息
            authService.mockDeveloperLogin()
            
            if authService.isAuthenticated {
                dismiss()
            }
        }
    }
    
    private func handleForgotPassword() {
        Task {
            do {
                try await authService.resetPassword(email: email)
                showSuccessMessage("Password reset email sent. Please check your inbox.")
            } catch {
                debugLog("Password reset failed: \(error)")
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func resetEmailForm() {
        email = ""
        password = ""
        confirmPassword = ""
        isCreatingAccount = false
        clearError()
    }
    
    private func clearPreviousErrors() {
        clearError()
    }
    
    private func clearError() {
        // Clear error message through proper encapsulation
        authService.authError = nil
    }
    
    private func showSuccessMessage(_ message: String) {
        // Show success message through authError (temporary until proper success handling is added)
        authService.authError = message
    }
    
    private func debugLog(_ message: String) {
        #if DEBUG
        print("[SignInView] \(message)")
        #endif
    }
}

// MARK: - Validated Input Field Component

struct ValidatedInputField: View {
    @Binding var text: String
    let placeholder: String
    let isValid: Bool
    let errorMessage: String
    var keyboardType: UIKeyboardType = .default
    var textContentType: UITextContentType? = nil
    let isDisabled: Bool
    let isSecure: Bool
    let customModifier: ((AnyView) -> AnyView)?
    
    init(
        text: Binding<String>,
        placeholder: String,
        isValid: Bool,
        errorMessage: String,
        keyboardType: UIKeyboardType = .default,
        textContentType: UITextContentType? = nil,
        isDisabled: Bool = false,
        isSecure: Bool = false,
        customModifier: ((AnyView) -> AnyView)? = nil
    ) {
        self._text = text
        self.placeholder = placeholder
        self.isValid = isValid
        self.errorMessage = errorMessage
        self.keyboardType = keyboardType
        self.textContentType = textContentType
        self.isDisabled = isDisabled
        self.isSecure = isSecure
        self.customModifier = customModifier
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .textFieldStyle(.roundedBorder)
            .keyboardType(keyboardType)
            .textContentType(textContentType)
            .disabled(isDisabled)
            .accessibilityLabel(placeholder)
            .accessibilityValue(text.isEmpty ? "Empty" : "Filled")
            .let { field in
                if let modifier = customModifier {
                    modifier(AnyView(field))
                } else {
                    AnyView(field)
                }
            }
            
            if !text.isEmpty && !isValid {
                Label(errorMessage, systemImage: "exclamationmark.triangle.fill")
                    .font(.caption)
                    .foregroundColor(.red)
                    .accessibilityLabel("Error: \(errorMessage)")
            }
        }
    }
}

// MARK: - View Extension for Conditional Modifiers

extension View {
    func `let`<T>(_ transform: (Self) -> T) -> T {
        transform(self)
    }
}

// MARK: - Preview

struct SignInView_Previews: PreviewProvider {
    static var previews: some View {
        SignInView()
            .environment(AuthenticationService())
    }
}
