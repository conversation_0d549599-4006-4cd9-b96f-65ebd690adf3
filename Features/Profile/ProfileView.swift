import SwiftUI
import PhotosUI

/// Profile view that directly shows sign-in when not authenticated
/// and preferences management when authenticated
struct ProfileView: View {
    
    @Environment(AuthenticationService.self) var authService: AuthenticationService
    @Environment(NavigationCoordinator.self) var coordinator: NavigationCoordinator
    
    // State for sign-in sheet
    @State(initialValue: false) private var showingSignInSheet: Bool
    
    var body: some View {
        Group {
            if authService.isAuthenticated {
                authenticatedView
            } else {
                // Show sign-in prompt
                signInPromptView
            }
        }
        .sheet(isPresented: $showingSignInSheet) { SignInView() }
        .task(id: authService.isAuthenticated) {
            // Auto-show sign-in sheet when user taps Profile tab while not authenticated
            if !authService.isAuthenticated && !showingSignInSheet {
                showingSignInSheet = true
            }
        }
    }
    
    // MARK: - Sign-In Prompt View
    
    @ViewBuilder
    private var signInPromptView: some View {
        VStack(spacing: 32) {
            Spacer()
            
            VStack(spacing: 16) {
                Image(systemName: "person.circle.fill")
                    .font(.system(size: 80))
                    .foregroundStyle(.blue.gradient)
                
                VStack(spacing: 8) {
                    Text("Sign In Required")
                        .font(.title2.weight(.semibold))
                        .foregroundStyle(.primary)
                    
                    Text("Sign in to access your preferences and sync across devices")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 32)
                }
            }
            
            Button("Sign In") {
                showingSignInSheet = true
            }
            .buttonStyle(.borderedProminent)
            .font(.headline)
            .padding(.horizontal, 40)
            
            Spacer()
        }
        .padding()
        .navigationTitle("Profile")
        .navigationBarTitleDisplayMode(.large)
    }
}

// MARK: - Authenticated Content

private extension ProfileView {
    @ViewBuilder
    var authenticatedView: some View {
        PreferencesEditView()
    }
}

// MARK: - Preview

#if DEBUG
#Preview("Not Authenticated") {
    NavigationStack {
        ProfileView()
            .environment(AuthenticationService())
            .environment(NavigationCoordinator())
    }
}

#Preview("Authenticated") {
    let authService = AuthenticationService()
    // Mock authenticated state for preview
    return NavigationStack {
        ProfileView()
            .environment(authService)
            .environment(NavigationCoordinator())
    }
}
#endif
