import SwiftUI

/// Equipment selection view for user's kitchen equipment and cookware
struct EquipmentView: View {
    let initialEquipment: [String]
    let onSave: ([String]) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var selectedEquipment: Set<String> = []
    
    // Common kitchen equipment options
    private let equipmentOptions = [
        // Cooking Appliances
        EquipmentCategory(
            name: "Cooking Appliances",
            items: [
                EquipmentItem(id: "oven", name: "Oven", icon: "🔥"),
                EquipmentItem(id: "stovetop", name: "Stovetop", icon: "🍳"),
                EquipmentItem(id: "microwave", name: "Microwave", icon: "📱"),
                EquipmentItem(id: "air_fryer", name: "Air Fryer", icon: "💨"),
                EquipmentItem(id: "slow_cooker", name: "Slow Cooker", icon: "🍲"),
                EquipmentItem(id: "instant_pot", name: "Instant Pot", icon: "⚡"),
                EquipmentItem(id: "rice_cooker", name: "Rice Cooker", icon: "🍚"),
                EquipmentItem(id: "toaster_oven", name: "Toaster Oven", icon: "🍞"),
                EquipmentItem(id: "grill", name: "Grill", icon: "🔥")
            ]
        ),
        
        // Small Appliances
        EquipmentCategory(
            name: "Small Appliances",
            items: [
                EquipmentItem(id: "blender", name: "Blender", icon: "🥤"),
                EquipmentItem(id: "food_processor", name: "Food Processor", icon: "⚙️"),
                EquipmentItem(id: "stand_mixer", name: "Stand Mixer", icon: "🥧"),
                EquipmentItem(id: "hand_mixer", name: "Hand Mixer", icon: "🥄"),
                EquipmentItem(id: "immersion_blender", name: "Immersion Blender", icon: "🔧"),
                EquipmentItem(id: "coffee_maker", name: "Coffee Maker", icon: "☕"),
                EquipmentItem(id: "juicer", name: "Juicer", icon: "🍊")
            ]
        ),
        
        // Cookware
        EquipmentCategory(
            name: "Cookware",
            items: [
                EquipmentItem(id: "cast_iron_skillet", name: "Cast Iron Skillet", icon: "🍳"),
                EquipmentItem(id: "non_stick_pan", name: "Non-Stick Pan", icon: "🍳"),
                EquipmentItem(id: "wok", name: "Wok", icon: "🥘"),
                EquipmentItem(id: "dutch_oven", name: "Dutch Oven", icon: "🍲"),
                EquipmentItem(id: "sauce_pan", name: "Sauce Pan", icon: "🥄"),
                EquipmentItem(id: "stock_pot", name: "Stock Pot", icon: "🍲"),
                EquipmentItem(id: "sheet_pan", name: "Sheet Pan", icon: "📄"),
                EquipmentItem(id: "baking_dish", name: "Baking Dish", icon: "🥧")
            ]
        )
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    ForEach(equipmentOptions, id: \.name) { category in
                        VStack(alignment: .leading, spacing: 12) {
                            Text(category.name)
                                .font(.headline)
                                .foregroundColor(.primary)
                                .padding(.horizontal, 20)
                            
                            LazyVGrid(columns: [
                                GridItem(.flexible()),
                                GridItem(.flexible())
                            ], spacing: 12) {
                                ForEach(category.items, id: \.id) { item in
                                    equipmentCard(item)
                                }
                            }
                            .padding(.horizontal, 20)
                        }
                    }
                }
                .padding(.vertical, 20)
            }
            .navigationTitle("Kitchen Equipment")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        onSave(Array(selectedEquipment))
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            selectedEquipment = Set(initialEquipment)
        }
    }
    
    @ViewBuilder
    private func equipmentCard(_ item: EquipmentItem) -> some View {
        let isSelected = selectedEquipment.contains(item.id)
        
        Button(action: {
            if isSelected {
                selectedEquipment.remove(item.id)
            } else {
                selectedEquipment.insert(item.id)
            }
        }) {
            VStack(spacing: 8) {
                Text(item.icon)
                    .font(.title2)
                
                Text(item.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 80)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.accentColor.opacity(0.1) : Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Supporting Types

private struct EquipmentCategory {
    let name: String
    let items: [EquipmentItem]
}

private struct EquipmentItem {
    let id: String
    let name: String
    let icon: String
}

// MARK: - Preview

#Preview {
    EquipmentView(
        initialEquipment: ["oven", "stovetop", "microwave"],
        onSave: { equipment in
            print("Selected equipment: \(equipment)")
        }
    )
}
