import SwiftUI

/// HIG-aligned equipment selector using chip toggles
/// iOS 17+, SwiftUI, NavigationStack, @Observable-compatible
struct EquipmentChipsView: View {
    let initialSelection: [String]
    let onSave: ([String]) -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var selected: Set<String> = []

    // MARK: - Data
    private let appliances: [(id: String, label: String)] = [
        ("oven", "Oven"),
        ("stovetop", "Stovetop"),
        ("microwave", "Microwave"),
        ("air_fryer", "Air Fryer"),
        ("pressure_cooker", "Pressure Cooker"),
        ("slow_cooker", "Slow Cooker"),
        ("rice_cooker", "Rice Cooker"),
        ("toaster_oven", "Toaster Oven"),
        ("induction_cooktop", "Induction Cooktop"),
        ("grill", "Grill"),
        ("sous_vide_circulator", "Sous Vide"),
        ("blender", "Blender"),
        ("immersion_blender", "Immersion Blender"),
        ("food_processor", "Food Processor"),
        ("stand_mixer", "Stand Mixer"),
        ("electric_griddle", "Electric Griddle"),
        ("sandwich_press", "Sandwich Press"),
        ("bread_maker", "Bread Maker")
    ]

    private let cookware: [(id: String, label: String)] = [
        ("cast_iron_skillet", "Cast Iron Skillet"),
        ("stainless_skillet", "Stainless Skillet"),
        ("saute_pan", "Sauté Pan"),
        ("saucepan", "Saucepan"),
        ("stock_pot", "Stock Pot"),
        ("dutch_oven", "Dutch Oven"),
        ("wok_carbon_steel", "Wok (Carbon Steel)"),
        ("sheet_pan_half", "Half Sheet Pan"),
        ("baking_dish", "Baking Dish"),
        ("roasting_pan_rack", "Roasting Pan + Rack"),
        ("steamer_basket", "Steamer Basket"),
        ("braiser", "Braiser"),
        ("grill_pan", "Grill Pan"),
        ("stovetop_griddle", "Stovetop Griddle"),
        ("saucier", "Saucier"),
        ("tagine", "Tagine"),
        ("pizza_steel", "Pizza Steel")
    ]

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    section(title: "Appliances", items: appliances)
                    section(title: "Durable Cookware", items: cookware)
                }
                .padding(.vertical, 16)
                .padding(.horizontal, 20)
            }
            .navigationTitle("Kitchen Equipment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("Cancel") { dismiss() }
                }
                ToolbarItem(placement: .topBarTrailing) {
                    Button("Save") {
                        onSave(Array(selected).sorted())
                        dismiss()
                    }
                    .bold()
                }
            }
            .onAppear { selected = Set(initialSelection) }
        }
    }

    // MARK: - Section builder
    @ViewBuilder
    private func section(title: String, items: [(id: String, label: String)]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title).font(.headline)
            FlowLayout(alignment: .leading, spacing: 8) {
                ForEach(items, id: \.id) { item in
                    chip(id: item.id, label: item.label)
                }
            }
        }
    }

    // MARK: - Chip
    @ViewBuilder
    private func chip(id: String, label: String) -> some View {
        let isOn = selected.contains(id)
        Button(action: {
            if isOn { selected.remove(id) } else { selected.insert(id) }
            UIImpactFeedbackGenerator(style: .light).impactOccurred()
        }) {
            Text(label)
                .font(.subheadline.weight(.medium))
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .foregroundStyle(isOn ? Color.white : Color.primary)
                .background(
                    Capsule(style: .circular)
                        .fill(isOn ? Color.accentColor : Color.secondary.opacity(0.12))
                )
        }
        .buttonStyle(.plain)
        .accessibilityLabel(Text(label))
        .accessibilityValue(Text(isOn ? "Selected" : "Not selected"))
    }
}

#Preview {
    EquipmentChipsView(initialSelection: ["oven", "cast_iron_skillet"]) { selection in
        print(selection)
    }
}

