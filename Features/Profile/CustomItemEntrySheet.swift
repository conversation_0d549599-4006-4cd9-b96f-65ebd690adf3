import SwiftUI

struct CustomItemEntrySheet: View {
    @Environment(\.dismiss) private var dismiss

    let existingKeys: Set<String>
    let onSave: (String) -> Void

    @State private var text: String = ""
    @FocusState private var isFieldFocused: Bool

    private var sanitizedInput: String {
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return "" }
        let components = trimmed.split { $0.isWhitespace }
        return components.joined(separator: " ")
    }

    private var validationError: String? {
        let sanitized = sanitizedInput

        if sanitized.isEmpty {
            return text.isEmpty ? nil : "Enter at least 2 characters."
        }

        if sanitized.count < 2 {
            return "Enter at least 2 characters."
        }

        if sanitized.count > 40 {
            return "Keep it under 40 characters."
        }

        let allowed = CharacterSet.letters
            .union(.decimalDigits)
            .union(CharacterSet(charactersIn: " -'"))

        let invalidScalar = sanitized.unicodeScalars.first { !allowed.contains($0) }
        if invalidScalar != nil {
            return "Use letters, numbers, spaces, hyphen, or apostrophe."
        }

        let normalized = DoNotEatHelper.normalizeForComparison(sanitized)
        if existingKeys.contains(normalized) {
            return "That item is already in your exclusions."
        }

        return nil
    }

    private var canSave: Bool {
        validationError == nil && !sanitizedInput.isEmpty
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Custom Item")) {
                    TextField("e.g. Kale", text: $text)
                        .textInputAutocapitalization(.words)
                        .autocorrectionDisabled()
                        .focused($isFieldFocused)
                        .onSubmit(saveIfPossible)

                    if let error = validationError, !error.isEmpty {
                        Text(error)
                            .font(.footnote)
                            .foregroundStyle(.red)
                    }
                }
            }
            .navigationTitle("Add Custom Item")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("Cancel", role: .cancel) {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .topBarTrailing) {
                    Button("Save", action: saveIfPossible)
                        .disabled(!canSave)
                }
            }
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
                    isFieldFocused = true
                }
            }
        }
    }

    private func saveIfPossible() {
        guard canSave else { return }
        onSave(sanitizedInput)
        dismiss()
    }
}

struct CustomItemEntrySheet_Previews: PreviewProvider {
    static var previews: some View {
        CustomItemEntrySheet(existingKeys: ["garlic", "kale"]) { _ in }
    }
}
