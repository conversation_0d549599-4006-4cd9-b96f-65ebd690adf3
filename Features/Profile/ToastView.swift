import SwiftUI

struct ToastView: View {
    var message: String
    var type: ToastType = .info
    
    enum ToastType {
        case success, error, info
        
        var color: Color {
            switch self {
            case .success: return .green
            case .error: return .red
            case .info: return .blue
            }
        }
        
        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .error: return "exclamationmark.circle.fill"
            case .info: return "info.circle.fill"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: type.icon)
                .foregroundColor(.white)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.white)
            
            Spacer()
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(type.color)
        .cornerRadius(8)
        .shadow(radius: 4)
        .padding(.horizontal, 16)
    }
}

// MARK: - Toast Modifier

struct ToastModifier: ViewModifier {
    @Binding var isPresented: Bool
    let message: String
    let type: ToastView.ToastType
    let duration: TimeInterval
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if isPresented {
                VStack {
                    ToastView(
                        message: message,
                        type: type
                    )
                    .task {
                        try? await Task.sleep(for: .seconds(duration))
                        if !Task.isCancelled {
                            withAnimation {
                                isPresented = false
                            }
                        }
                    }
                    Spacer()
                }
                .zIndex(1000)
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
    }
}

// MARK: - View Extension

extension View {
    func toast(
        message: String,
        type: ToastView.ToastType = .info,
        duration: TimeInterval = 3.0,
        isPresented: Binding<Bool>
    ) -> some View {
        modifier(ToastModifier(
            isPresented: isPresented,
            message: message,
            type: type,
            duration: duration
        ))
    }
}

// MARK: - Previews

#Preview("Success Toast") {
    @Previewable @State var showToast = true
    
    ZStack {
        Color.gray.opacity(0.1)
        
        VStack {
            ToastView(
                message: "Successfully signed in!",
                type: .success
            )
            Spacer()
        }
    }
}

#Preview("Error Toast") {
    @Previewable @State var showToast = true
    
    ZStack {
        Color.gray.opacity(0.1)
        
        VStack {
            ToastView(
                message: "Invalid email or password",
                type: .error
            )
            Spacer()
        }
    }
}

#Preview("Info Toast") {
    @Previewable @State var showToast = true
    
    ZStack {
        Color.gray.opacity(0.1)
        
        VStack {
            ToastView(
                message: "Processing your request...",
                type: .info
            )
            Spacer()
        }
    }
} 