import SwiftUI

/// Reusable loading view component for various loading states
struct LoadingView: View {
    let message: String
    let showSpinner: Bool
    
    init(message: String = "Loading...", showSpinner: Bool = true) {
        self.message = message
        self.showSpinner = showSpinner
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if showSpinner {
                ProgressView()
                    .scaleEffect(1.2)
                    .progressViewStyle(CircularProgressViewStyle(tint: .blue))
            }
            
            Text(message)
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(24)
        .background(Color(.systemBackground))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
}

/// Comprehensive sync status view for Firestore operations
struct SyncStatusView: View {
    
    // MARK: - Sync Status Enum
    
    enum SyncStatus: Equatable {
        case idle
        case syncing
        case success
        case failure(Error)
        case offline
        
        static func == (lhs: SyncStatus, rhs: SyncStatus) -> <PERSON><PERSON> {
            switch (lhs, rhs) {
            case (.idle, .idle), (.syncing, .syncing), (.success, .success), (.offline, .offline):
                return true
            case (.failure(let lhsError), .failure(let rhsError)):
                return lhsError.localizedDescription == rhsError.localizedDescription
            default:
                return false
            }
        }
        
        var message: String {
            switch self {
            case .idle:
                return "Ready"
            case .syncing:
                return "Syncing preferences..."
            case .success:
                return "Preferences synced"
            case .failure(let error):
                return "Sync failed: \(error.localizedDescription)"
            case .offline:
                return "Offline - Changes will sync when connected"
            }
        }
        
        var icon: String {
            switch self {
            case .idle:
                return "checkmark.circle"
            case .syncing:
                return "arrow.triangle.2.circlepath"
            case .success:
                return "checkmark.circle.fill"
            case .failure:
                return "exclamationmark.triangle.fill"
            case .offline:
                return "wifi.slash"
            }
        }
        
        var color: Color {
            switch self {
            case .idle:
                return .secondary
            case .syncing:
                return .blue
            case .success:
                return .green
            case .failure:
                return .red
            case .offline:
                return .orange
            }
        }
    }
    
    // MARK: - Properties
    
    let status: SyncStatus
    let onRetry: (() -> Void)?
    
    @State private var isVisible = true
    
    init(status: SyncStatus, onRetry: (() -> Void)? = nil) {
        self.status = status
        self.onRetry = onRetry
    }
    
    // MARK: - Body
    
    var body: some View {
        if isVisible {
            HStack(spacing: 12) {
                // Status Icon
                Group {
                    if case .syncing = status {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: status.icon)
                            .font(.system(size: 16, weight: .medium))
                    }
                }
                .foregroundStyle(status.color)
                
                // Status Message
                Text(status.message)
                    .font(.subheadline)
                    .foregroundStyle(status.color)
                
                Spacer()
                
                // Retry Button (for failures)
                if case .failure = status, let onRetry = onRetry {
                    Button("Retry") {
                        onRetry()
                    }
                    .font(.caption.weight(.medium))
                    .foregroundStyle(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(status.color, in: Capsule())
                }
                
                // Dismiss Button (for success)
                if case .success = status {
                    Button {
                        withAnimation(.easeOut(duration: 0.3)) {
                            isVisible = false
                        }
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundStyle(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(status.color.opacity(0.1), in: RoundedRectangle(cornerRadius: 10))
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(status.color.opacity(0.3), lineWidth: 1)
            )
            .transition(.asymmetric(
                insertion: .scale(scale: 0.8).combined(with: .opacity),
                removal: .scale(scale: 0.8).combined(with: .opacity)
            ))
            .onAppear {
                // Auto-dismiss success status after 3 seconds
                if case .success = status {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        withAnimation(.easeOut(duration: 0.3)) {
                            isVisible = false
                        }
                    }
                }
            }
        }
    }
}

/// Network status banner for offline mode indication
struct NetworkStatusBanner: View {
    
    // MARK: - Network Status Enum
    
    enum NetworkStatus {
        case online
        case offline
        case reconnecting
        
        var message: String {
            switch self {
            case .online:
                return "Connected"
            case .offline:
                return "No internet connection - Working offline"
            case .reconnecting:
                return "Reconnecting..."
            }
        }
        
        var icon: String {
            switch self {
            case .online:
                return "wifi"
            case .offline:
                return "wifi.slash"
            case .reconnecting:
                return "arrow.triangle.2.circlepath"
            }
        }
        
        var color: Color {
            switch self {
            case .online:
                return .green
            case .offline:
                return .orange
            case .reconnecting:
                return .blue
            }
        }
    }
    
    // MARK: - Properties
    
    let status: NetworkStatus
    
    // MARK: - Body
    
    var body: some View {
        if status != .online {
            HStack(spacing: 10) {
                Group {
                    if case .reconnecting = status {
                        ProgressView()
                            .scaleEffect(0.7)
                    } else {
                        Image(systemName: status.icon)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .foregroundStyle(.white)
                
                Text(status.message)
                    .font(.caption.weight(.medium))
                    .foregroundStyle(.white)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(status.color, in: Rectangle())
            .transition(.move(edge: .top).combined(with: .opacity))
        }
    }
}

// MARK: - Preview

struct LoadingView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            LoadingView()
            
            LoadingView(message: "Saving preferences...", showSpinner: true)
            
            SyncStatusView(status: .syncing)
            
            SyncStatusView(status: .success)
            
            SyncStatusView(status: .failure(NSError(domain: "Test", code: 1, userInfo: [NSLocalizedDescriptionKey: "Network error"])), onRetry: {})
            
            SyncStatusView(status: .offline)
            
            NetworkStatusBanner(status: .offline)
            
            NetworkStatusBanner(status: .reconnecting)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
} 