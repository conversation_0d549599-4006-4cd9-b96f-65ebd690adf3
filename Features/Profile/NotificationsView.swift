import SwiftUI

struct NotificationsView: View {
    @AppStorage("foodExpirationReminderEnabled") private var foodExpirationReminderEnabled: Bool = false
    
    var body: some View {
        Form {
            Toggle(isOn: $foodExpirationReminderEnabled) {
                Text("Food Expiration Reminders")
            }
        }
        .navigationTitle("Notifications")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    NavigationStack {
        NotificationsView()
    }
}

