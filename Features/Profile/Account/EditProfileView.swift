import SwiftUI
import FirebaseAuth

struct EditProfileView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss

    @StateObject private var networkService = OptimizedNetworkService.shared

    @State private var displayName = ""
    @State private var isProcessing = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false

    private let telemetry = ServiceContainer.shared.telemetryService

    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("Display Name", text: $displayName)
                        .textContentType(.name)
                } header: {
                    Text("Profile Information")
                } footer: {
                    Text("This name appears across your synced devices.")
                }

                if let message = errorMessage {
                    Section {
                        Text(message)
                            .font(.caption)
                            .foregroundStyle(.red)
                    }
                }
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    But<PERSON>("Cancel") { dismiss() }
                }

                ToolbarItem(placement: .confirmationAction) {
                    But<PERSON>("Save") {
                        Task { await updateProfile() }
                    }
                    .disabled(displayName.isEmpty || isProcessing || !networkService.isOnline)
                }
            }
        }
        .onAppear {
            displayName = authService.currentUser?.displayName ?? ""
        }
        .alert("Profile Updated", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your display name has been updated.")
        }
    }
}

private extension EditProfileView {
    func updateProfile() async {
        guard !isProcessing else { return }
        isProcessing = true
        errorMessage = nil
        let start = Date()

        do {
            try await authService.updateUserProfile(displayName: displayName, photoURL: nil)
            trackAccountEvent(.profileUpdated, startedAt: start, error: nil)
            showingSuccess = true
        } catch {
            trackAccountEvent(.profileUpdated, startedAt: start, error: error)
            errorMessage = error.localizedDescription
        }

        isProcessing = false
    }

    func trackAccountEvent(_ event: TelemetryService.AccountEvent, startedAt: Date, error: Error?) {
        AccountTelemetryHelper.track(
            event,
            authService: authService,
            telemetry: telemetry,
            startedAt: startedAt,
            error: error
        )
    }
}
