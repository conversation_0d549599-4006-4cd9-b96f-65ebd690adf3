import FirebaseAuth

enum AccountTelemetryHelper {
    @MainActor static func track(
        _ event: TelemetryService.AccountEvent,
        authService: AuthenticationService,
        telemetry: TelemetryService,
        startedAt: Date,
        error: Error?,
        overrideContext: (required: Bool, attempts: Int)? = nil
    ) {
        let duration = Date().timeIntervalSince(startedAt)
        let provider = providerIdentifier(for: authService.currentUser)
        let result = (error == nil) ? "success" : "failure"
        let errorCode = (error as? AuthenticationService.AuthError)?.telemetryCode ?? (error as NSError?)?.telemetryCode
        let languageCode = Auth.auth().languageCode ?? Locale.current.identifier
        let reauthContext: (required: Bool, attempts: Int)

        if let overrideContext {
            reauthContext = overrideContext
        } else {
            reauthContext = authService.consumeReauthContext()
        }

        telemetry.trackAccountEvent(
            event,
            payload: .init(
                provider: provider,
                result: result,
                errorCode: errorCode,
                requiresRecentLogin: reauthContext.required,
                reauthAttempts: reauthContext.attempts,
                languageCode: languageCode,
                durationMs: max(0, Int(duration * 1000))
            )
        )
    }

    static func providerIdentifier(for user: User?) -> String {
        guard let user else { return "unknown" }
        if user.isAnonymous { return "anonymous" }
        let providerID = user.providerData.first?.providerID ?? "password"
        switch providerID {
        case "apple.com": return "apple"
        case "google.com": return "google"
        case "password": return "password"
        default: return providerID
        }
    }
}

extension AuthenticationService.AuthError {
    var telemetryCode: String {
        switch self {
        case .invalidEmail: return "invalid_email"
        case .weakPassword: return "weak_password"
        case .emailAlreadyInUse: return "email_in_use"
        case .userNotFound: return "user_not_found"
        case .wrongPassword: return "wrong_password"
        case .networkError: return "network_error"
        case .credentialAlreadyInUse: return "credential_in_use"
        case .invalidCredential: return "invalid_credential"
        case .operationNotAllowed: return "operation_not_allowed"
        case .tooManyRequests: return "too_many_requests"
        case .requiresRecentLogin: return "requires_recent_login"
        case .userDisabled: return "user_disabled"
        case .userTokenExpired: return "token_expired"
        case .userCancelled: return "user_cancelled"
        case .appleSignInFailed: return "apple_sign_in_failed"
        case .googleSignInFailed: return "google_sign_in_failed"
        case .unknown: return "unknown"
        }
    }
}

extension NSError {
    var telemetryCode: String {
        "\(domain)_\(code)"
    }
}
