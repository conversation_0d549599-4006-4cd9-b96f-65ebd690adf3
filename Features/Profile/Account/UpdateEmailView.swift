import SwiftUI
import FirebaseAuth

struct UpdateEmailView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss

    @StateObject private var networkService = OptimizedNetworkService.shared

    @State private var newEmail = ""
    @State private var isProcessing = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false

    private let telemetry = ServiceContainer.shared.telemetryService

    var body: some View {
        NavigationView {
            Form {
                Section {
                    TextField("New Email", text: $newEmail)
                        .textContentType(.emailAddress)
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                } header: {
                    Text("Email Address")
                } footer: {
                    Text("You'll receive a verification email at this address.")
                }

                if let message = errorMessage {
                    Section {
                        Text(message)
                            .font(.caption)
                            .foregroundStyle(.red)
                    }
                }
            }
            .navigationTitle("Update Email")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Update") {
                        Task { await updateEmail() }
                    }
                    .disabled(!isFormValid || isProcessing || !networkService.isOnline)
                }
            }
        }
        .onAppear {
            newEmail = authService.currentUser?.email ?? ""
        }
        .alert("Email Updated", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your email has been updated. Please verify the new address.")
        }
    }
}

private extension UpdateEmailView {
    var isFormValid: Bool {
        !newEmail.isEmpty && newEmail.contains("@")
    }

    func updateEmail() async {
        guard !isProcessing else { return }
        isProcessing = true
        errorMessage = nil
        let start = Date()

        do {
            try await authService.safeUpdateEmail(to: newEmail)
            trackAccountEvent(.emailUpdated, startedAt: start, error: nil)
            showingSuccess = true
        } catch {
            trackAccountEvent(.emailUpdated, startedAt: start, error: error)
            errorMessage = error.localizedDescription
        }

        isProcessing = false
    }

    func trackAccountEvent(_ event: TelemetryService.AccountEvent, startedAt: Date, error: Error?) {
        AccountTelemetryHelper.track(
            event,
            authService: authService,
            telemetry: telemetry,
            startedAt: startedAt,
            error: error
        )
    }
}
