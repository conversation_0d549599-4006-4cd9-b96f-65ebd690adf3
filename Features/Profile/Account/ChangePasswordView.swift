import SwiftUI
import FirebaseAuth

struct ChangePasswordView: View {
    @Environment(AuthenticationService.self) private var authService
    @Environment(\.dismiss) private var dismiss

    @StateObject private var networkService = OptimizedNetworkService.shared

    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var isProcessing = false
    @State private var errorMessage: String?
    @State private var showingSuccess = false

    private let telemetry = ServiceContainer.shared.telemetryService

    var body: some View {
        NavigationView {
            Form {
                Section {
                    SecureField("Current Password", text: $currentPassword)
                        .textContentType(.password)

                    SecureField("New Password", text: $newPassword)
                        .textContentType(.newPassword)

                    SecureField("Confirm New Password", text: $confirmPassword)
                        .textContentType(.newPassword)
                } header: {
                    Text("Password Change")
                } footer: {
                    Text("Passwords must be at least 6 characters long.")
                }

                if let message = errorMessage {
                    Section {
                        Text(message)
                            .font(.caption)
                            .foregroundStyle(.red)
                    }
                }
            }
            .navigationTitle("Change Password")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        Task { await changePassword() }
                    }
                    .disabled(!isFormValid || isProcessing || !networkService.isOnline)
                }
            }
        }
        .alert("Password Changed", isPresented: $showingSuccess) {
            Button("OK") { dismiss() }
        } message: {
            Text("Your password has been successfully updated.")
        }
    }
}

private extension ChangePasswordView {
    var isFormValid: Bool {
        !currentPassword.isEmpty &&
        !newPassword.isEmpty &&
        newPassword == confirmPassword &&
        newPassword.count >= 6
    }

    func changePassword() async {
        guard !isProcessing else { return }
        isProcessing = true
        errorMessage = nil

        guard let email = authService.currentUser?.email else {
            errorMessage = "Email address unavailable."
            isProcessing = false
            return
        }

        let start = Date()

        do {
            let credential = try authService.emailCredential(email: email, password: currentPassword)
            try await authService.reauthenticateUser(with: credential)
            let manualContext = authService.consumeReauthContext()
            try await authService.safeUpdatePassword(to: newPassword)
            let updateContext = authService.consumeReauthContext()
            let combinedContext = combine(manualContext, updateContext)
            trackAccountEvent(.changePassword, startedAt: start, error: nil, overrideContext: combinedContext)
            showingSuccess = true
        } catch {
            let context = authService.consumeReauthContext()
            trackAccountEvent(.changePassword, startedAt: start, error: error, overrideContext: context)
            if let authError = error as? AuthenticationService.AuthError, case .wrongPassword = authError {
                errorMessage = "Current password is incorrect."
            } else {
                errorMessage = error.localizedDescription
            }
        }

        isProcessing = false
    }

    func trackAccountEvent(
        _ event: TelemetryService.AccountEvent,
        startedAt: Date,
        error: Error?,
        overrideContext: (required: Bool, attempts: Int)? = nil
    ) {
        AccountTelemetryHelper.track(
            event,
            authService: authService,
            telemetry: telemetry,
            startedAt: startedAt,
            error: error,
            overrideContext: overrideContext
        )
    }

    func combine(
        _ manual: (required: Bool, attempts: Int),
        _ update: (required: Bool, attempts: Int)
    ) -> (required: Bool, attempts: Int) {
        let required = manual.required || update.required
        let attempts = manual.attempts + update.attempts
        return (required, attempts)
    }
}
