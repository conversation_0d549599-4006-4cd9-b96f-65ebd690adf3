import SwiftUI

/// Family Size管理页面
/// 
/// 用于设置家庭人数，影响食谱的分量
struct FamilySizeView: View {

    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss

    // New model: adults and kids
    @State private var numberOfAdults: Int = 1
    @State private var numberOfKids: Int = 0
    @State private var hasUnsavedChanges = false

    // MARK: - Callback-Based Properties
    // New style
    let initialAdults: Int?
    let initialKids: Int?
    let onSaveAdultsKids: ((Int, Int) async -> Void)?
    // Legacy style (kept for backward compatibility)
    let initialFamilySize: Int?
    let onSave: ((Int) async -> Void)?

    // MARK: - Initializers

    /// Default initializer for backward compatibility
    init() {
        self.initialAdults = nil
        self.initialKids = nil
        self.onSaveAdultsKids = nil
        self.initialFamilySize = nil
        self.onSave = nil
    }

    /// New initializer with adults/kids separation
    init(initialAdults: Int, initialKids: Int, onSave: @escaping (Int, Int) async -> Void) {
        self.initialAdults = initialAdults
        self.initialKids = initialKids
        self.onSaveAdultsKids = onSave
        self.initialFamilySize = nil
        self.onSave = nil
    }

    /// Legacy initializer (will be removed in future)
    init(initialFamilySize: Int, onSave: @escaping (Int) async -> Void) {
        self.initialAdults = nil
        self.initialKids = nil
        self.onSaveAdultsKids = nil
        self.initialFamilySize = initialFamilySize
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection

                // Adults/Kids steppers
                Form {
                    Section(header: Text("Household Members")) {
                        Stepper(value: $numberOfAdults, in: 1...20) {
                            HStack {
                                Text("Adults")
                                Spacer()
                                Text("\(numberOfAdults)").foregroundStyle(.secondary)
                            }
                        }
                        Stepper(value: $numberOfKids, in: 0...20) {
                            HStack {
                                VStack(alignment: .leading) {
                                    Text("Kids")
                                    Text("(under 16)")
                                        .font(.footnote)
                                        .foregroundStyle(.secondary)
                                }
                                Spacer()
                                Text("\(numberOfKids)").foregroundStyle(.secondary)
                            }
                        }
                    }
                    Section(footer: Text("At least one adult is required.")) {
                        HStack {
                            Text("Total")
                            Spacer()
                            Text("\(numberOfAdults + numberOfKids) people").foregroundStyle(.secondary)
                        }
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Family Members")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        hasUnsavedChanges = false
                        if let onSaveAdultsKids = onSaveAdultsKids {
                            Task { await onSaveAdultsKids(numberOfAdults, numberOfKids) }
                        } else if let onSave = onSave {
                            // Legacy path: save total
                            Task { await onSave(numberOfAdults + numberOfKids) }
                        } else {
                            // Legacy fallback to auth service
                            saveFamilySize()
                            dismiss()
                        }
                        dismiss()
                    }
                    .font(.body.weight(.medium))
                    .foregroundStyle(.blue)
                }
            }
        }
        .onAppear { loadInitialValues() }
    }
    
    // MARK: - Header Section

    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.2.fill")
                .font(.system(size: 50))
                .foregroundStyle(.blue.gradient)

            VStack(spacing: 8) {
                Text("Family Members")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(.primary)

                Text("How many people do you typically cook for?")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Helper Methods
    
    private func loadInitialValues() {
        if let a = initialAdults, let k = initialKids {
            numberOfAdults = max(1, a)
            numberOfKids = max(0, k)
        } else if let initialFamilySize = initialFamilySize {
            numberOfAdults = 1
            numberOfKids = max(0, initialFamilySize - 1)
        } else if let preferences = authService.userPreferences {
            numberOfAdults = max(1, preferences.numberOfAdults)
            numberOfKids = max(0, preferences.numberOfKids)
        }
    }

    private func saveFamilySize() {
        var preferences = authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
        preferences.numberOfAdults = max(1, numberOfAdults)
        preferences.numberOfKids = max(0, numberOfKids)
        preferences.familySize = preferences.numberOfAdults + preferences.numberOfKids
        preferences.lastUpdated = Date()
        authService.userPreferences = preferences
        hasUnsavedChanges = false
    }
}

// MARK: - Preview

#Preview {
    FamilySizeView()
        .environment(AuthenticationService())
} 