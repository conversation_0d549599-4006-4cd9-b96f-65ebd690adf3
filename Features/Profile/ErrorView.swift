import SwiftUI

struct ErrorView: View {
    var error: AuthenticationService.AuthError
    var retryAction: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
                .font(.system(size: 40))
            
            Text("Error")
                .font(.headline)
            
            Text(error.localizedDescription)
                .font(.subheadline)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            if let retryAction = retryAction {
                Button(action: retryAction) {
                    Text("Try Again")
                        .fontWeight(.semibold)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
                .padding(.top, 8)
            }
        }
        .padding(24)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 4)
    }
}

// MARK: - Previews

#Preview("Network Error") {
    ErrorView(
        error: .networkError,
        retryAction: {}
    )
    .padding()
}

#Preview("Invalid Email") {
    ErrorView(
        error: .invalidEmail
    )
    .padding()
}

#Preview("User Cancelled") {
    ErrorView(
        error: .userCancelled,
        retryAction: {}
    )
    .padding()
} 