import SwiftUI

/// Dietary Restrictions管理页面
/// 
/// 用于管理用户的饮食偏好和限制
struct DietaryRestrictionsView: View {
    
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedRestrictions: Set<DietaryRestriction> = []
    @State private var hasUnsavedChanges = false
    
    // MARK: - New Callback-Based Properties (Task 24)
    
    let initialRestrictions: [DietaryRestriction]?
    let onSave: (([DietaryRestriction]) async -> Void)?
    
    // Available restriction options
    private let availableRestrictions: [DietaryRestriction] = DietaryRestriction.allCases
    
    // MARK: - Initializers
    
    /// Default initializer for backward compatibility
    init() {
        self.initialRestrictions = nil
        self.onSave = nil
    }
    
    /// New callback-based initializer for Task 24 integration
    init(initialRestrictions: [DietaryRestriction], onSave: @escaping ([DietaryRestriction]) async -> Void) {
        self.initialRestrictions = initialRestrictions
        self.onSave = onSave
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                // Restrictions List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(availableRestrictions.sorted(by: { $0.rawValue < $1.rawValue }), id: \.self) { restriction in
                            restrictionRow(restriction)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Dietary Restrictions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        if let onSave = onSave {
                            // Use callback-based saving (Task 24)
                            Task {
                                await onSave(Array(selectedRestrictions))
                            }
                        } else {
                            // Fallback to legacy saving
                            saveRestrictions()
                            dismiss()
                        }
                    }
                    .font(.body.weight(.medium))
                    .foregroundStyle(.blue)
                }
            }
        }
        .onAppear {
            loadCurrentRestrictions()
        }
    }
    
    // MARK: - Header Section
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "leaf.fill")
                .font(.system(size: 50))
                .foregroundStyle(.green.gradient)
            
            VStack(spacing: 8) {
                Text("Your Dietary Preferences")
                    .font(.title2.weight(.semibold))
                    .foregroundStyle(.primary)
                
                Text("Choose dietary restrictions that match your lifestyle")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(Color(.systemBackground))
    }
    
    // MARK: - Restriction Row
    
    @ViewBuilder
    private func restrictionRow(_ restriction: DietaryRestriction) -> some View {
        Button {
            toggleRestriction(restriction)
        } label: {
            HStack(spacing: 16) {
                // Icon
                Text(restriction.icon)
                    .font(.title2)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(selectedRestrictions.contains(restriction) ? .green.opacity(0.1) : .gray.opacity(0.1))
                    )
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(restriction.rawValue)
                        .font(.body.weight(.medium))
                        .foregroundStyle(.primary)
                        .multilineTextAlignment(.leading)
                    
                    Text(restriction.description)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                // Selection Indicator
                if selectedRestrictions.contains(restriction) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.title2)
                        .foregroundStyle(.green)
                } else {
                    Image(systemName: "circle")
                        .font(.title2)
                        .foregroundStyle(.gray)
                }
            }
            .padding(16)
            .background(Color(.systemBackground))
            .clipShape(RoundedRectangle(cornerRadius: 12))
            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Helper Methods
    
    private func toggleRestriction(_ restriction: DietaryRestriction) {
        if selectedRestrictions.contains(restriction) {
            selectedRestrictions.remove(restriction)
        } else {
            selectedRestrictions.insert(restriction)
        }
        hasUnsavedChanges = true
    }
    
    private func loadCurrentRestrictions() {
        if let initialRestrictions = initialRestrictions {
            // Use provided initial restrictions (Task 24)
            selectedRestrictions = Set(initialRestrictions)
        } else if let preferences = authService.userPreferences {
            // Fallback to legacy loading
            selectedRestrictions = Set(preferences.dietaryRestrictions)
        }
    }
    
    private func saveRestrictions() {
        var preferences = authService.userPreferences ?? UserPreferences.createDefault(for: authService.currentUser?.uid ?? "")
        preferences.dietaryRestrictions = Array(selectedRestrictions)
        preferences.lastUpdated = Date()
        authService.userPreferences = preferences
        hasUnsavedChanges = false
    }
}

// MARK: - DietaryRestriction Extension

extension DietaryRestriction {
    var description: String {
        switch self {
        case .vegan:
            return "No animal products at all"
        case .vegetarian:
            return "No meat, poultry, or fish"
        case .lactoOvoVegetarian:
            return "Includes dairy and eggs; excludes meat and fish"
        case .pescatarian:
            return "Fish and seafood allowed; no meat"
        case .paleo:
            return "Whole foods; avoids processed items"
        case .keto:
            return "Very low carb, high fat diet"
        case .lowCarb:
            return "Limits carbohydrate intake"
        case .lowSodium:
            return "Reduced salt and sodium intake"
        case .lowFat:
            return "Limits dietary fat intake"
        case .lowFODMAP:
            return "Limits fermentable carbs (FODMAPs)"
        case .diabeticFriendly:
            return "Supports blood sugar management"
        case .halal:
            return "Islamic dietary guidelines"
        case .kosher:
            return "Jewish dietary laws"
        }
    }
}

#Preview {
    DietaryRestrictionsView()
        .environment(AuthenticationService())
} 