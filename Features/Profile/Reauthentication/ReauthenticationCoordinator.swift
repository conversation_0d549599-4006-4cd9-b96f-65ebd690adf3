import SwiftUI
import AuthenticationServices
import FirebaseAuth
@preconcurrency import GoogleSignIn
import UIKit

@MainActor
final class ReauthenticationCoordinator: ObservableObject {
    enum ProviderOption: Identifiable, Equatable {
        case password(email: String)
        case apple
        case google

        var id: String {
            switch self {
            case .password(let email):
                return "password_\(email)"
            case .apple:
                return "apple"
            case .google:
                return "google"
            }
        }

        var title: String {
            switch self {
            case .password:
                return "Account Password"
            case .apple:
                return "Sign in with Apple"
            case .google:
                return "Continue with Google"
            }
        }

        var detail: String? {
            switch self {
            case .password(let email):
                return email
            case .apple:
                return "Use Face ID or Touch ID"
            case .google:
                return "Authenticate with your Google account"
            }
        }
    }

    @Published var isPresented = false
    @Published var isProcessing = false
    @Published var errorMessage: String?
    @Published var password: String = ""
    @Published var providerOptions: [ProviderOption] = []
    @Published var selectedProvider: ProviderOption?

    private let authService: AuthenticationService
    private var continuation: CheckedContinuation<Void, Error>?
    private(set) var pendingOperation: AuthenticationService.SecureOperation?

    init(authService: AuthenticationService) {
        self.authService = authService
        providerOptions = buildProviderOptions()
        selectedProvider = providerOptions.first

        authService.reauthenticationHandler = { [weak self] operation in
            guard let self else { throw AuthenticationService.AuthError.requiresRecentLogin }
            try await self.beginReauthentication(for: operation)
        }
    }

    var operationTitle: String {
        guard let pendingOperation else { return "Verify Identity" }
        switch pendingOperation {
        case .updateEmail:
            return "Confirm Email Update"
        case .updatePassword:
            return "Confirm Password Change"
        case .deleteAccount:
            return "Confirm Account Deletion"
        case .sendVerificationEmail:
            return "Verify Your Identity"
        case .sendPasswordReset:
            return "Verify Your Identity"
        case .updateProfile:
            return "Verify Profile Update"
        case .reloadUser:
            return "Verify Identity"
        case .custom(let value):
            return value
        }
    }

    private func beginReauthentication(for operation: AuthenticationService.SecureOperation) async throws {
        guard authService.currentUser != nil else {
            throw AuthenticationService.AuthError.userNotFound
        }

        providerOptions = buildProviderOptions()
        guard !providerOptions.isEmpty else {
            throw AuthenticationService.AuthError.operationNotAllowed
        }

        selectedProvider = providerOptions.first
        password = ""
        errorMessage = nil
        pendingOperation = operation
        isProcessing = false

        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            self.continuation = continuation
            self.isPresented = true
        }
    }

    private func buildProviderOptions() -> [ProviderOption] {
        guard let user = authService.currentUser else { return [] }
        var options: [ProviderOption] = []
        let providerIDs = user.providerData.map(\.providerID)

        if providerIDs.contains("password"), let email = user.email {
            options.append(.password(email: email))
        }
        if providerIDs.contains("apple.com") {
            options.append(.apple)
        }
        if providerIDs.contains("google.com") {
            options.append(.google)
        }
        return options
    }

    func cancel() {
        if let continuation {
            continuation.resume(throwing: AuthenticationService.AuthError.userCancelled)
        }
        resetState()
    }

    private func complete() {
        continuation?.resume(returning: ())
        resetState()
    }

    private func resetState() {
        continuation = nil
        pendingOperation = nil
        isPresented = false
        isProcessing = false
        password = ""
    }

    func submitPassword() {
        guard case let .password(email) = selectedProvider else { return }
        guard !password.isEmpty else {
            errorMessage = "Password is required."
            return
        }

        isProcessing = true
        errorMessage = nil

        Task { @MainActor in
            do {
                let credential = try authService.emailCredential(email: email, password: password)
                authService.recordReauthAttempt()
                try await authService.reauthenticateUser(with: credential)
                complete()
            } catch let authError as AuthenticationService.AuthError {
                authService.handleAuthError(authError)
                if case .userCancelled = authError {
                    isProcessing = false
                    return
                }
                errorMessage = authError.localizedDescription
                isProcessing = false
            } catch {
                authService.handleAuthError(error)
                errorMessage = error.localizedDescription
                isProcessing = false
            }
        }
    }

    func configureAppleRequest(_ request: ASAuthorizationAppleIDRequest) {
        let appleRequest = authService.startSignInWithApple()
        request.requestedScopes = appleRequest.requestedScopes
        request.nonce = appleRequest.nonce
    }

    func handleAppleAuthorizationResult(_ result: Result<ASAuthorization, Error>) {
        isProcessing = true
        errorMessage = nil

        Task { @MainActor in
            switch result {
            case .success(let authorization):
                do {
                    let credential = try authService.appleCredential(from: authorization)
                    authService.recordReauthAttempt()
                    try await authService.reauthenticateUser(with: credential)
                    complete()
                } catch let authError as AuthenticationService.AuthError {
                    authService.handleAuthError(authError)
                    if case .userCancelled = authError {
                        isProcessing = false
                        return
                    }
                    errorMessage = authError.localizedDescription
                    isProcessing = false
                } catch {
                    authService.handleAuthError(error)
                    errorMessage = error.localizedDescription
                    isProcessing = false
                }
            case .failure(let error):
                authService.handleAuthError(error)
                if let appleError = error as? ASAuthorizationError,
                   appleError.code == .canceled {
                    isProcessing = false
                    return
                }
                errorMessage = error.localizedDescription
                isProcessing = false
            }
        }
    }

    func startGoogleReauthentication() {
        isProcessing = true
        errorMessage = nil

        Task { @MainActor in
            do {
                guard GIDSignIn.sharedInstance.configuration != nil else {
                    throw AuthenticationService.AuthError.googleSignInFailed
                }
                let presenter = try topViewController()
                let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: presenter)
                let credential = try authService.googleCredential(from: result)
                authService.recordReauthAttempt()
                try await authService.reauthenticateUser(with: credential)
                complete()
            } catch let authError as AuthenticationService.AuthError {
                authService.handleAuthError(authError)
                if case .userCancelled = authError {
                    isProcessing = false
                    return
                }
                errorMessage = authError.localizedDescription
                isProcessing = false
            } catch {
                authService.handleAuthError(error)
                errorMessage = error.localizedDescription
                isProcessing = false
            }
        }
    }

    private func topViewController() throws -> UIViewController {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            throw AuthenticationService.AuthError.googleSignInFailed
        }

        var topController = rootViewController
        while let presented = topController.presentedViewController {
            topController = presented
        }
        return topController
    }
}

extension ReauthenticationCoordinator.ProviderOption: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}

