import SwiftUI
import AuthenticationServices

struct ReauthenticationView: View {
    @ObservedObject var coordinator: ReauthenticationCoordinator

    var body: some View {
        NavigationView {
            ZStack {
                VStack(alignment: .leading, spacing: 24) {
                    header

                    if coordinator.providerOptions.count > 1 {
                        providerPicker
                    }

                    providerContent

                    if let errorMessage = coordinator.errorMessage, !errorMessage.isEmpty {
                        Text(errorMessage)
                            .font(.footnote)
                            .foregroundStyle(.red)
                    }

                    Spacer()
                }
                .padding()

                if coordinator.isProcessing {
                    Color.black.opacity(0.08)
                        .ignoresSafeArea()
                    ProgressView()
                        .progressViewStyle(.circular)
                }
            }
            .navigationTitle("Verify Identity")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        coordinator.cancel()
                    }
                    .disabled(coordinator.isProcessing)
                }
            }
        }
        .interactiveDismissDisabled(coordinator.isProcessing)
    }

    private var header: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(coordinator.operationTitle)
                .font(.title3.weight(.semibold))

            Text("For your security, confirm your identity to continue.")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
    }

    private var providerPicker: some View {
        Picker("Authentication Method", selection: $coordinator.selectedProvider) {
            ForEach(coordinator.providerOptions) { option in
                Text(option.title)
                    .tag(Optional(option))
            }
        }
        .pickerStyle(.segmented)
    }

    @ViewBuilder
    private var providerContent: some View {
        switch coordinator.selectedProvider {
        case .password(let email):
            passwordForm(email: email)
        case .apple:
            appleButton
        case .google:
            googleButton
        case .none:
            Text("No authentication methods are available for this account.")
                .font(.footnote)
                .foregroundStyle(.secondary)
        }
    }

    private func passwordForm(email: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Enter the password for \(email)")
                .font(.subheadline)
                .foregroundStyle(.secondary)

            SecureField("Password", text: $coordinator.password)
                .textContentType(.password)
                .disabled(coordinator.isProcessing)

            Button {
                coordinator.submitPassword()
            } label: {
                Text("Continue")
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(.borderedProminent)
            .disabled(coordinator.password.isEmpty || coordinator.isProcessing)
        }
    }

    private var appleButton: some View {
        SignInWithAppleButton(.continue) { request in
            coordinator.configureAppleRequest(request)
        } onCompletion: { result in
            coordinator.handleAppleAuthorizationResult(result)
        }
        .signInWithAppleButtonStyle(.black)
        .frame(height: 48)
        .cornerRadius(10)
        .disabled(coordinator.isProcessing)
    }

    private var googleButton: some View {
        Button {
            coordinator.startGoogleReauthentication()
        } label: {
            HStack {
                Image(systemName: "globe")
                    .accessibilityHidden(true)

                Text("Continue with Google")
                    .fontWeight(.semibold)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 48)
        }
        .buttonStyle(.bordered)
        .disabled(coordinator.isProcessing)
    }
}
