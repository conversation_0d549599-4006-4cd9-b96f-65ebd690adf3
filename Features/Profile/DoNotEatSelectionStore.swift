import Foundation
import SwiftUI

@MainActor
final class DoNotEatSelectionStore: ObservableObject {
    @Published private(set) var selectedAllergies: Set<AllergyIntolerance> = []
    @Published private(set) var selectedStrictEnums: Set<StrictExclusion> = []
    @Published private(set) var customEntries: [CustomEntry] = []

    private(set) var initialSnapshot: SelectionSnapshot?

    var hasLoaded: Bool { initialSnapshot != nil }
    var hasChanges: Bool {
        guard let initialSnapshot else { return false }
        return initialSnapshot != currentSnapshot
    }

    var totalSelectionCount: Int { summaryChips.count }

    var summaryChips: [SummaryChip] {
        let allergyChips = selectedAllergies
            .sorted { $0.rawValue < $1.rawValue }
            .map { SummaryChip(kind: .allergy($0)) }

        let strictChips = selectedStrictEnums
            .sorted { $0.rawValue < $1.rawValue }
            .map { SummaryChip(kind: .strict($0)) }

        let customChips = customEntries
            .sorted { $0.displayText.localizedCaseInsensitiveCompare($1.displayText) == .orderedAscending }
            .map { SummaryChip(kind: .custom($0)) }

        return allergyChips + strictChips + customChips
    }

    var allSelectedKeys: Set<String> {
        var keys = Set<String>()
        selectedAllergies.forEach { keys.insert(DoNotEatHelper.normalizeForComparison($0.rawValue)) }
        selectedStrictEnums.forEach { keys.insert(DoNotEatHelper.normalizeForComparison($0.rawValue)) }
        customEntries.forEach { keys.insert($0.normalizedKey) }
        return keys
    }

    var currentSnapshot: SelectionSnapshot {
        SelectionSnapshot(
            allergies: selectedAllergies.sorted { $0.rawValue < $1.rawValue },
            strictExclusions: selectedStrictEnums.sorted { $0.rawValue < $1.rawValue },
            customStrings: customEntries
                .map { $0.displayText }
                .sorted { $0.localizedCaseInsensitiveCompare($1) == .orderedAscending }
        )
    }

    func load(from preferences: UserPreferences) {
        let dedupe = DoNotEatHelper.deduplicateSelections(
            allergies: preferences.allergiesIntolerances,
            strictEnums: preferences.strictExclusions,
            customStrings: preferences.customStrictExclusions
        )

        selectedAllergies = Set(dedupe.allergies)
        selectedStrictEnums = Set(dedupe.strictExclusions)
        customEntries = dedupe.customStrings.map { CustomEntry(displayText: $0) }
        initialSnapshot = currentSnapshot
    }

    func toggleAllergy(_ allergy: AllergyIntolerance) {
        let key = DoNotEatHelper.normalizeForComparison(allergy.rawValue)
        if selectedAllergies.contains(allergy) {
            selectedAllergies.remove(allergy)
        } else {
            removeConflictingSelections(with: key)
            selectedAllergies.insert(allergy)
        }
    }

    func toggleStrict(_ exclusion: StrictExclusion) {
        let key = DoNotEatHelper.normalizeForComparison(exclusion.rawValue)
        if selectedStrictEnums.contains(exclusion) {
            selectedStrictEnums.remove(exclusion)
        } else {
            removeConflictingSelections(with: key)
            selectedStrictEnums.insert(exclusion)
        }
    }

    func addCustomEntry(_ text: String) {
        let entry = CustomEntry(displayText: text)
        guard !entry.displayText.isEmpty else { return }
        let key = entry.normalizedKey

        if let allergy = AllergyIntolerance.allCases.first(where: { DoNotEatHelper.normalizeForComparison($0.rawValue) == key }) {
            removeConflictingSelections(with: key)
            selectedAllergies.insert(allergy)
            return
        }

        if let strict = StrictExclusion.allCases.first(where: { DoNotEatHelper.normalizeForComparison($0.rawValue) == key }) {
            removeConflictingSelections(with: key)
            selectedStrictEnums.insert(strict)
            return
        }

        removeConflictingSelections(with: key)
        guard customEntries.contains(entry) == false else { return }
        customEntries.append(entry)
        customEntries.sort { $0.displayText.localizedCaseInsensitiveCompare($1.displayText) == .orderedAscending }
    }

    func removeCustomEntry(_ entry: CustomEntry) {
        customEntries.removeAll { $0.normalizedKey == entry.normalizedKey }
    }

    func removeChip(_ chip: SummaryChip) {
        switch chip.kind {
        case .allergy(let allergy):
            selectedAllergies.remove(allergy)
        case .strict(let exclusion):
            selectedStrictEnums.remove(exclusion)
        case .custom(let entry):
            removeCustomEntry(entry)
        }
    }

    func markSaved() {
        initialSnapshot = currentSnapshot
    }

    func dedupeResultForSave() -> DoNotEatHelper.DedupeResult {
        DoNotEatHelper.deduplicateSelections(
            allergies: Array(selectedAllergies),
            strictEnums: Array(selectedStrictEnums),
            customStrings: customEntries.map { $0.displayText }
        )
    }

    // MARK: - Private helpers

    private func removeConflictingSelections(with key: String) {
        if let allergy = selectedAllergies.first(where: { DoNotEatHelper.normalizeForComparison($0.rawValue) == key }) {
            selectedAllergies.remove(allergy)
        }
        if let strict = selectedStrictEnums.first(where: { DoNotEatHelper.normalizeForComparison($0.rawValue) == key }) {
            selectedStrictEnums.remove(strict)
        }
        customEntries.removeAll { $0.normalizedKey == key }
    }

    // MARK: - Types

    struct SelectionSnapshot: Equatable {
        let allergies: [AllergyIntolerance]
        let strictExclusions: [StrictExclusion]
        let customStrings: [String]
    }

    struct CustomEntry: Identifiable, Equatable {
        let displayText: String
        let normalizedKey: String

        init(displayText: String) {
            let sanitized = Self.sanitize(displayText)
            self.displayText = sanitized
            self.normalizedKey = DoNotEatHelper.normalizeForComparison(sanitized)
        }

        var id: String { normalizedKey }

        static func sanitize(_ text: String) -> String {
            let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmed.isEmpty else { return "" }
            let components = trimmed.split { $0.isWhitespace }
            return components.joined(separator: " ")
        }
    }

    struct SummaryChip: Identifiable, Equatable {
        enum Kind {
            case allergy(AllergyIntolerance)
            case strict(StrictExclusion)
            case custom(CustomEntry)
        }

        let kind: Kind

        var id: String {
            switch kind {
            case .allergy(let value):
                return "allergy-\(value.rawValue)"
            case .strict(let value):
                return "strict-\(value.rawValue)"
            case .custom(let entry):
                return "custom-\(entry.normalizedKey)"
            }
        }

        static func == (lhs: SummaryChip, rhs: SummaryChip) -> Bool {
            lhs.id == rhs.id
        }

        var title: String {
            switch kind {
            case .allergy(let value):
                return value.rawValue
            case .strict(let value):
                return value.rawValue
            case .custom(let entry):
                return entry.displayText
            }
        }

        var badge: String? {
            switch kind {
            case .allergy:
                return "Allergy"
            case .strict:
                return "Strict"
            case .custom:
                return "Custom"
            }
        }

        var category: ChipCategory {
            switch kind {
            case .allergy:
                return .allergy
            case .strict:
                return .strict
            case .custom:
                return .custom
            }
        }
    }

    enum ChipCategory {
        case allergy
        case strict
        case custom
    }
}
