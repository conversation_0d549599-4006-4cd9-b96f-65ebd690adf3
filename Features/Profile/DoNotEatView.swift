import SwiftUI

struct DoNotEatView: View {
    @Environment(AuthenticationService.self) private var authService: AuthenticationService
    @Environment(\.dismiss) private var dismiss

    private let userProfileService = UserProfileService.shared
    private var telemetryService: TelemetryService { ServiceContainer.shared.telemetryService }

    @StateObject private var store = DoNotEatSelectionStore()

    @State private var isLoading = true
    @State private var isSaving = false
    @State private var showingCustomSheet = false
    @State private var showingHelp = false
    @State private var saveError: Error?
    @State private var showingSaveError = false

    private let gridColumns: [GridItem] = [
        GridItem(.adaptive(minimum: 130), spacing: 12, alignment: .leading)
    ]

    var body: some View {
        NavigationStack {
            ScrollView {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(.circular)
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                        .padding(40)
                } else {
                    VStack(alignment: .leading, spacing: 32) {
                        header

                        myExclusionsSection

                        addCustomButton

                        allergiesSection

                        strictSection
                    }
                    .padding(20)
                }
            }
            .background(Color(.systemGroupedBackground))
            .navigationTitle("Do Not Eat")
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button {
                        Task {
                            await saveSelections()
                        }
                    } label: {
                        if isSaving {
                            ProgressView()
                                .progressViewStyle(.circular)
                        } else {
                            Text("Save")
                                .font(.body.weight(.semibold))
                        }
                    }
                    .disabled(isSaveDisabled)
                }
            }
        }
        .sheet(isPresented: $showingCustomSheet) {
            CustomItemEntrySheet(
                existingKeys: store.allSelectedKeys
            ) { newItem in
                handleCustomItemSave(newItem)
            }
        }
        .sheet(isPresented: $showingHelp) {
            DoNotEatHelpSheet()
        }
        .alert("Unable to Save", isPresented: $showingSaveError, presenting: saveError) { _ in
            Button("OK", role: .cancel) {}
        } message: { error in
            Text(errorMessage(for: error))
        }
        .onAppear {
            if isLoading {
                loadInitialSelections()
            }
        }
    }

    // MARK: - Sections

    private var header: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Keep your meals safe and satisfying.")
                .font(.title3.weight(.semibold))
                .foregroundStyle(.primary)
            Text("Items in this list will be strictly excluded from recipe ideas. Mark allergies to avoid any risk.")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
    }

    private var myExclusionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Text("My Exclusions")
                    .font(.title3.weight(.semibold))
                Button {
                    showingHelp = true
                } label: {
                    Image(systemName: "questionmark.circle")
                        .font(.title3)
                        .foregroundStyle(.secondary)
                        .frame(width: 44, height: 44)
                        .contentShape(Rectangle())
                }
                .buttonStyle(.plain)
                .accessibilityLabel("About My Exclusions")
                .accessibilityHint("Opens guidance about allergies, strict exclusions, and custom items.")
                Spacer()
                if !store.summaryChips.isEmpty {
                    Text("\(store.totalSelectionCount)")
                        .font(.footnote.weight(.semibold))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.accentColor.opacity(0.1), in: Capsule())
                }
            }

            if store.summaryChips.isEmpty {
                Text("No items selected yet.")
                    .font(.footnote)
                    .foregroundStyle(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                LazyVGrid(columns: gridColumns, alignment: .leading, spacing: 12) {
                    ForEach(store.summaryChips) { chip in
                        let uiCategory = mapChipCategory(chip.category)
                        DoNotEatChip(
                            title: chip.title,
                            badge: chip.badge,
                            isSelected: true,
                            category: uiCategory,
                            accentColor: accentColor(for: uiCategory),
                            accessibilityLabel: accessibilityLabel(for: chip.title, category: uiCategory, isSelected: true),
                            accessibilityHint: accessibilityRemovalHint(for: uiCategory)
                        ) {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.85)) {
                                if case .custom(let entry) = chip.kind {
                                    telemetryService.trackDoNotEatCustomRemoved(length: entry.displayText.count)
                                }
                                store.removeChip(chip)
                            }
                        }
                    }
                }
                .animation(.spring(response: 0.3, dampingFraction: 0.85), value: store.summaryChips)
            }
        }
    }

    private var addCustomButton: some View {
        Button {
            showingCustomSheet = true
        } label: {
            Label("Add custom item", systemImage: "plus")
                .font(.body.weight(.semibold))
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .buttonStyle(DoNotEatChipStyle(isSelected: false, showOutline: true, accentColor: .accentColor))
        .accessibilityLabel("Add custom item")
        .accessibilityHint("Opens a sheet to enter your own exclusion.")
    }

    private var allergiesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Allergies & Intolerances")
                .font(.title3.weight(.semibold))

            LazyVGrid(columns: gridColumns, alignment: .leading, spacing: 12) {
                ForEach(AllergyIntolerance.allCases, id: \.self) { allergy in
                    let isSelected = store.selectedAllergies.contains(allergy)
                    DoNotEatChip(
                        title: allergy.rawValue,
                        badge: isSelected ? "Allergy" : nil,
                        isSelected: isSelected,
                        category: .allergy,
                        accentColor: accentColor(for: .allergy),
                        accessibilityLabel: accessibilityLabel(for: allergy.rawValue, category: .allergy, isSelected: isSelected),
                        accessibilityHint: accessibilityToggleHint(for: .allergy)
                    ) {
                        withAnimation(.spring(response: 0.28, dampingFraction: 0.8)) {
                            store.toggleAllergy(allergy)
                        }
                    }
                }
            }
        }
    }

    private var strictSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Strict Exclusions")
                .font(.title3.weight(.semibold))

            LazyVGrid(columns: gridColumns, alignment: .leading, spacing: 12) {
                ForEach(StrictExclusion.allCases, id: \.self) { exclusion in
                    let isSelected = store.selectedStrictEnums.contains(exclusion)
                    DoNotEatChip(
                        title: exclusion.rawValue,
                        badge: isSelected ? "Strict" : nil,
                        isSelected: isSelected,
                        category: .strict,
                        accentColor: accentColor(for: .strict),
                        accessibilityLabel: accessibilityLabel(for: exclusion.rawValue, category: .strict, isSelected: isSelected),
                        accessibilityHint: accessibilityToggleHint(for: .strict)
                    ) {
                        withAnimation(.spring(response: 0.28, dampingFraction: 0.8)) {
                            store.toggleStrict(exclusion)
                        }
                    }
                }
            }
        }
    }

    // MARK: - Helpers

    private func mapChipCategory(_ category: DoNotEatSelectionStore.ChipCategory) -> DoNotEatChip.Category {
        switch category {
        case .allergy:
            return .allergy
        case .strict:
            return .strict
        case .custom:
            return .custom
        }
    }

    private func accessibilityLabel(for title: String, category: DoNotEatChip.Category, isSelected: Bool) -> String {
        let prefix = accessibilityPrefix(for: category)
        let state = isSelected ? "selected" : "not selected"
        return "\(prefix), \(state), \(title)"
    }

    private func accessibilityToggleHint(for category: DoNotEatChip.Category) -> String {
        switch category {
        case .allergy:
            return "Double-tap to toggle this allergy."
        case .strict:
            return "Double-tap to toggle this strict exclusion."
        case .custom:
            return "Double-tap to toggle this custom exclusion."
        case .neutral:
            return "Double-tap to toggle this item."
        }
    }

    private func accessibilityRemovalHint(for category: DoNotEatChip.Category) -> String {
        switch category {
        case .allergy:
            return "Double-tap to remove this allergy from My Exclusions."
        case .strict:
            return "Double-tap to remove this strict exclusion from My Exclusions."
        case .custom:
            return "Double-tap to remove this custom exclusion from My Exclusions."
        case .neutral:
            return "Double-tap to remove this item."
        }
    }

    private func accessibilityPrefix(for category: DoNotEatChip.Category) -> String {
        switch category {
        case .allergy:
            return "Allergy chip"
        case .strict:
            return "Strict chip"
        case .custom:
            return "Custom chip"
        case .neutral:
            return "Chip"
        }
    }

    private func accentColor(for category: DoNotEatChip.Category) -> Color {
        switch category {
        case .allergy:
            return .orange
        case .strict:
            return .blue
        case .custom:
            return .purple
        case .neutral:
            return .accentColor
        }
    }

    private var isSaveDisabled: Bool {
        isLoading || isSaving || !store.hasLoaded || !store.hasChanges
    }

    private func loadInitialSelections() {
        let userId = authService.currentUser?.uid ?? ""
        let preferences = authService.userPreferences ?? UserPreferences.createDefault(for: userId)
        store.load(from: preferences)
        telemetryService.trackDoNotEatView(
            allergyCount: store.selectedAllergies.count,
            strictCount: store.selectedStrictEnums.count,
            customCount: store.customEntries.count
        )
        isLoading = false
    }

    private func handleCustomItemSave(_ text: String) {
        let previousCustom = store.customEntries.count
        let previousAllergies = store.selectedAllergies.count
        let previousStrict = store.selectedStrictEnums.count

        store.addCustomEntry(text)

        if store.customEntries.count > previousCustom {
            let sanitized = DoNotEatSelectionStore.CustomEntry(displayText: text)
            if !sanitized.displayText.isEmpty {
                telemetryService.trackDoNotEatCustomAdded(length: sanitized.displayText.count)
            }
        } else if store.selectedAllergies.count > previousAllergies || store.selectedStrictEnums.count > previousStrict {
            // Promotion to enum chips is already captured by view metrics; no custom event needed.
        }
    }

    private func errorMessage(for error: Error) -> String {
        if let authError = error as? AuthenticationService.AuthError {
            return authError.localizedDescription
        }
        return error.localizedDescription
    }

    @MainActor
    private func saveSelections() async {
        guard !isSaveDisabled else { return }
        guard let userId = authService.currentUser?.uid ?? authService.userPreferences?.userId, !userId.isEmpty else {
            saveError = AuthenticationService.AuthError.userNotFound
            showingSaveError = true
            return
        }

        isSaving = true
        saveError = nil

        let dedupe = store.dedupeResultForSave()

        var updatedPreferences = authService.userPreferences ?? UserPreferences.createDefault(for: userId)
        updatedPreferences.userId = userId
        updatedPreferences.allergiesIntolerances = dedupe.allergies
        updatedPreferences.strictExclusions = dedupe.strictExclusions
        updatedPreferences.customStrictExclusions = dedupe.customStrings
        updatedPreferences.lastUpdated = Date()

        do {
            try await userProfileService.savePreferencesOfflineAware(
                updatedPreferences,
                for: userId,
                showOfflineMessage: true
            )

            await authService.updatePreferences(updatedPreferences)
            store.markSaved()
            dismiss()
        } catch {
            saveError = error
            showingSaveError = true
        }

        isSaving = false
    }

}

private struct DoNotEatChip: View {
    enum Category {
        case allergy
        case strict
        case custom
        case neutral
    }

    let title: String
    let badge: String?
    let isSelected: Bool
    let category: Category
    let accentColor: Color
    let accessibilityLabel: String
    let accessibilityHint: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(alignment: .center, spacing: 8) {
                Text(title)
                    .font(.body.weight(.semibold))
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
                    .minimumScaleFactor(0.8)

                if let badge = badge {
                    Text(badge.uppercased())
                        .font(.caption2.weight(.bold))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 4)
                        .foregroundStyle(isSelected ? Color.white : accentColor)
                        .background(
                            Capsule()
                                .fill(isSelected ? Color.white.opacity(0.25) : accentColor.opacity(0.12))
                        )
                }

                Spacer(minLength: 0)
            }
            .frame(minHeight: 44)
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
        }
        .buttonStyle(DoNotEatChipStyle(isSelected: isSelected, accentColor: accentColor))
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityAddTraits(.isButton)
        .accessibilityAddTraits(isSelected ? .isSelected : [])
    }
}

private struct DoNotEatChipStyle: ButtonStyle {
    let isSelected: Bool
    var showOutline: Bool = false
    var accentColor: Color = .accentColor

    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundStyle(isSelected ? Color.white : accentColor)
            .background(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .fill(isSelected ? accentColor : accentColor.opacity(0.08))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20, style: .continuous)
                    .stroke(
                        showOutline || !isSelected
                            ? accentColor.opacity(showOutline ? 0.35 : 0.25)
                            : Color.clear,
                        lineWidth: showOutline || !isSelected ? 1 : 0
                    )
            )
            .opacity(configuration.isPressed ? 0.8 : 1.0)
    }
}

struct DoNotEatView_Previews: PreviewProvider {
    static var previews: some View {
        let authService = AuthenticationService()
        authService.userPreferences = .sample
        return DoNotEatView()
            .environment(authService)
    }
}
