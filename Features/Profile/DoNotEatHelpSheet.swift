import SwiftUI

struct DoNotEatHelpSheet: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Use this list to keep recipes safe and aligned with how you eat.")
                        .font(.body)
                        .foregroundStyle(.secondary)

                    VStack(alignment: .leading, spacing: 12) {
                        Text("Allergies & Intolerances")
                            .font(.headline)
                        Text("Mark anything that could cause a medical reaction. These chips are labelled as allergies and stay at the top of your list.")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }

                    VStack(alignment: .leading, spacing: 12) {
                        Text("Strict Exclusions")
                            .font(.headline)
                        Text("Use strict exclusions for ingredients you never want in meals (e.g. cilantro, mushrooms). Choose as many as you need and adjust anytime.")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }

                    VStack(alignment: .leading, spacing: 12) {
                        Text("Custom Items")
                            .font(.headline)
                        Text("Add your own ingredients when you do not see them listed. We will match common names to existing chips automatically and keep the original wording for anything custom.")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }

                    VStack(alignment: .leading, spacing: 12) {
                        Text("What happens next?")
                            .font(.headline)
                        Text("Every selection is removed from recipe suggestions, meal plans, and grocery lists. Saving works offline and syncs once you are back online.")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
                }
                .padding(24)
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .navigationTitle("About Do Not Eat")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct DoNotEatHelpSheet_Previews: PreviewProvider {
    static var previews: some View {
        DoNotEatHelpSheet()
    }
}
