核心目标： 彻底解决 Google、Apple、Email 登录模块的严重问题，清理冗余代码，并严格按照 Firebase
  官方文档重构认证流程，确保功能稳定可靠。

  ---

  第一阶段：代码审计与清理 

  这个阶段的目标是为后续的重构工作打下坚实的基础，移除所有干扰项。

  
   * 任务： 扫描整个项目，特别是 Features/Profile/，Models/ 和 Services/ 目录。
   * 指令：
       1. 识别并列出所有重复定义的模型、工具函数和视图。
       2. 找出并删除所有未被引用的、无效的或已废弃的代码文件和定义。
       3. 将重复的定义合并为单一可信来源（Single Source of Truth）。
       4. 在清理完成后，确保项目可以成功编译，没有因为删除代码而产生编译错误。
   * 注意： Features/UserManagement/ 目录不存在，所有认证相关UI在 Features/Profile/ 目录下。

  
   * 任务： 深入理解当前的认证架构和问题所在。
   * 指令：
       1. 仔细阅读 Services/AuthenticationService.swift，梳理出当前有问题的登录逻辑。
          注意：LoginViewModel.swift 文件不存在。
       2. 分析 Features/Profile/ 目录下的 SignInView.swift 和 ProfileView.swift，
          理解 UI 和 AuthenticationService 是如何交互的。
       3. 检查 Tests/ManualTestScripts/ 目录下的测试文档，特别是 SignInManualTest.md，
          以了解当前的模拟认证行为。
       

  ---

  第二阶段：核心登录模块重构 

  总要求： 严格禁止自行创造实现方式。所有实现必须严格遵守以下 Firebase官方文档。如果现有代码与官方文档不符，以官方文档为准进行重写。
  核心原则：绝对严格遵守最新的Firebase官方文档。所有实现细节、技术栈选择和代码风格都必须以Firebase官方指南为唯一真理。任何偏离官方文档的实现都将被视为不合格。
  特别注意： 必须保留现有的developer后门登录方式，在重构过程中不得修改或移除此功能。

  
   * 核心指令： 彻底重写 Apple 登录功能。
   * 唯一参考标准： Firebase Official Documentation for Apple Sign-In 
     (https://firebase.google.com/docs/auth/ios/apple)
   * 步骤：
       1. 删除 AuthenticationService.swift 中所有旧的 Apple 登录逻辑（LoginViewModel.swift 不存在）。
       2. 严格按照文档要求，在 Xcode 项目中配置 "Sign in with Apple" 权限。
       3. 实现处理 ASAuthorizationAppleIDCredential 的逻辑，并正确生成用于 Firebase 验证的 nonce。
       4. 使用获取到的凭证调用 Auth.auth().signIn(with:credential:) 来完成 Firebase 认证。
       5. 确保错误能被正确捕获并传递给 UI 层显示。

  
   * 核心指令： 彻底重写 Google 登录功能。
   * 唯一参考标准： Firebase Official Documentation for Google Sign-In 
     (https://firebase.google.com/docs/auth/ios/google-signin)
   * 步骤：
       1. 删除所有旧的 Google 登录逻辑。
       2. 严格按照文档，确保 GoogleService-Info.plist 文件已正确配置，并且 URL Schemes 已在 Info.plist 中设置。
       3. 使用最新的 GoogleSignIn 库来处理认证流程。
       4. 从 GIDGoogleUser 对象中获取 idToken，并用它来创建 GoogleAuthProvider 凭证。
       5. 调用 Firebase 的 signIn 方法完成认证。
       6. 重构 AuthenticationService.swift 以集成新的、符合官方文档的流程。

    彻底重写 email 登录功能
   * 核心指令： 重构 Email/密码注册和登录功能。
   * 唯一参考标准： Firebase Official Documentation for Password-Auth 
     (https://firebase.google.com/docs/auth/ios/password-auth)
   * 步骤：
       1. 检查 SignUpView.swift 和 LoginView.swift 的逻辑。
       2. 确保用户创建严格使用 Auth.auth().createUser(withEmail:password:)。
       3. 确保用户登录严格使用 Auth.auth().signIn(withEmail:password:)。
       4. 实现并优化错误处理机制，能够清晰地处理密码太弱、邮箱已被使用、密码错误等情况。

  
   * 核心指令： 整合所有重构后的模块，并确保整个认证系统无懈可击。
   * 步骤：
       1. 合并由其他代理完成的所有代码更改。
       2. 管理应用的整体认证状态（AuthenticationState），确保用户在登录、登出后，UI（由 AppCoordinator
          控制）能够正确响应和跳转。
       3. 更新并运行 LoginFlowTests.swift 等现有测试，确保它们能够通过。
       4. 为新的实现编写必要的单元测试和 UI 测试，以覆盖之前缺失的测试用例。
       5. 手动执行一次完整的端到端测试，验证 Apple、Google、Email 三种登录方式以及登出流程均完美无缺。

  ---

  重要更新：
  
  经过九位专家团队的深入分析，我们已经创建了详细的重构计划文档 login_refactor_plan.md。
  
  关键发现：
  1. Features/UserManagement/ 目录不存在
  2. LoginViewModel.swift 文件不存在
  3. 当前的 AuthenticationService.swift 虽然包含 Firebase 代码结构，但实际上是模拟实现
  4. 没有发现需要保留的 developer 后门登录（之前的 "developer login" 是一个已修复的 bug）
  
  请参考 login_refactor_plan.md 进行具体实施。
