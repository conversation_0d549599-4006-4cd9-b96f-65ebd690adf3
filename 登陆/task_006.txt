# Task ID: 6
# Title: Implement Apple Sign-In
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Implement real Apple Sign-In authentication flow with Firebase integration.
# Details:
1. Generate cryptographic nonce for Apple Sign-In
2. Create Apple ID request with proper scopes
3. Implement ASAuthorizationControllerDelegate
4. Create Firebase credential from Apple ID token
5. Sign in with Firebase using Apple credential
6. Handle Apple Sign-In errors properly

Implementation code:
```swift
import AuthenticationServices
import CryptoKit

// Add to AuthenticationService.swift
// Property to store the current nonce for Apple Sign In
private var currentNonce: String?

// Generate a random nonce for authentication
private func randomNonceString(length: Int = 32) -> String {
    precondition(length > 0)
    let charset: [Character] = Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
    var result = ""
    var remainingLength = length
    
    while remainingLength > 0 {
        let randoms: [UInt8] = (0 ..< 16).map { _ in
            var random: UInt8 = 0
            let errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random)
            if errorCode != errSecSuccess {
                fatalError("Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)")
            }
            return random
        }
        
        randoms.forEach { random in
            if remainingLength == 0 {
                return
            }
            
            if random < charset.count {
                result.append(charset[Int(random)])
                remainingLength -= 1
            }
        }
    }
    
    return result
}

// Compute the SHA256 hash of a string
private func sha256(_ input: String) -> String {
    let inputData = Data(input.utf8)
    let hashedData = SHA256.hash(data: inputData)
    let hashString = hashedData.compactMap {
        String(format: "%02x", $0)
    }.joined()
    
    return hashString
}

func startSignInWithApple() -> ASAuthorizationAppleIDRequest {
    let nonce = randomNonceString()
    currentNonce = nonce
    let appleIDProvider = ASAuthorizationAppleIDProvider()
    let request = appleIDProvider.createRequest()
    request.requestedScopes = [.fullName, .email]
    request.nonce = sha256(nonce)
    
    return request
}

func handleSignInWithApple(authorization: ASAuthorization) async throws -> User {
    guard let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential else {
        throw AuthError.invalidCredential
    }
    
    guard let nonce = currentNonce else {
        throw AuthError.invalidCredential
    }
    
    guard let appleIDToken = appleIDCredential.identityToken else {
        throw AuthError.invalidCredential
    }
    
    guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
        throw AuthError.invalidCredential
    }
    
    let credential = OAuthProvider.credential(withProviderID: "apple.com",
                                              idToken: idTokenString,
                                              rawNonce: nonce)
    
    do {
        let authResult = try await Auth.auth().signIn(with: credential)
        
        // If this is a new user, save their name to Firestore if available
        if let fullName = appleIDCredential.fullName, 
           let givenName = fullName.givenName,
           let familyName = fullName.familyName {
            // Update user profile
            let changeRequest = authResult.user.createProfileChangeRequest()
            changeRequest.displayName = "\(givenName) \(familyName)"
            try await changeRequest.commitChanges()
        }
        
        return authResult.user
    } catch {
        throw AuthError.from(error)
    }
}
```

# Test Strategy:
Test Apple Sign-In flow: 1) Test nonce generation and SHA256 hashing, 2) Test Apple ID request creation with proper scopes, 3) Test Firebase credential creation from Apple ID token, 4) Test sign in with Firebase using Apple credential, 5) Test error handling for invalid credentials. Use Firebase Auth Emulator for testing when possible.
