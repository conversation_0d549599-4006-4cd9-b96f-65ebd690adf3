# Task ID: 7
# Title: Implement Google Sign-In
# Status: pending
# Dependencies: 4
# Priority: high
# Description: Implement real Google Sign-In authentication flow with Firebase integration.
# Details:
1. Configure Google Sign-In with Firebase client ID
2. Implement proper OAuth flow
3. Get Google credentials and create Firebase credential
4. Sign in with Firebase using Google credential
5. Handle Google Sign-In errors properly
6. Update App.swift to handle Google Sign-In URL

Implementation code:
```swift
import GoogleSignIn

// Add to AuthenticationService.swift
func signInWithGoogle(presenting viewController: UIViewController) async throws -> User {
    guard let clientID = FirebaseApp.app()?.options.clientID else {
        throw AuthError.invalidCredential
    }
    
    // Create Google Sign In configuration object
    let config = GIDConfiguration(clientID: clientID)
    GIDSignIn.sharedInstance.configuration = config
    
    do {
        // Start the sign in flow
        let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: viewController)
        
        guard let idToken = result.user.idToken?.tokenString else {
            throw AuthError.invalidCredential
        }
        
        // Create a Firebase credential with the Google ID token
        let credential = GoogleAuthProvider.credential(withIDToken: idToken,
                                                     accessToken: result.user.accessToken.tokenString)
        
        // Sign in with Firebase using the Google credential
        let authResult = try await Auth.auth().signIn(with: credential)
        return authResult.user
    } catch {
        throw AuthError.from(error)
    }
}

// Add to App.swift
func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
    return GIDSignIn.sharedInstance.handle(url)
}
```

Also update Info.plist to include the Google Sign-In URL scheme:
```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.googleusercontent.apps.YOUR-CLIENT-ID</string>
    </array>
  </dict>
</array>
```

Replace YOUR-CLIENT-ID with the actual client ID from GoogleService-Info.plist (REVERSED_CLIENT_ID value).

# Test Strategy:
Test Google Sign-In flow: 1) Test Google Sign-In configuration with Firebase client ID, 2) Test OAuth flow with proper scopes, 3) Test Firebase credential creation from Google ID token, 4) Test sign in with Firebase using Google credential, 5) Test error handling for invalid credentials, 6) Test URL handling in App.swift. Use Firebase Auth Emulator for testing when possible.
