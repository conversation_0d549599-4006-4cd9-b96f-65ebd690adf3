# Changelog

## Unreleased

- Removed legacy Gemini batch processing paths and prompts:
  - Deleted BatchGeminiProcessingView.swift and BatchGeminiProcessingViewModel.swift
  - Deleted BatchProcessingView(.swift) and BatchProcessingViewModel(.swift)
  - Deleted BatchVisionResultsView(.swift) and BatchVisionResultsViewModel(.swift)
  - Removed GeminiAPIService.extractIngredients(from:) and .analyzeIngredients(in:)
  - Removed deprecated AppRoute cases and navigation helpers for batchProcessing, batchVisionResults, and batchGeminiProcessing
  - Updated AppCoordinator to use only the unified staging → results flow
  - Updated tests accordingly (NavigationCoordinatorTests, AppRouteTests)
- Unified Do Not Eat profile experience:
  - Introduced `DoNotEatSelectionStore` with telemetry hooks and accessibility polish
  - Added integration, migration, and interaction tests covering the consolidated flow
  - Extended recipe generation and cache hashing to honour custom strict exclusions

Migration notes:
- Use StagingView/StagingViewModel for the complete Vision → Gemini pipeline
- For results: call NavigationCoordinator.navigateToResults(ingredients:)
- Any deep links or code paths using legacy batch routes should be updated to staging
