import XCTest
@testable import IngredientScanner

final class MealPlanDateRangeTests: XCTestCase {
    func test_validDateRange_isTodayThroughSevenDays() {
        let calendar = Calendar(identifier: .gregorian)
        var comps = DateComponents()
        comps.year = 2025; comps.month = 1; comps.day = 10; comps.hour = 13; comps.minute = 45
        let now = calendar.date(from: comps)!

        let range = MealPlanDateRange.validDateRange(from: now, calendar: calendar)
        let expectedLower = calendar.startOfDay(for: now)
        let expectedUpper = calendar.date(byAdding: .day, value: 7, to: expectedLower)!

        XCTAssertEqual(range.lowerBound, expectedLower)
        XCTAssertEqual(range.upperBound, expectedUpper)
    }

    func test_isWithinValidRange_andClamp() {
        let calendar = Calendar(identifier: .gregorian)
        let base = calendar.date(from: DateComponents(year: 2025, month: 1, day: 10, hour: 8))!
        let start = calendar.startOfDay(for: base)
        let inside = calendar.date(byAdding: .day, value: 3, to: start)!
        let below = calendar.date(byAdding: .day, value: -2, to: start)!
        let above = calendar.date(byAdding: .day, value: 9, to: start)!

        XCTAssertTrue(MealPlanDateRange.isWithinValidRange(inside, from: base, calendar: calendar))
        XCTAssertFalse(MealPlanDateRange.isWithinValidRange(below, from: base, calendar: calendar))
        XCTAssertFalse(MealPlanDateRange.isWithinValidRange(above, from: base, calendar: calendar))

        XCTAssertEqual(MealPlanDateRange.clamp(below, from: base, calendar: calendar), start)
        let expectedUpper = calendar.date(byAdding: .day, value: 7, to: start)!
        XCTAssertEqual(MealPlanDateRange.clamp(above, from: base, calendar: calendar), expectedUpper)
    }
}

