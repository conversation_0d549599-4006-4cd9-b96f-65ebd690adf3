import Foundation
@preconcurrency import FirebaseAuth
@preconcurrency import FirebaseCore
@preconcurrency import FirebaseFirestore
import AuthenticationServices
@testable import IngredientScanner

/// Test configuration and utilities for AuthenticationService tests
/// 
/// Provides centralized test setup, mock data, and helper methods
/// to ensure consistent test environment across all authentication tests
/// Includes Firebase Emulator Suite support for integration testing
@MainActor
final class AuthenticationTestConfig {
    
    // MARK: - Test Data Constants
    
    struct TestData {
        static let validEmail = "<EMAIL>"
        static let invalidEmail = "invalid-email"
        static let validPassword = "password123" 
        static let weakPassword = "123" // Too short
        static let strongPassword = "strongPassword123"
        static let newUserEmail = "<EMAIL>"
        
        // Firebase test configuration
        static let testProjectID = "test-project-id"
        static let testGoogleAppID = "test-google-app-id"
        static let testGCMSenderID = "test-gcm-sender-id"
        static let testAPIKey = "test-api-key"
        static let testBundleID = "com.test.ingredientscanner"
        
        // Firebase Emulator configuration
        static let authEmulatorHost = "localhost"
        static let authEmulatorPort = 9099
        static let firestoreEmulatorHost = "localhost"
        static let firestoreEmulatorPort = 8080
    }
    
    // MARK: - Firebase Emulator Suite Setup
    
    /// Configure Firebase Emulator Suite for testing
    /// This is the key requirement from task_014.txt
    static func configureFirebaseEmulator() throws {
        guard FirebaseApp.app() == nil else {
            return // Already configured
        }
        
        let options = FirebaseOptions(
            googleAppID: TestData.testGoogleAppID,
            gcmSenderID: TestData.testGCMSenderID
        )
        options.projectID = TestData.testProjectID
        options.apiKey = TestData.testAPIKey
        options.bundleID = TestData.testBundleID
        
        FirebaseApp.configure(options: options)
        
        // Configure Auth Emulator
        if isFirebaseEmulatorAvailable() {
            Auth.auth().useEmulator(withHost: TestData.authEmulatorHost, port: TestData.authEmulatorPort)
            
            // Configure Firestore Emulator
            let settings = FirestoreSettings()
            settings.host = "\(TestData.firestoreEmulatorHost):\(TestData.firestoreEmulatorPort)"
            settings.isSSLEnabled = false
            Firestore.firestore().settings = settings
        }
    }
    
    /// Clean up test data from Firebase Emulator
    static func cleanupTestData() async throws {
        guard isFirebaseEmulatorAvailable() else {
            return
        }
        
        // Sign out current user if any
        if let currentUser = Auth.auth().currentUser {
            try await currentUser.delete()
        }
        
        // Clear Firestore test data
        let db = Firestore.firestore()
        let batch = db.batch()
        
        // Delete test user preferences documents
        let testEmails = [
            TestData.validEmail,
            TestData.newUserEmail,
            "<EMAIL>"
        ]
        
        for email in testEmails {
            // Since we don't have user IDs, we'll query by email if possible
            // For now, just attempt to delete known test documents
            let query = db.collection("userPreferences").whereField("email", isEqualTo: email)
            let documents = try await query.getDocuments()
            
            for document in documents.documents {
                batch.deleteDocument(document.reference)
            }
        }
        
        try await batch.commit()
    }
    
    // MARK: - Firebase Test Setup
    
    /// Configure Firebase for testing environment
    /// Should be called once before any tests that require Firebase
    static func configureFirebaseForTesting() throws {
        guard FirebaseApp.app() == nil else {
            return // Already configured
        }
        
        let options = FirebaseOptions(
            googleAppID: TestData.testGoogleAppID,
            gcmSenderID: TestData.testGCMSenderID
        )
        options.projectID = TestData.testProjectID
        options.apiKey = TestData.testAPIKey
        options.bundleID = TestData.testBundleID
        
        FirebaseApp.configure(options: options)
    }
    
    /// Create a fresh AuthenticationService instance for testing
    static func createTestAuthService() async throws -> AuthenticationService {
        try configureFirebaseForTesting()
        
        let service = AuthenticationService()
        
        // Wait for auth state initialization
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
        
        return service
    }
    
    // MARK: - Test Assertions
    
    /// Verify that a user object has expected properties
    static func verifyUserProperties(_ user: User, expectedEmail: String) {
        XCTAssertNotNil(user)
        XCTAssertEqual(user.email, expectedEmail)
    }
    
    /// Verify authentication service state after successful login
    static func verifyAuthenticatedState(_ service: AuthenticationService) {
        XCTAssertTrue(service.isAuthenticated)
        XCTAssertEqual(service.authState, .authenticated)
        XCTAssertNotNil(service.currentUser)
        XCTAssertNil(service.authError)
        XCTAssertNil(service.lastAuthError)
        XCTAssertFalse(service.isLoading)
    }
    
    /// Verify authentication service state after logout
    static func verifyUnauthenticatedState(_ service: AuthenticationService) {
        XCTAssertFalse(service.isAuthenticated)
        XCTAssertEqual(service.authState, .unauthenticated)
        XCTAssertNil(service.currentUser)
        XCTAssertNil(service.authError)
        XCTAssertNil(service.lastAuthError)
        XCTAssertFalse(service.isLoading)
    }
    
    /// Verify that an AuthError matches expected type
    static func verifyAuthError(_ error: Error, expectedType: AuthenticationService.AuthError) {
        guard let authError = error as? AuthenticationService.AuthError else {
            XCTFail("Expected AuthError, got: \(error)")
            return
        }
        
        switch (authError, expectedType) {
        case (.invalidEmail, .invalidEmail),
             (.weakPassword, .weakPassword),
             (.userNotFound, .userNotFound),
             (.wrongPassword, .wrongPassword),
             (.networkError, .networkError),
             (.userCancelled, .userCancelled),
             (.invalidCredential, .invalidCredential):
            break // Exact match
        case (.unknown(_), .unknown(_)):
            break // Both unknown types
        default:
            XCTFail("Expected \(expectedType), got \(authError)")
        }
    }
    
    /// Verify that error is one of several expected types (for network-dependent tests)
    static func verifyExpectedNetworkErrors(_ error: Error) {
        guard let authError = error as? AuthenticationService.AuthError else {
            XCTFail("Expected AuthError, got: \(error)")
            return
        }
        
        let expectedErrors: [AuthenticationService.AuthError] = [
            .networkError, .userNotFound, .wrongPassword, .googleSignInFailed, .unknown("")
        ]
        
        let isExpectedError = expectedErrors.contains { expectedType in
            switch (authError, expectedType) {
            case (.networkError, .networkError),
                 (.userNotFound, .userNotFound),
                 (.wrongPassword, .wrongPassword),
                 (.googleSignInFailed, .googleSignInFailed):
                return true
            case (.unknown(_), .unknown(_)):
                return true
            default:
                return false
            }
        }
        
        XCTAssertTrue(isExpectedError, "Unexpected error type: \(authError)")
    }
    
    // MARK: - Mock Objects
    
    /// Mock ASAuthorization for Apple Sign-In testing
    class MockASAuthorization: ASAuthorization {
        private let mockCredential: ASAuthorizationCredential
        
        init(credential: ASAuthorizationCredential) {
            self.mockCredential = credential
            super.init()
        }
        
        override var credential: ASAuthorizationCredential {
            return mockCredential
        }
    }
    
    /// Mock credential that doesn't implement ASAuthorizationAppleIDCredential
    class MockInvalidCredential: ASAuthorizationCredential {
        // Empty mock credential for testing invalid credentials
    }
    
    /// Mock Apple ID credential for successful Apple Sign-In testing
    class MockAppleIDCredential: ASAuthorizationAppleIDCredential {
        let mockUser: String
        let mockIdentityToken: Data?
        let mockAuthorizationCode: Data?
        let mockFullName: PersonNameComponents?
        let mockEmail: String?
        let mockRealUserStatus: ASUserDetectionStatus
        
        init(
            user: String = "test.user.123",
            identityToken: Data? = "mock-token".data(using: .utf8),
            authorizationCode: Data? = "mock-code".data(using: .utf8),
            fullName: PersonNameComponents? = nil,
            email: String? = "<EMAIL>",
            realUserStatus: ASUserDetectionStatus = .likelyReal
        ) {
            self.mockUser = user
            self.mockIdentityToken = identityToken
            self.mockAuthorizationCode = authorizationCode
            self.mockFullName = fullName
            self.mockEmail = email
            self.mockRealUserStatus = realUserStatus
            super.init()
        }
        
        override var user: String { mockUser }
        override var identityToken: Data? { mockIdentityToken }
        override var authorizationCode: Data? { mockAuthorizationCode }
        override var fullName: PersonNameComponents? { mockFullName }
        override var email: String? { mockEmail }
        override var realUserStatus: ASUserDetectionStatus { mockRealUserStatus }
    }
    
    // MARK: - Test Environment Detection
    
    /// Check if Firebase Emulator is available
    static func isFirebaseEmulatorAvailable() -> Bool {
        // Check if Firebase Emulator Suite is running
        // This can be detected by checking if the emulator ports are accessible
        let authEmulatorURL = "http://\(TestData.authEmulatorHost):\(TestData.authEmulatorPort)"
        let firestoreEmulatorURL = "http://\(TestData.firestoreEmulatorHost):\(TestData.firestoreEmulatorPort)"
        
        // For CI/testing environments, check environment variable
        if ProcessInfo.processInfo.environment["FIREBASE_EMULATOR_SUITE"] == "true" {
            return true
        }
        
        // For local development, assume emulator is available if running tests
        // In production, this should do actual network checks
        return true // Enable emulator by default for integration tests
    }
    
    /// Skip test if Firebase Emulator is required but not available
    static func skipIfEmulatorNotAvailable() throws {
        guard isFirebaseEmulatorAvailable() else {
            throw XCTSkip("Firebase Auth Emulator not available")
        }
    }
}

// MARK: - XCTest Extensions

extension XCTestCase {
    
    /// Wait for async condition with timeout
    func waitForCondition(
        timeout: TimeInterval = 5.0,
        condition: @escaping () async -> Bool
    ) async throws {
        let deadline = Date().addingTimeInterval(timeout)
        
        while Date() < deadline {
            if await condition() {
                return
            }
            try await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
        }
        
        XCTFail("Condition not met within timeout")
    }
    
    /// Assert that async operation throws specific error type
    func assertThrowsError<T, E: Error>(
        _ expression: @autoclosure () async throws -> T,
        errorType: E.Type,
        file: StaticString = #file,
        line: UInt = #line
    ) async {
        do {
            _ = try await expression()
            XCTFail("Expected error of type \(errorType), but no error was thrown", file: file, line: line)
        } catch {
            XCTAssertTrue(error is E, "Expected error of type \(errorType), got \(type(of: error))", file: file, line: line)
        }
    }
} 