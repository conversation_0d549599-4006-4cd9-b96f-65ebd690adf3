import XCTest
@testable import IngredientScanner

@MainActor
final class ConfigurationSummaryTests: XCTestCase {
    
    func makeViewModel() -> RecipeGeneratorViewModel {
        return RecipeGeneratorViewModel(
            recipeService: ServiceContainer.shared.recipeGenerationService,
            authService: ServiceContainer.shared.authenticationService
        )
    }
    
    func test_configurationSummary_quickMode_returnsEmpty() {
        let vm = makeViewModel()
        vm.mode = .quick
        
        let summary = vm.configurationSummary
        XCTAssertTrue(summary.isEmpty, "Quick mode should return empty summary")
    }
    
    func test_configurationSummary_customMode_emptyConfiguration() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration = CustomConfiguration() // Empty configuration
        
        let summary = vm.configurationSummary
        XCTAssertFalse(summary.isEmpty, "Should return non-empty summary for empty config")
        XCTAssertTrue(summary.contains("select at least one meal"), "Should contain empty state message")
    }
    
    func test_configurationSummary_customMode_basicConfiguration() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = [.breakfast, .dinner]
        vm.customConfiguration.days = 3
        
        let summary = vm.configurationSummary
        XCTAssertFalse(summary.isEmpty)
        XCTAssertTrue(summary.contains("3 days"), "Should include days count")
        XCTAssertTrue(summary.contains("Breakfast") && summary.contains("Dinner"), "Should include meal names")
    }
    
    func test_configurationSummary_customMode_singleDay() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = [.lunch]
        vm.customConfiguration.days = 1
        
        let summary = vm.configurationSummary
        XCTAssertTrue(summary.contains("1 day"), "Should handle singular day correctly")
    }
    
    // Removed in Phase 1: style and ingredient strategy no longer part of configuration/summary
    func test_configurationSummary_customMode_withStyleAndStrategy_removed() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = [.breakfast, .lunch]
        vm.customConfiguration.days = 2

        let summary = vm.configurationSummary
        XCTAssertFalse(summary.contains("Healthy"))
        XCTAssertFalse(summary.contains("Prefer pantry ingredients"))
    }
    
    func test_configurationSummary_customMode_allMealTypes() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = Set(MealType.allCases)
        vm.customConfiguration.days = 1
        
        let summary = vm.configurationSummary
        XCTAssertTrue(summary.contains("Breakfast"), "Should include breakfast")
        XCTAssertTrue(summary.contains("Lunch"), "Should include lunch")
        XCTAssertTrue(summary.contains("Dinner"), "Should include dinner")
    }
    
    func test_configurationSummary_customMode_withCuisine_notIncludedInSummary() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = [.dinner]
        vm.customConfiguration.days = 1
        vm.customConfiguration.cuisines = ["Italian"]

        let summary = vm.configurationSummary
        XCTAssertFalse(summary.isEmpty)
        XCTAssertFalse(summary.contains("Italian"), "Cuisine list should not appear in summary in Phase 1")
    }
    
    func test_configurationSummary_localization() {
        let vm = makeViewModel()
        vm.mode = .custom
        vm.customConfiguration.selectedMeals = [.breakfast]
        vm.customConfiguration.days = 2
        
        let summary = vm.configurationSummary
        
        // Test that localized strings are used (assuming English locale for tests)
        XCTAssertTrue(summary.contains("Planning"), "Should use localized planning text")
        XCTAssertTrue(summary.contains("days"), "Should use localized days text")
    }
    
    func test_mealType_displayNames() {
        XCTAssertEqual(MealType.breakfast.displayName, NSLocalizedString("meal_breakfast", comment: ""))
        XCTAssertEqual(MealType.lunch.displayName, NSLocalizedString("meal_lunch", comment: ""))
        XCTAssertEqual(MealType.dinner.displayName, NSLocalizedString("meal_dinner", comment: ""))
    }
    
    // Legacy enum tests removed per Phase 1 requirements
    // CookingStyle and IngredientStrategy enums have been deleted
}
