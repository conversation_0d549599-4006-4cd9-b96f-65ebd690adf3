import XCTest
@testable import IngredientScanner

final class RecipeGrouperTests: XCTestCase {
    func test_grouping_distributes_across_days_and_meals() {
        let items: [RecipeUIModel] = (1...6).map { i in
            RecipeUIModel(
                id: "\(i)",
                title: "Recipe \(i)",
                estimatedTime: 10,
                ingredientsFromPantry: ["Ingredient A"],
                additionalIngredients: ["Ingredient B"],
                mealType: i % 2 == 0 ? .breakfast : .dinner,
                dayIndex: (i - 1) % 2
            )
        }
        let (sections, summary) = RecipeGrouper.group(items, days: 2, selectedMeals: [.breakfast, .dinner])
        XCTAssertEqual(sections.count, 2)
        XCTAssertGreaterThan(summary.totalRecipes, 0)
        XCTAssertTrue(sections.allSatisfy { !$0.meals.isEmpty })
    }

    func test_pantry_usage_calculation() {
        let items: [RecipeUIModel] = [
            RecipeUIModel(
                id: "1",
                title: "Test Recipe",
                estimatedTime: 10,
                ingredientsFromPantry: ["X", "Y"],
                additionalIngredients: ["Z"],
                mealType: .lunch,
                dayIndex: 0
            )
        ]
        let (sections, _) = RecipeGrouper.group(items, days: 1, selectedMeals: [.lunch])
        let usage = sections.first!.meals.first!.pantryUsage
        XCTAssertEqual(usage.itemsUsed, 2)
        XCTAssertEqual(usage.itemsTotal, 3)
        XCTAssertEqual(usage.utilizationRate, 2.0/3.0, accuracy: 0.0001)
    }

    func test_enhanced_grouping_with_metadata() {
        let items = [
            RecipeUIModel(
                id: "1",
                title: "Italian Pasta",
                estimatedTime: 25,
                ingredientsFromPantry: ["Pasta", "Tomatoes"],
                additionalIngredients: ["Basil"],
                mealType: .dinner,
                dayIndex: 0,
                cuisine: "Italian"
            ),
            RecipeUIModel(
                id: "2",
                title: "Asian Stir Fry",
                estimatedTime: 15,
                ingredientsFromPantry: ["Vegetables"],
                additionalIngredients: ["Soy Sauce"],
                mealType: .lunch,
                dayIndex: 1,
                cuisine: "Asian"
            )
        ]

        let (sections, summary) = RecipeGrouper.group(items, days: 2, selectedMeals: [.lunch, .dinner])

        XCTAssertEqual(sections.count, 2)
        XCTAssertEqual(summary.totalRecipes, 2)
        XCTAssertEqual(summary.cuisineDistribution["Italian"], 1)
        XCTAssertEqual(summary.cuisineDistribution["Asian"], 1)
        XCTAssertEqual(summary.avgCookingTime, 20) // (25 + 15) / 2
    }
}

