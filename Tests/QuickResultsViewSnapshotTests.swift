import XCTest
import <PERSON><PERSON>
@testable import IngredientScanner

final class QuickResultsViewSnapshotTests: XCTestCase {
    func render(_ view: some View, width: CGFloat = 375, height: CGFloat = 812) -> UIImage {
        let controller = UIHostingController(rootView: view)
        controller.view.bounds = CGRect(x: 0, y: 0, width: width, height: height)
        controller.view.backgroundColor = .systemBackground
        let renderer = UIGraphicsImageRenderer(bounds: controller.view.bounds)
        controller.view.layoutIfNeeded()
        return renderer.image { ctx in
            controller.view.drawHierarchy(in: controller.view.bounds, afterScreenUpdates: true)
        }
    }

    func test_idle_loading_failed_snapshots_render() {
        let idle = QuickResultsView(state: .idle)
        let loading = QuickResultsView(state: .loading)
        let failed = QuickResultsView(state: .failed(.serviceError(message: "")))

        XCTAssertNotNil(render(idle))
        XCTAssertNotNil(render(loading))
        XCTAssertNotNil(render(failed))
    }

    func test_loaded_snapshot_renders() {
        let items: [RecipeUIModel] = [
            .init(id: "1", title: "Pasta", subtitle: "Tasty", estimatedTime: 20, imageURL: nil, ingredientsFromPantry: ["Pasta"], additionalIngredients: ["Basil"], difficulty: "easy")
        ]
        let loaded = QuickResultsView(state: .loaded(items))
        XCTAssertNotNil(render(loaded))
    }
}

