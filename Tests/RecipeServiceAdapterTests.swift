import XCTest
@testable import IngredientScanner

final class RecipeServiceAdapterTests: XCTestCase {
    
    func test_pantryOnlyEnforcement() async throws {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        let mockAuthService = MockAuthenticationService()
        
        mockPantryService.pantryItems = [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes)
        ]
        
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService
        )
        
        let request = RecipeGenerationRequest(mode: .quick, pantryItemCount: 2)
        
        // When
        let results = try await adapter.generate(using: request, cookingTimeMinutes: 30, authService: mockAuthService)
        
        // Then
        XCTAssertFalse(results.isEmpty)
        for result in results {
            XCTAssertTrue(result.additionalIngredients.isEmpty, "Phase 1: additionalIngredients must be empty for pantry-only mode")
        }
    }
    
    func test_profileConstraintsApplication() async throws {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        let mockAuthService = MockAuthenticationService()
        
        mockPantryService.pantryItems = [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes)
        ]
        
        // Set up user preferences with restrictions
        let userPrefs = UserPreferences(
            userId: "test",
            familySize: 4,
            dietaryRestrictions: [.vegetarian],
            allergiesIntolerances: [.treeNuts],
            respectRestrictions: true
        )
        mockAuthService.userPreferences = userPrefs
        
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService
        )
        
        let request = RecipeGenerationRequest(mode: .quick, pantryItemCount: 2)
        
        // When
        let results = try await adapter.generate(using: request, cookingTimeMinutes: 30, authService: mockAuthService)
        
        // Then
        XCTAssertFalse(results.isEmpty)
        // Verify that profile constraints were applied (family size = 4)
        XCTAssertTrue(mockRecipeService.lastPreferences?.numberOfServings == 4)
        XCTAssertTrue(mockRecipeService.lastPreferences?.respectRestrictions == true)
    }

    func test_generateWithoutPrefetch_stillReturnsResults() async throws {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        mockPantryService.pantryItems = [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes)
        ]
        let mockAuthService = MockAuthenticationService()
        let spyPrefetcher = SpyRecipeDetailPrefetcher()
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService,
            detailPrefetcher: spyPrefetcher
        )

        let request = RecipeGenerationRequest(mode: .quick, pantryItemCount: 2)

        // When
        let results = try await adapter.generate(using: request, cookingTimeMinutes: 30, authService: mockAuthService, prefetchDetails: false)

        // Then
        XCTAssertFalse(results.isEmpty)
        let callCount = await spyPrefetcher.invocationCount()
        XCTAssertEqual(callCount, 0, "Prefetch should not run when disabled")
    }

    func test_quickMode_prefetchesByDefault() async throws {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        mockPantryService.pantryItems = [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes)
        ]
        let mockAuthService = MockAuthenticationService()
        let spyPrefetcher = SpyRecipeDetailPrefetcher()
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService,
            detailPrefetcher: spyPrefetcher
        )

        let request = RecipeGenerationRequest(mode: .quick, pantryItemCount: 2)

        // When
        _ = try await adapter.generate(using: request, cookingTimeMinutes: 30, authService: mockAuthService)

        // Then
        let callCount = await spyPrefetcher.invocationCount()
        XCTAssertEqual(callCount, 1, "Quick mode should prefetch details by default")
    }

    func test_structuredMealPlan_generationDisablesPrefetch() async throws {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        mockPantryService.pantryItems = [
            Ingredient(name: "Chicken", category: .proteins),
            Ingredient(name: "Rice", category: .grainsPastaLegumes)
        ]
        let mockAuthService = MockAuthenticationService()
        let spyPrefetcher = SpyRecipeDetailPrefetcher()
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService,
            detailPrefetcher: spyPrefetcher
        )

        let generator = StructuredMealPlanGenerator(
            adapter: adapter,
            pantryService: mockPantryService,
            authService: mockAuthService,
            cutoffManager: MealCutoffManager()
        )

        let mealConfig = MealConfig(cookingTimeMinutes: 30, numberOfDishes: 1)
        var components = DateComponents()
        components.year = 2024
        components.month = 3
        components.day = 15
        components.hour = 10
        let calendar = Calendar(identifier: .gregorian)
        let referenceDate = calendar.date(from: components)!
        let request = MealPlanGenerationRequest(
            startDate: referenceDate,
            days: 1,
            selectedMeals: [.dinner],
            slotConfigs: [.dinner: mealConfig]
        )

        // When
        _ = try await generator.generatePlan(request, now: referenceDate)

        // Then
        let callCount = await spyPrefetcher.invocationCount()
        XCTAssertEqual(callCount, 0, "Structured meal plan path must not prefetch details")
    }
    
    func test_emptyPantryThrowsError() async {
        // Given
        let mockRecipeService = MockRecipeGenerationService()
        let mockPantryService = MockPantryService()
        let mockAuthService = MockAuthenticationService()
        
        mockPantryService.pantryItems = [] // Empty pantry
        
        let adapter = RecipeServiceAdapter(
            recipeService: mockRecipeService,
            pantryService: mockPantryService
        )
        
        let request = RecipeGenerationRequest(mode: .quick, pantryItemCount: 0)
        
        // When/Then
        do {
            _ = try await adapter.generate(using: request, cookingTimeMinutes: 30, authService: mockAuthService)
            XCTFail("Expected RecipeGenerationError.noPantryItems")
        } catch RecipeGenerationError.noPantryItems {
            // Expected
        } catch {
            XCTFail("Expected RecipeGenerationError.noPantryItems, got \(error)")
        }
    }
}

// MARK: - Mock Services

class MockRecipeGenerationService: RecipeGenerationServiceProtocol {
    var lastPreferences: RecipePreferences?
    
    func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
        lastPreferences = preferences
        
        let recipe = Recipe(
            recipeTitle: "Test Recipe",
            description: "A test recipe",
            ingredients: ingredients,
            instructions: ["Cook it"],
            nutrition: Recipe.NutritionInfo(calories: "200", protein: "10g", carbs: "20g", fat: "5g"),
            cookingTime: "30 minutes",
            servings: preferences.numberOfServings,
            difficulty: .easy
        )
        
        return [RecipeIdea(recipe: recipe, status: .readyToCook, missingIngredients: [])]
    }
}

class MockPantryService: PantryService {
    var pantryItems: [Ingredient] = []
    
    override init() {
        super.init()
    }
}

class MockAuthenticationService: AuthenticationService {
    var userPreferences: UserPreferences?
    
    override init() {
        super.init()
    }
}

actor SpyRecipeDetailPrefetcher: RecipeDetailPrefetching {
    private var callCount: Int = 0

    func prefetchDetails(for models: [RecipeUIModel], pantryIngredients: [String], userPreferences: UserPreferences?) async {
        _ = (models, pantryIngredients, userPreferences)
        callCount += 1
    }

    func invocationCount() async -> Int {
        callCount
    }
}
