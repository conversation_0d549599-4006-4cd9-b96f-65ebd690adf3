import XCTest
@testable import IngredientScanner

@MainActor
final class RecipeGeneratorTimeoutTests: XCTestCase {
    actor StubPlanService: RecipeGenerationServiceProtocol {
        var planResult: Result<MealPlan, Error>

        init(planResult: Result<MealPlan, Error>) {
            self.planResult = planResult
        }

        func generateMealIdeas(from ingredients: [String], preferences: RecipePreferences) async throws -> [RecipeIdea] {
            []
        }

        func generateStructuredMealPlan(
            _ planRequest: MealPlanGenerationRequest,
            pantryService: PantryService,
            authService: AuthenticationService
        ) async throws -> MealPlan {
            switch planResult {
            case .success(let plan):
                return plan
            case .failure(let error):
                throw error
            }
        }
    }

    func makeCustomViewModel(service: RecipeGenerationServiceProtocol) -> RecipeGeneratorViewModel {
        let vm = RecipeGeneratorViewModel(
            recipeService: service,
            authService: ServiceContainer.shared.authenticationService
        )
        vm.mode = .custom
        vm.pantryState = .hasItems(count: 2)
        vm.customConfiguration.selectedMeals = [ .dinner ]
        vm.customConfiguration.mealConfigurations[.dinner] = MealConfig(cookingTimeMinutes: 30, numberOfDishes: 1)
        return vm
    }

    func test_customGeneration_handlesTimeoutErrorGracefully() async {
        let stub = StubPlanService(planResult: .failure(TimeoutError()))
        let vm = makeCustomViewModel(service: stub)

        await vm.generateRecipes(cookingTimeMinutes: 30)

        await waitUntilFailed(viewModel: vm)

        guard case .failed(let error) = vm.viewState else {
            XCTFail("Expected failed state")
            return
        }

        switch error {
        case .serviceError(let message):
            XCTAssertEqual(message, NSLocalizedString("error_generation_timeout", comment: ""))
        default:
            XCTFail("Expected service error for timeout")
        }
    }

    private func waitUntilFailed(viewModel: RecipeGeneratorViewModel, timeout: TimeInterval = 1.0) async {
        let deadline = Date().addingTimeInterval(timeout)
        while Date() < deadline {
            if case .failed = viewModel.viewState {
                return
            }
            try? await Task.sleep(nanoseconds: 50_000_000)
        }
        XCTFail("Timed out waiting for viewState to enter failed state")
    }
}
