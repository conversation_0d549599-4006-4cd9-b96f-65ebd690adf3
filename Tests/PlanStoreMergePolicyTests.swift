import XCTest
@testable import IngredientScanner

final class PlanStoreMergePolicyTests: XCTestCase {
    @MainActor
    override func tearDown() {
        PlanStore.shared.clearAll()
        FavoritesStore.shared.clearAll()
        super.tearDown()
    }

    @MainActor
    func testAppendAllAddsNewMealsWithoutReplacing() {
        PlanStore.shared.clearAll()
        FavoritesStore.shared.clearAll()

        let baseDate = Self.makeDate(year: 2024, month: 1, day: 8)
        let existingRecipe = RecipeUIModel(
            id: "existing",
            title: "Existing Dish",
            mealType: .lunch,
            dayIndex: 0,
            scheduledDate: baseDate
        )
        let newRecipe = RecipeUIModel(
            id: "new",
            title: "New Dish",
            mealType: .lunch,
            dayIndex: 0,
            scheduledDate: baseDate
        )

        let existingSlot = MealSlot(slotId: UUID(), dayIndex: 0, mealType: .lunch, recipe: existingRecipe)
        let existingDay = DayPlan(date: baseDate, meals: [existingSlot])
        _ = PlanStore.shared.mergeAndSave(newPlan: MealPlan(days: [existingDay]))

        let newSlot = MealSlot(slotId: UUID(), dayIndex: 0, mealType: .lunch, recipe: newRecipe)
        let newDay = DayPlan(date: baseDate, meals: [newSlot])
        let summary = PlanStore.shared.mergeAndSave(newPlan: MealPlan(days: [newDay]), policy: .appendAll)

        let storedPlan = PlanStore.shared.loadLastMealPrep()?.plan
        let storedMeals = storedPlan?.days.first?.meals ?? []
        XCTAssertEqual(storedMeals.count, 2)
        XCTAssertTrue(storedMeals.contains(where: { $0.recipe.id == "existing" }))
        XCTAssertTrue(storedMeals.contains(where: { $0.recipe.id == "new" }))

        XCTAssertEqual(summary.replaced, 0)
        XCTAssertEqual(summary.skippedFavorites, 0)
        XCTAssertEqual(summary.added, 1)
        XCTAssertEqual(summary.addedCountsByDayMeal.count, 1)
        guard let summaryDate = summary.addedCountsByDayMeal.keys.first else {
            XCTFail("Expected summary date")
            return
        }
        XCTAssertTrue(Self.calendar.isDate(summaryDate, inSameDayAs: baseDate))
        XCTAssertEqual(summary.addedCountsByDayMeal[summaryDate]?[.lunch], 1)
    }

    private static let calendar: Calendar = {
        var calendar = Calendar(identifier: .gregorian)
        calendar.timeZone = TimeZone(secondsFromGMT: 0)!
        return calendar
    }()

    private static func makeDate(year: Int, month: Int, day: Int) -> Date {
        var components = DateComponents()
        components.year = year
        components.month = month
        components.day = day
        components.hour = 0
        components.minute = 0
        components.second = 0
        components.timeZone = TimeZone(secondsFromGMT: 0)
        return calendar.date(from: components) ?? Date()
    }
}
