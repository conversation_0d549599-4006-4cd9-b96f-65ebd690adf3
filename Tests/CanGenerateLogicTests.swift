import XCTest
@testable import IngredientScanner

@MainActor
final class CanGenerateLogicTests: XCTestCase {
    func makeVM() -> RecipeGeneratorViewModel {
        let vm = RecipeGeneratorViewModel(recipeService: ServiceContainer.shared.recipeGenerationService, authService: ServiceContainer.shared.authenticationService)
        return vm
    }

    func test_canGenerate_false_whenPantryEmpty() {
        let vm = makeVM()
        vm.pantryState = .empty
        vm.viewState = .idle
        vm.mode = .quick
        XCTAssertFalse(vm.canGenerate)
    }

    func test_canGenerate_false_whenLoading() {
        let vm = makeVM()
        vm.pantryState = .hasItems(count: 1)
        vm.viewState = .loading
        vm.mode = .quick
        XCTAssertFalse(vm.canGenerate)
    }

    func test_canGenerate_quick_true_whenPantryHasItems_andNotLoading() {
        let vm = makeVM()
        vm.pantryState = .hasItems(count: 2)
        vm.viewState = .idle
        vm.mode = .quick
        XCTAssertTrue(vm.canGenerate)
    }

    func test_canGenerate_custom_validOnlyWithRequiredFields() {
        let vm = makeVM()
        vm.pantryState = .hasItems(count: 3)
        vm.viewState = .idle
        vm.mode = .custom
        // Missing required fields
        XCTAssertFalse(vm.canGenerate)
        // Add required
        vm.customConfiguration.selectedMeals = [.breakfast, .dinner]
        vm.customConfiguration.mealConfigurations[.breakfast] = .init(cookingTimeMinutes: 30, numberOfDishes: 1)
        vm.customConfiguration.mealConfigurations[.dinner] = .init(cookingTimeMinutes: 30, numberOfDishes: 2)
        vm.customConfiguration.days = 3
        XCTAssertTrue(vm.canGenerate)
        // Out of bounds
        vm.customConfiguration.days = 99
        XCTAssertFalse(vm.canGenerate)
    }

    func test_canGenerate_pantryStateVariations() {
        let vm = makeVM()
        vm.viewState = .idle
        vm.mode = .quick

        // Test different pantry states
        vm.pantryState = .loading
        XCTAssertFalse(vm.canGenerate, "Should not generate while pantry is loading")

        vm.pantryState = .error("Network error")
        XCTAssertFalse(vm.canGenerate, "Should not generate when pantry has error")

        vm.pantryState = .hasItems(count: 5)
        XCTAssertTrue(vm.canGenerate, "Should generate when pantry has items")
    }

    func test_configurationSummary_customMode() {
        let vm = makeVM()
        vm.mode = .custom

        // Empty configuration
        let emptySummary = vm.configurationSummary
        XCTAssertTrue(emptySummary.contains("select at least one meal"), "Should show empty state message")

        // With meals selected
        vm.customConfiguration.selectedMeals = [.breakfast, .lunch]
        vm.customConfiguration.days = 2

        let summary = vm.configurationSummary
        XCTAssertTrue(summary.contains("2 days"), "Should include days count")
        XCTAssertTrue(summary.contains("Breakfast") && summary.contains("Lunch"), "Should include meal types")
    }
}

