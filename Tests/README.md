# Authentication Tests

This directory contains comprehensive unit and integration tests for the AuthenticationService in the Ingredient Scanner app.

## Test Structure

### Unit Tests (`AuthenticationServiceTests.swift`)
- **425 lines** of comprehensive unit tests
- Tests all authentication methods (Email/Password, Apple Sign-In, Google Sign-In)
- Tests error handling and validation
- Tests user preferences sync
- Uses mock objects and test configurations

### Integration Tests (`AuthenticationIntegrationTests.swift`)
- **500+ lines** of Firebase integration tests
- Tests complete authentication flows with Firebase
- Tests Firestore sync functionality
- Tests error scenarios and state persistence
- **Requires Firebase Emulator Suite for full functionality**

### Test Configuration (`AuthenticationTestConfig.swift`)
- **249 lines** of centralized test configuration
- Provides mock objects and test data
- Handles Firebase Emulator Suite setup
- Contains helper methods for test assertions

## Firebase Emulator Suite Setup

### Prerequisites
1. Install Firebase CLI:
   ```bash
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

### Running Integration Tests

#### Option 1: Manual Emulator Setup
1. Initialize Firebase in your project directory:
   ```bash
   firebase init emulators
   ```

2. Select Auth and Firestore emulators when prompted

3. Start the emulator suite:
   ```bash
   firebase emulators:start --only auth,firestore
   ```

4. Run the integration tests in Xcode or via command line:
   ```bash
   xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15'
   ```

#### Option 2: Automated Setup (Recommended)
Set the environment variable and run tests:
```bash
export FIREBASE_EMULATOR_SUITE=true
firebase emulators:exec --only auth,firestore "xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15'"
```

### Emulator Configuration
- **Auth Emulator**: `localhost:9099`
- **Firestore Emulator**: `localhost:8080`
- **Project ID**: `test-project-id`

### Test Coverage

#### Authentication Flow Tests
- ✅ Complete authentication flow with Firebase
- ✅ Authentication and Firestore sync integration
- ✅ Error scenarios with Firebase integration
- ✅ State persistence across app restarts

#### Firebase Integration Tests
- ✅ Firebase Auth integration
- ✅ Firestore sync after authentication
- ✅ Error handling with Firebase services
- ✅ State consistency across service restarts

## Running Tests

### Unit Tests Only
Unit tests run without Firebase Emulator and use mock objects:
```bash
xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:IngredientScannerTests/AuthenticationServiceTests
```

### Integration Tests Only
Integration tests require Firebase Emulator Suite:
```bash
export FIREBASE_EMULATOR_SUITE=true
firebase emulators:exec --only auth,firestore "xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15' -only-testing:IngredientScannerTests/AuthenticationIntegrationTests"
```

### All Tests
Run both unit and integration tests:
```bash
export FIREBASE_EMULATOR_SUITE=true
firebase emulators:exec --only auth,firestore "xcodebuild test -scheme IngredientScanner -destination 'platform=iOS Simulator,name=iPhone 15'"
```

## Test Data Cleanup

The integration tests automatically clean up test data after each test run. If you need to manually clear emulator data:

```bash
firebase emulators:exec --only auth,firestore "curl -X DELETE http://localhost:9099/emulator/v1/projects/test-project-id/accounts && curl -X DELETE http://localhost:8080/emulator/v1/projects/test-project-id/databases/(default)/documents"
```

## Manual Test Scripts

The `ManualTestScripts/` directory contains manual testing procedures:

- `SignInManualTest.md` - Manual sign-in flow testing
- `FamilyInfoManualTest.md` - Family preferences testing

## Troubleshooting

### Common Issues

1. **Emulator not starting**: Ensure ports 9099 and 8080 are not in use
2. **Tests failing**: Verify Firebase Emulator Suite is running before test execution
3. **Network errors**: Check that emulator configuration matches test settings

### Debug Mode

To enable debug logging during tests, set the environment variable:
```bash
export TASKMASTER_LOG_LEVEL=debug
```

## Test Success Criteria

All tests should pass when:
- Firebase Emulator Suite is properly configured and running
- All authentication methods work correctly
- Firestore sync operates as expected
- Error handling provides appropriate feedback
- State persistence works across app restarts

**Target**: 100% test coverage for authentication flows and Firebase integration 