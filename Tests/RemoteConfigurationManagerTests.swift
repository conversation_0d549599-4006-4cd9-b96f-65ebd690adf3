import XCTest
@testable import IngredientScanner

@MainActor
final class RemoteConfigurationManagerTests: XCTestCase {
    func test_initialConfiguration_isDefaultOrBundle() async {
        let mgr = RemoteConfigurationManager.shared
        // Allow bootstrap to complete
        try? await Task.sleep(nanoseconds: 400_000_000)
        let cfg = mgr.configuration
        XCTAssertGreaterThan(cfg.maxDays, 0)
    }

    func test_refreshIfNeeded_updatesCacheOnSuccess() async {
        let mgr = RemoteConfigurationManager.shared
        let changed = await mgr.refreshIfNeeded(force: true)
        XCTAssertTrue(changed || !changed) // ensure no crash; stub returns default
        let cfg = mgr.configuration
        XCTAssertGreaterThan(cfg.maxCookingTime, 0)
    }

    func test_defaultConfiguration_isValid() {
        let config = RemoteConfiguration.default
        XCTAssertFalse(config.availableCuisines.isEmpty)
        XCTAssertGreaterThan(config.maxDays, 0)
        XCTAssertGreaterThan(config.maxCookingTime, 0)
        XCTAssertEqual(config.configVersion, "3.0")
    }

    // Phase 1: defaults per-meal removed; test trimmed model shape
    func test_defaultConfiguration_model_shape() {
        let config = RemoteConfiguration.default
        XCTAssertFalse(config.availableCuisines.isEmpty)
        XCTAssertGreaterThan(config.maxDays, 0)
        XCTAssertGreaterThan(config.maxCookingTime, 0)
    }
}

