import XCTest
@testable import IngredientScanner

/// Test suite for pantry deletion functionality
/// Created by Jax 'The Guardian' Ko<PERSON> (Sentinel) to prevent regression
class PantryDeletionTests: XCTestCase {
    
    var pantryService: PantryService!
    var viewModel: PantryViewModel!
    
    override func setUp() {
        super.setUp()
        pantryService = PantryService()
        viewModel = PantryViewModel(pantryService: pantryService)
        
        // Add test ingredients
        let testIngredients = [
            Ingredient(name: "test apple", category: .produce, dateAdded: Date()),
            Ingredient(name: "test banana", category: .produce, dateAdded: Date()),
            Ingredient(name: "test chicken", category: .proteins, dateAdded: Date()),
            Ingredient(name: "test milk", category: .dairy, dateAdded: Date()),
            Ingredient(name: "test bread", category: .bakery, dateAdded: Date())
        ]
        
        Task {
            await pantryService.addIngredients(testIngredients)
        }
    }
    
    override func tearDown() {
        pantryService = nil
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Safety Tests
    
    func testDeleteSelectedItems_EmptySelection() {
        // Given: No items selected
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
        let initialCount = pantryService.pantryItems.count
        
        // When: Attempting to delete
        viewModel.deleteSelectedItems()
        
        // Then: Nothing should be deleted
        XCTAssertEqual(pantryService.pantryItems.count, initialCount)
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
    }
    
    func testDeleteSelectedItems_SingleItem() async {
        // Given: One item selected
        await waitForPantryLoad()
        let firstItem = pantryService.pantryItems.first!
        viewModel.selectedItems.insert(firstItem.id)
        let initialCount = pantryService.pantryItems.count
        
        // When: Deleting selected items
        viewModel.deleteSelectedItems()
        
        // Give async operation time to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then: Only selected item should be deleted
        XCTAssertEqual(pantryService.pantryItems.count, initialCount - 1)
        XCTAssertFalse(pantryService.pantryItems.contains { $0.id == firstItem.id })
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
        XCTAssertFalse(viewModel.isEditMode)
    }
    
    func testDeleteSelectedItems_MultipleItems() async {
        // Given: Multiple items selected
        await waitForPantryLoad()
        let itemsToDelete = Array(pantryService.pantryItems.prefix(3))
        for item in itemsToDelete {
            viewModel.selectedItems.insert(item.id)
        }
        let initialCount = pantryService.pantryItems.count
        
        // When: Deleting selected items
        viewModel.deleteSelectedItems()
        
        // Give async operation time to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then: Only selected items should be deleted
        XCTAssertEqual(pantryService.pantryItems.count, initialCount - 3)
        for item in itemsToDelete {
            XCTAssertFalse(pantryService.pantryItems.contains { $0.id == item.id })
        }
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
        XCTAssertFalse(viewModel.isEditMode)
    }
    
    func testDeleteSelectedItems_MassDeleteionPrevention() async {
        // Given: Attempting to delete more than 50% of items
        await waitForPantryLoad()
        let totalItems = pantryService.pantryItems.count
        let itemsToSelect = pantryService.pantryItems // Select ALL items
        for item in itemsToSelect {
            viewModel.selectedItems.insert(item.id)
        }
        
        // When: Attempting mass deletion
        viewModel.deleteSelectedItems()
        
        // Give async operation time to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then: Mass deletion should be prevented (this depends on implementation)
        // Note: Current implementation allows full deletion, but logs warning
        // In production, you might want to add UI confirmation for mass deletion
        print("🔍 Test: Total items before: \(totalItems), after: \(pantryService.pantryItems.count)")
    }
    
    func testDeleteSelectedItems_InvalidIDs() async {
        // Given: Selected items include invalid IDs
        await waitForPantryLoad()
        let validItem = pantryService.pantryItems.first!
        let invalidID = UUID() // Non-existent ID
        
        viewModel.selectedItems.insert(validItem.id)
        viewModel.selectedItems.insert(invalidID)
        
        let initialCount = pantryService.pantryItems.count
        
        // When: Deleting with mixed valid/invalid IDs
        viewModel.deleteSelectedItems()
        
        // Give async operation time to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        
        // Then: Only valid item should be deleted
        XCTAssertEqual(pantryService.pantryItems.count, initialCount - 1)
        XCTAssertFalse(pantryService.pantryItems.contains { $0.id == validItem.id })
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
    }
    
    // MARK: - UI State Tests
    
    func testToggleItemSelection() async {
        // Given: Items loaded
        await waitForPantryLoad()
        let testItem = pantryService.pantryItems.first!
        
        // When: Toggling selection
        viewModel.toggleItemSelection(testItem.id)
        
        // Then: Item should be selected
        XCTAssertTrue(viewModel.selectedItems.contains(testItem.id))
        XCTAssertTrue(viewModel.isItemSelected(testItem))
        
        // When: Toggling again
        viewModel.toggleItemSelection(testItem.id)
        
        // Then: Item should be deselected
        XCTAssertFalse(viewModel.selectedItems.contains(testItem.id))
        XCTAssertFalse(viewModel.isItemSelected(testItem))
    }
    
    func testSelectAllItems() async {
        // Given: Items loaded
        await waitForPantryLoad()
        let totalItems = pantryService.pantryItems.count
        
        // When: Selecting all items
        viewModel.selectAllItems()
        
        // Then: All items should be selected
        XCTAssertEqual(viewModel.selectedItems.count, totalItems)
        for item in pantryService.pantryItems {
            XCTAssertTrue(viewModel.selectedItems.contains(item.id))
        }
    }
    
    func testToggleEditMode() {
        // Given: Not in edit mode
        XCTAssertFalse(viewModel.isEditMode)
        
        // When: Toggling edit mode
        viewModel.toggleEditMode()
        
        // Then: Should be in edit mode
        XCTAssertTrue(viewModel.isEditMode)
        
        // When: Toggling again
        viewModel.toggleEditMode()
        
        // Then: Should exit edit mode and clear selections
        XCTAssertFalse(viewModel.isEditMode)
        XCTAssertTrue(viewModel.selectedItems.isEmpty)
    }
    
    // MARK: - Helper Methods
    
    private func waitForPantryLoad() async {
        // Wait for pantry items to load
        for _ in 0..<10 { // Max 1 second wait
            if !pantryService.pantryItems.isEmpty {
                break
            }
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        }
    }
} 