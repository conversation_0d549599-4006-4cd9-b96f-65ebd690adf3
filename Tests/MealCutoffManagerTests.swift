import XCTest
@testable import IngredientScanner

final class MealCutoffManagerTests: XCTestCase {
    private let calendar = Calendar(identifier: .gregorian)

    private func date(_ y: Int, _ m: Int, _ d: Int, _ h: Int, _ min: Int) -> Date {
        calendar.date(from: DateComponents(year: y, month: m, day: d, hour: h, minute: min))!
    }

    func test_defaultCutoffs_hasPassedCutoff() {
        let mgr = MealCutoffManager()
        let morning = date(2025, 1, 10, 9, 0)
        let afterBreakfast = date(2025, 1, 10, 11, 0)
        let midday = date(2025, 1, 10, 13, 0)
        let afterLunch = date(2025, 1, 10, 15, 0)
        let evening = date(2025, 1, 10, 20, 0)
        let late = date(2025, 1, 10, 22, 0)

        XCTAssertFalse(mgr.hasPassedCutoff(for: .breakfast, now: morning, calendar: calendar))
        XCTAssertTrue(mgr.hasPassedCutoff(for: .breakfast, now: afterBreakfast, calendar: calendar))

        XCTAssertFalse(mgr.hasPassedCutoff(for: .lunch, now: midday, calendar: calendar))
        XCTAssertTrue(mgr.hasPassedCutoff(for: .lunch, now: afterLunch, calendar: calendar))

        XCTAssertFalse(mgr.hasPassedCutoff(for: .dinner, now: evening, calendar: calendar))
        XCTAssertTrue(mgr.hasPassedCutoff(for: .dinner, now: late, calendar: calendar))
    }

    func test_shouldSkipSameDay_onlyOnSameDate() {
        let mgr = MealCutoffManager()
        let now = date(2025, 1, 10, 15, 0) // 3pm
        let today = calendar.startOfDay(for: now)
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: today)!

        // Lunch at 3pm should have passed today
        XCTAssertTrue(mgr.shouldSkipSameDay(meal: .lunch, on: today, now: now, calendar: calendar))
        // Same time but slot is tomorrow → should not skip
        XCTAssertFalse(mgr.shouldSkipSameDay(meal: .lunch, on: tomorrow, now: now, calendar: calendar))
    }

    func test_customCutoffs_areConfigurable() {
        let custom = MealCutoffManager.Cutoffs(
            breakfast: DateComponents(hour: 9, minute: 0),
            lunch: DateComponents(hour: 12, minute: 0),
            dinner: DateComponents(hour: 18, minute: 0)
        )
        let mgr = MealCutoffManager(cutoffs: custom)

        let noon = date(2025, 1, 10, 12, 0)
        XCTAssertTrue(mgr.hasPassedCutoff(for: .breakfast, now: noon, calendar: calendar)) // 12:00 > 9:00
        XCTAssertFalse(mgr.hasPassedCutoff(for: .lunch, now: noon, calendar: calendar))     // == 12:00 not passed
        XCTAssertFalse(mgr.hasPassedCutoff(for: .dinner, now: noon, calendar: calendar))    // 12:00 < 18:00
    }
}

