import XCTest
@testable import IngredientScanner

@MainActor
final class DoNotEatViewTests: XCTestCase {

    func testLoadFromPreferencesPopulatesSelections() {
        let store = DoNotEatSelectionStore()
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.allergiesIntolerances = [.eggs, .milk]
        preferences.strictExclusions = [.garlic]
        preferences.customStrictExclusions = ["  Kale  "]

        store.load(from: preferences)

        XCTAssertTrue(store.hasLoaded)
        XCTAssertFalse(store.hasChanges)
        XCTAssertEqual(store.selectedAllergies, Set([.eggs, .milk]))
        XCTAssertEqual(store.selectedStrictEnums, Set([.garlic]))
        XCTAssertEqual(store.customEntries.map { $0.displayText }, ["Kale"])
        XCTAssertEqual(store.summaryChips.map { $0.title }, ["Eggs", "Milk", "Garlic", "<PERSON><PERSON>"])
        XCTAssertEqual(store.totalSelectionCount, 4)
    }

    func testAddingCustomEntryPromotesEnumMatches() {
        let store = DoNotEatSelectionStore()
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.strictExclusions = [.garlic]

        store.load(from: preferences)
        XCTAssertFalse(store.hasChanges)

        store.addCustomEntry("Garlic ")

        XCTAssertTrue(store.customEntries.isEmpty)
        XCTAssertEqual(store.selectedStrictEnums, Set([.garlic]))
        XCTAssertFalse(store.hasChanges)
    }

    func testCustomEntryFlowAddsSanitizedValue() {
        let store = DoNotEatSelectionStore()
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.customStrictExclusions = []

        store.load(from: preferences)
        store.addCustomEntry("  Roasted   Peppers  ")

        XCTAssertEqual(store.customEntries.map { $0.displayText }, ["Roasted Peppers"])
        XCTAssertTrue(store.hasChanges)

        let dedupe = store.dedupeResultForSave()
        XCTAssertEqual(dedupe.customStrings, ["Roasted Peppers"])
        XCTAssertTrue(dedupe.normalizedKeys.contains("roasted peppers"))
    }

    func testTogglingStrictRemovesMatchingCustomEntry() {
        let store = DoNotEatSelectionStore()
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.customStrictExclusions = ["Garlic"]

        store.load(from: preferences)
        XCTAssertEqual(store.customEntries.count, 1)

        store.toggleStrict(.garlic)

        XCTAssertTrue(store.selectedStrictEnums.contains(.garlic))
        XCTAssertTrue(store.customEntries.isEmpty)
        XCTAssertTrue(store.hasChanges)

        store.markSaved()
        XCTAssertFalse(store.hasChanges)
    }
}
