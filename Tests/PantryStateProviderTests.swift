import XCTest
@testable import IngredientScanner

@MainActor
final class PantryStateProviderTests: XCTestCase {
    
    func test_pantryState_isEmpty() {
        let emptyState = PantryState.empty
        XCTAssertTrue(emptyState.isEmpty)
        XCTAssertFalse(emptyState.hasItems)
        
        let hasItemsState = PantryState.hasItems(count: 5)
        XCTAssertFalse(hasItemsState.isEmpty)
        XCTAssertTrue(hasItemsState.hasItems)
        
        let loadingState = PantryState.loading
        XCTAssertFalse(loadingState.isEmpty)
        XCTAssertFalse(loadingState.hasItems)
        
        let errorState = PantryState.error("Test error")
        XCTAssertFalse(errorState.isEmpty)
        XCTAssertFalse(errorState.hasItems)
    }
    
    func test_defaultPantryStateProvider_currentState() {
        let mockPantryService = PantryService()
        let provider = DefaultPantryStateProvider(pantryService: mockPantryService)
        
        // Initially should be empty since mock service has no items
        let initialState = provider.currentState
        XCTAssertEqual(initialState, .empty)
    }
    
    func test_defaultPantryStateProvider_checkPantryState() async {
        let mockPantryService = PantryService()
        let provider = DefaultPantryStateProvider(pantryService: mockPantryService)
        
        let state = await provider.checkPantryState()
        XCTAssertEqual(state, .empty)
    }
    
    func test_pantryStateProvider_observeChanges() async {
        let mockPantryService = PantryService()
        let provider = DefaultPantryStateProvider(pantryService: mockPantryService)
        
        var receivedStates: [PantryState] = []
        let expectation = XCTestExpectation(description: "Receive initial state")
        
        let task = Task {
            var iterator = provider.observePantryChanges().makeAsyncIterator()
            if let firstState = await iterator.next() {
                receivedStates.append(firstState)
                expectation.fulfill()
            }
        }
        
        await fulfillment(of: [expectation], timeout: 2.0)
        task.cancel()
        
        XCTAssertEqual(receivedStates.count, 1)
        XCTAssertEqual(receivedStates.first, .empty)
    }
}

// MARK: - Mock PantryStateProvider for Testing

class MockPantryStateProvider: PantryStateProvider {
    var currentState: PantryState = .loading
    private var stateSequence: [PantryState] = []
    private var sequenceIndex = 0
    
    init(initialState: PantryState = .loading, stateSequence: [PantryState] = []) {
        self.currentState = initialState
        self.stateSequence = stateSequence
    }
    
    func checkPantryState() async -> PantryState {
        return currentState
    }
    
    func observePantryChanges() -> AsyncStream<PantryState> {
        AsyncStream { continuation in
            continuation.yield(currentState)
            
            // Simulate state changes from sequence
            let task = Task {
                for state in stateSequence {
                    try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 second
                    if !Task.isCancelled {
                        self.currentState = state
                        continuation.yield(state)
                    }
                }
            }
            
            continuation.onTermination = { _ in
                task.cancel()
            }
        }
    }
    
    func simulateStateChange(to newState: PantryState) {
        currentState = newState
    }
}
