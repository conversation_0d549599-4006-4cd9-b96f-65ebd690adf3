import XCTest
@testable import IngredientScanner

final class UserPreferencesCustomExclusionsTests: XCTestCase {

    func testDecodingLegacyPreferencesDefaultsCustomArray() throws {
        let json = "{\"userId\":\"legacy-user\"}"
        let data = Data(json.utf8)
        let decoder = JSONDecoder()

        let preferences = try decoder.decode(UserPreferences.self, from: data)

        XCTAssertTrue(preferences.customStrictExclusions.isEmpty)
    }

    func testEncodingIncludesCustomStrictExclusions() throws {
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.customStrictExclusions = ["Kale", "Blue Cheese"]

        let encoder = JSONEncoder()
        let data = try encoder.encode(preferences)
        let object = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let storedCustom = object?["customStrictExclusions"] as? [String]

        XCTAssertEqual(storedCustom ?? [], ["<PERSON><PERSON>", "Blue Cheese"])
    }
}
