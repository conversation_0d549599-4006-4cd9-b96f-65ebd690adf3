import XCTest
@testable import IngredientScanner

final class AsyncTimeoutTests: XCTestCase {
    func test_withTimeout_succeedsBeforeDeadline() async throws {
        let value: String = try await withTimeout(1.0) {
            try await Task.sleep(nanoseconds: 50_000_000)
            return "done"
        }
        XCTAssertEqual(value, "done")
    }

    func test_withTimeout_throwsTimeoutError() async {
        do {
            _ = try await withTimeout(0.05) {
                try await Task.sleep(nanoseconds: 150_000_000)
                return 1
            }
            XCTFail("Expected timeout")
        } catch {
            XCTAssertTrue(error is TimeoutError)
        }
    }

    func test_withTimeout_clampsLowerBound() async throws {
        let result: Int = try await withTimeout(0.0) {
            return 42
        }
        XCTAssertEqual(result, 42)
    }
}
