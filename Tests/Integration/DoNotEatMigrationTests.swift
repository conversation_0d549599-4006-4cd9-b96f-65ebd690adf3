import XCTest
@testable import IngredientScanner

final class DoNotEatMigrationTests: XCTestCase {

    func testSavedUserPreferencesRoundTripPreservesCustomExclusions() {
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.allergiesIntolerances = [.eggs]
        preferences.strictExclusions = [.garlic]
        preferences.customStrictExclusions = ["Kale", "Blue Cheese"]

        let saved = SavedUserPreferences.from(preferences)
        let restored = saved.toUserPreferences()

        XCTAssertEqual(restored.customStrictExclusions, ["Kale", "Blue Cheese"])
        XCTAssertEqual(restored.strictExclusions, [.garlic])
        XCTAssertEqual(restored.allergiesIntolerances, [.eggs])
    }

    func testLegacySavedUserPreferencesDefaultsCustomArray() {
        let saved = SavedUserPreferences(userId: "legacy")
        let restored = saved.toUserPreferences()

        XCTAssertTrue(restored.customStrictExclusions.isEmpty)
        XCTAssertTrue(restored.strictExclusions.isEmpty)
        XCTAssertTrue(restored.allergiesIntolerances.isEmpty)
    }

    func testRecipePreferencesInitializationIncludesCustomExclusions() {
        var preferences = UserPreferences.createDefault(for: "user")
        preferences.strictExclusions = [.beef]
        preferences.allergiesIntolerances = [.soybeans]
        preferences.customStrictExclusions = ["Mushrooms"]

        let recipePreferences = RecipePreferences(from: preferences, cookingTime: 25)

        XCTAssertEqual(recipePreferences.strictExclusions, ["Beef"])
        XCTAssertEqual(recipePreferences.allergiesAndIntolerances, ["Soybeans"])
        XCTAssertEqual(recipePreferences.customStrictExclusions, ["Mushrooms"])

        let combined = DoNotEatHelper.buildCombinedExclusionSet(from: preferences)
        XCTAssertTrue(combined.contains("beef"))
        XCTAssertTrue(combined.contains("soybeans"))
        XCTAssertTrue(combined.contains("mushrooms"))
        XCTAssertEqual(combined.count, 3)
    }
}
