import XCTest
@testable import IngredientScanner

final class TelemetryFeedbackTests: XCTestCase {
    func test_user_feedback_event_like() {
        let e = TelemetryEventBuilder.user_feedback(like: true, rating: nil)
        XCTAssertEqual(e.name, "user_feedback")
        XCTAssertEqual(e.params["like"] as? Bool, true)
        XCTAssertNil(e.params["rating"])    }

    func test_user_feedback_event_rating() {
        let e = TelemetryEventBuilder.user_feedback(like: nil, rating: 4)
        XCTAssertEqual(e.name, "user_feedback")
        XCTAssertEqual(e.params["rating"] as? Int, 4)
        XCTAssertNil(e.params["like"])    }
}

