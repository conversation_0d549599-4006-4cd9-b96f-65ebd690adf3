import XCTest
import Swift<PERSON>
@preconcurrency import <PERSON>base<PERSON>uth
@preconcurrency import FirebaseCore
@preconcurrency import FirebaseFirestore
import AuthenticationServices
@testable import IngredientScanner

/// Integration tests for AuthenticationService Firebase integration
/// 
/// Tests Firebase authentication integration, state management, and user preferences sync
/// Focuses on testing the complete authentication flow and state consistency
/// Includes Firebase Emulator Suite support for comprehensive testing
@MainActor
final class AuthenticationIntegrationTests: XCTestCase {
    
    var authService: AuthenticationService!
    var db: Firestore!
    
    override func setUp() async throws {
        super.setUp()
        
        // Set up Firebase Emulator Suite
        try AuthenticationTestConfig.configureFirebaseEmulator()
        
        authService = try await AuthenticationTestConfig.createTestAuthService()
        db = Firestore.firestore()
    }
    
    override func tearDown() async throws {
        // Clean up test data
        if let user = authService.currentUser {
            try? await authService.signOut()
        }
        try? await AuthenticationTestConfig.cleanupTestData()
        
        authService = nil
        db = nil
        super.tearDown()
    }
    
    // MARK: - Firebase Auth Integration Tests
    
    /// Test complete authentication flow with Firebase
    func testCompleteAuthenticationFlow() async throws {
        // Skip if emulator not available
        try AuthenticationTestConfig.skipIfEmulatorNotAvailable()
        
        // Start in unauthenticated state
        XCTAssertEqual(authService.authState, .unauthenticated)
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertNil(authService.currentUser)
        
        // Test email authentication
        let testEmail = AuthenticationTestConfig.TestData.validEmail
        let testPassword = AuthenticationTestConfig.TestData.validPassword
        
        // Create account
        let user = try await authService.createAccount(email: testEmail, password: testPassword)
        XCTAssertNotNil(user)
        
        // Wait for auth state to update
        try await waitForCondition(timeout: 3.0) {
            return self.authService.authState == .authenticated
        }
        
        // Verify authenticated state
        AuthenticationTestConfig.verifyAuthenticatedState(authService)
        XCTAssertEqual(authService.currentUser?.displayName, user.displayName)
        
        // Test sign out
        try authService.signOut()
        
        // Wait for state to update
        try await waitForCondition(timeout: 3.0) {
            return self.authService.authState == .unauthenticated
        }
        
        // Verify unauthenticated state
        AuthenticationTestConfig.verifyUnauthenticatedState(authService)
    }
    
    /// Test authentication and Firestore sync integration
    func testAuthenticationAndFirestoreSync() async throws {
        // Skip if emulator not available
        try AuthenticationTestConfig.skipIfEmulatorNotAvailable()
        
        let testEmail = AuthenticationTestConfig.TestData.newUserEmail
        let testPassword = AuthenticationTestConfig.TestData.strongPassword
        
        // Create account and authenticate
        let user = try await authService.createAccount(email: testEmail, password: testPassword)
        XCTAssertNotNil(user)
        
        // Wait for authentication state to stabilize
        try await waitForCondition(timeout: 3.0) {
            return self.authService.authState == .authenticated && self.authService.userPreferences != nil
        }
        
        // Verify user preferences were fetched from Firestore
        XCTAssertNotNil(authService.userPreferences)
        XCTAssertEqual(authService.userPreferences?.userId, user.uid)
        
        // Update user preferences
        guard var preferences = authService.userPreferences else {
            XCTFail("User preferences should not be nil after authentication")
            return
        }
        
        preferences.theme = "dark"
        preferences.notifications = true
        preferences.familySize = 4
        preferences.dietaryRestrictions = ["vegetarian", "gluten-free"]
        
        // Save preferences to Firestore
        try await authService.saveUserPreferences(preferences)
        
        // Wait for sync to complete
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Verify preferences were updated
        XCTAssertEqual(authService.userPreferences?.theme, "dark")
        XCTAssertEqual(authService.userPreferences?.notifications, true)
        XCTAssertEqual(authService.userPreferences?.familySize, 4)
        XCTAssertEqual(authService.userPreferences?.dietaryRestrictions, ["vegetarian", "gluten-free"])
        
        // Verify data persistence in Firestore
        let docRef = db.collection("userPreferences").document(user.uid)
        let document = try await docRef.getDocument()
        
        XCTAssertTrue(document.exists)
        let data = document.data()
        XCTAssertEqual(data?["theme"] as? String, "dark")
        XCTAssertEqual(data?["notifications"] as? Bool, true)
        XCTAssertEqual(data?["familySize"] as? Int, 4)
        
        // Sign out and verify state cleanup
        try authService.signOut()
        
        try await waitForCondition(timeout: 3.0) {
            return self.authService.authState == .unauthenticated
        }
        
        XCTAssertNil(authService.currentUser)
        XCTAssertEqual(authService.authState, .unauthenticated)
        // Note: userPreferences persist locally after sign out
        XCTAssertNotNil(authService.userPreferences)
    }
    
    /// Test error scenarios with Firebase integration
    func testFirebaseIntegrationErrorScenarios() async throws {
        // Skip if emulator not available
        try AuthenticationTestConfig.skipIfEmulatorNotAvailable()
        
        // Test authentication with invalid credentials
        do {
            _ = try await authService.signInWithEmail("<EMAIL>", password: "wrongpassword")
            XCTFail("Should fail with invalid credentials")
        } catch let error as AuthenticationService.AuthError {
            XCTAssertEqual(error, .userNotFound)
        }
        
        // Test account creation with existing email
        let existingEmail = AuthenticationTestConfig.TestData.validEmail
        let password = AuthenticationTestConfig.TestData.validPassword
        
        // First, create the account
        _ = try await authService.createAccount(email: existingEmail, password: password)
        
        // Try to create account with same email
        do {
            _ = try await authService.createAccount(email: existingEmail, password: password)
            XCTFail("Should fail with existing email")
        } catch let error as AuthenticationService.AuthError {
            // Should be email already in use error
            XCTAssertTrue([.emailAlreadyInUse, .unknown("")].contains { expectedError in
                switch (error, expectedError) {
                case (.emailAlreadyInUse, .emailAlreadyInUse):
                    return true
                case (.unknown(_), .unknown(_)):
                    return true
                default:
                    return false
                }
            })
        }
        
        // Clean up
        try? authService.signOut()
    }
    
    /// Test state persistence across app restarts
    func testStatePersistenceAcrossRestarts() async throws {
        // Skip if emulator not available
        try AuthenticationTestConfig.skipIfEmulatorNotAvailable()
        
        let testEmail = "<EMAIL>"
        let testPassword = AuthenticationTestConfig.TestData.strongPassword
        
        // Create and authenticate user
        let user = try await authService.createAccount(email: testEmail, password: testPassword)
        XCTAssertNotNil(user)
        
        // Wait for full authentication
        try await waitForCondition(timeout: 3.0) {
            return self.authService.authState == .authenticated
        }
        
        // Store current state
        let originalUserId = authService.currentUser?.uid
        let originalPreferences = authService.userPreferences
        
        // Simulate app restart by creating new AuthenticationService instance
        let newAuthService = try await AuthenticationTestConfig.createTestAuthService()
        
        // Wait for auth state to be restored
        try await waitForCondition(timeout: 5.0) {
            return newAuthService.authState != .initializing
        }
        
        // Verify state persistence
        if newAuthService.authState == .authenticated {
            XCTAssertEqual(newAuthService.currentUser?.uid, originalUserId)
            XCTAssertEqual(newAuthService.currentUser?.email, testEmail)
            
            // Wait for preferences to load
            try await waitForCondition(timeout: 3.0) {
                return newAuthService.userPreferences != nil
            }
            
            // Verify preferences persistence
            XCTAssertNotNil(newAuthService.userPreferences)
            XCTAssertEqual(newAuthService.userPreferences?.userId, originalUserId)
            XCTAssertEqual(newAuthService.userPreferences?.familySize, originalPreferences?.familySize)
        }
        
        // Clean up
        try? newAuthService.signOut()
    }
    
    // MARK: - Auth State Listener Tests
    
    /// Test that auth state listener is properly registered
    func testAuthStateListenerRegistration() async throws {
        // The auth state should be initialized
        XCTAssertTrue([.initializing, .unauthenticated, .authenticated].contains(authService.authState))
        
        // Wait for auth state to stabilize
        try await waitForCondition(timeout: 2.0) {
            return self.authService.authState != .initializing
        }
        
        // After initialization, state should be consistent
        if authService.authState == .authenticated {
            XCTAssertTrue(authService.isAuthenticated)
            XCTAssertNotNil(authService.currentUser)
        } else {
            XCTAssertFalse(authService.isAuthenticated)
            XCTAssertNil(authService.currentUser)
        }
    }
    
    /// Test auth state changes are properly handled
    func testAuthStateChanges() async throws {
        // Start in unauthenticated state
        if authService.authState == .authenticated {
            authService.signOutSilently()
            try await waitForCondition(timeout: 2.0) {
                return self.authService.authState == .unauthenticated
            }
        }
        
        XCTAssertEqual(authService.authState, .unauthenticated)
        XCTAssertFalse(authService.isAuthenticated)
        XCTAssertNil(authService.currentUser)
    }
    
    // MARK: - User Preferences Integration Tests
    
    /// Test user preferences initialization
    func testUserPreferencesInitialization() {
        XCTAssertNotNil(authService.userPreferences)
        
        let preferences = authService.userPreferences!
        
        // Test that preferences have expected default structure
        XCTAssertNotNil(preferences.familySize)
        XCTAssertNotNil(preferences.dietaryRestrictions)
        XCTAssertNotNil(preferences.allergies)
        
        // Test additional fields from Task 9
        XCTAssertNotNil(preferences.userId)
        XCTAssertNotNil(preferences.theme)
        XCTAssertNotNil(preferences.notifications)
        XCTAssertNotNil(preferences.lastUpdated)
    }
    
    /// Test local preferences loading
    func testLocalPreferencesLoading() {
        // UserPreferences should be loaded from UserDefaults
        XCTAssertNotNil(authService.userPreferences)
        
        // Test that preferences persist across service reinitializations
        let originalPreferences = authService.userPreferences
        
        // Create new service instance
        let newService = AuthenticationService()
        
        // Should have same preferences structure
        XCTAssertNotNil(newService.userPreferences)
        XCTAssertEqual(newService.userPreferences?.familySize, originalPreferences?.familySize)
    }
    
    /// Test preferences update functionality
    func testPreferencesUpdate() async throws {
        guard var preferences = authService.userPreferences else {
            XCTFail("User preferences not initialized")
            return
        }
        
        // Update preferences
        let originalFamilySize = preferences.familySize
        preferences.familySize = originalFamilySize + 1
        preferences.theme = "dark"
        preferences.notifications = true
        
        // Update preferences in service
        await authService.updatePreferences(preferences)
        
        // Verify update
        XCTAssertEqual(authService.userPreferences?.familySize, originalFamilySize + 1)
        XCTAssertEqual(authService.userPreferences?.theme, "dark")
        XCTAssertEqual(authService.userPreferences?.notifications, true)
    }
    
    // MARK: - Error Handling Integration Tests
    
    /// Test comprehensive error handling flow
    func testErrorHandlingFlow() {
        // Test that errors are properly processed and stored
        let testError = NSError(domain: AuthErrorDomain, code: AuthErrorCode.networkError.rawValue, userInfo: nil)
        
        authService.handleAuthError(testError)
        
        // Verify error is converted and stored
        XCTAssertNotNil(authService.lastAuthError)
        XCTAssertEqual(authService.lastAuthError, .networkError)
        XCTAssertEqual(authService.authError, AuthenticationService.AuthError.networkError.localizedDescription)
    }
    
    /// Test user cancellation error handling
    func testUserCancellationErrorHandling() {
        // Test Apple Sign-In cancellation
        let appleError = ASAuthorizationError(_nsError: NSError(
            domain: ASAuthorizationErrorDomain,
            code: ASAuthorizationError.canceled.rawValue,
            userInfo: nil
        ))
        
        authService.handleAuthError(appleError)
        
        // User cancellation should not show error message
        XCTAssertNil(authService.authError)
        XCTAssertEqual(authService.lastAuthError, .userCancelled)
    }
    
    /// Test error clearing functionality
    func testErrorClearing() {
        // Set test errors
        authService.authError = "Test error message"
        authService.lastAuthError = .networkError
        
        XCTAssertNotNil(authService.authError)
        XCTAssertNotNil(authService.lastAuthError)
        
        // Clear errors through sign out
        authService.signOutSilently()
        
        // Verify errors are cleared
        XCTAssertNil(authService.authError)
        XCTAssertNil(authService.lastAuthError)
    }
    
    // MARK: - Validation Integration Tests
    
    /// Test email validation with various formats
    func testEmailValidationComprehensive() {
        // Valid emails
        let validEmails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in validEmails {
            XCTAssertTrue(authService.isValidEmail(email), "Email should be valid: \(email)")
        }
        
        // Invalid emails
        let invalidEmails = [
            "",
            "invalid-email",
            "@domain.com",
            "user@",
            "user@.com",
            "user@@domain.com",
            "user.domain.com",
            "user @domain.com",
            "user@domain .com"
        ]
        
        for email in invalidEmails {
            XCTAssertFalse(authService.isValidEmail(email), "Email should be invalid: \(email)")
        }
    }
    
    /// Test password validation with various lengths
    func testPasswordValidationComprehensive() {
        // Valid passwords (6+ characters)
        let validPasswords = [
            "123456",
            "password",
            "pass123",
            "strongPassword123",
            "verylongpasswordwithmanycharacters"
        ]
        
        for password in validPasswords {
            XCTAssertTrue(authService.isValidPassword(password), "Password should be valid: \(password)")
        }
        
        // Invalid passwords (< 6 characters)
        let invalidPasswords = [
            "",
            "1",
            "12",
            "123",
            "1234",
            "12345"
        ]
        
        for password in invalidPasswords {
            XCTAssertFalse(authService.isValidPassword(password), "Password should be invalid: \(password)")
        }
    }
    
    // MARK: - Loading State Integration Tests
    
    /// Test loading state management during operations
    func testLoadingStateManagement() async throws {
        // Initially should not be loading
        XCTAssertFalse(authService.isLoading)
        
        // Test loading state during email authentication attempt
        let task = Task {
            do {
                _ = try await authService.signInWithEmail("<EMAIL>", password: "password123")
            } catch {
                // Expected to fail without emulator
            }
        }
        
        // Wait for completion
        await task.value
        
        // Loading should be reset
        XCTAssertFalse(authService.isLoading)
    }
    
    /// Test loading state during sign out
    func testLoadingStateDuringSignOut() {
        XCTAssertFalse(authService.isLoading)
        
        // Sign out (should complete quickly)
        authService.signOutSilently()
        
        // Loading should be reset
        XCTAssertFalse(authService.isLoading)
    }
    
    // MARK: - Apple Sign-In Integration Tests
    
    /// Test Apple Sign-In request generation
    func testAppleSignInRequestGeneration() {
        let request = authService.startSignInWithApple()
        
        // Verify request properties
        XCTAssertNotNil(request)
        XCTAssertEqual(request.requestedScopes, [.fullName, .email])
        XCTAssertNotNil(request.nonce)
        
        // Nonce should be properly hashed
        XCTAssertNotNil(request.nonce)
        
        // Test multiple requests generate different nonces
        let request2 = authService.startSignInWithApple()
        XCTAssertNotEqual(request.nonce, request2.nonce)
    }
    
    /// Test Apple Sign-In with mock credentials
    func testAppleSignInWithMockCredentials() async throws {
        // Test with invalid credential type
        let invalidAuth = AuthenticationTestConfig.MockASAuthorization(
            credential: AuthenticationTestConfig.MockInvalidCredential()
        )
        
        do {
            _ = try await authService.handleSignInWithApple(authorization: invalidAuth)
            XCTFail("Should fail with invalid credential")
        } catch let error as AuthenticationService.AuthError {
            XCTAssertEqual(error, .invalidCredential)
        }
    }
    
    // MARK: - Reset Password Integration Tests
    
    /// Test password reset functionality
    func testPasswordReset() async throws {
        // Test with valid email
        do {
            try await authService.resetPassword(email: AuthenticationTestConfig.TestData.validEmail)
            // Should succeed or fail with network error (without emulator)
        } catch let error as AuthenticationService.AuthError {
            // Expected errors without emulator
            AuthenticationTestConfig.verifyExpectedNetworkErrors(error)
        }
    }
    
    /// Test password reset with invalid email
    func testPasswordResetWithInvalidEmail() async throws {
        do {
            try await authService.resetPassword(email: AuthenticationTestConfig.TestData.invalidEmail)
            XCTFail("Should fail with invalid email")
        } catch let error as AuthenticationService.AuthError {
            XCTAssertEqual(error, .invalidEmail)
        }
    }
    
    // MARK: - State Consistency Tests
    
    /// Test that authentication state remains consistent
    func testAuthenticationStateConsistency() async throws {
        // Wait for initial state to stabilize
        try await waitForCondition(timeout: 2.0) {
            return self.authService.authState != .initializing
        }
        
        // Test state consistency
        if authService.authState == .authenticated {
            XCTAssertTrue(authService.isAuthenticated)
            XCTAssertNotNil(authService.currentUser)
        } else if authService.authState == .unauthenticated {
            XCTAssertFalse(authService.isAuthenticated) 
            XCTAssertNil(authService.currentUser)
        }
        
        // Loading should not be stuck in true state
        XCTAssertFalse(authService.isLoading)
    }
    
    /// Test preferences persistence across authentication state changes
    func testPreferencesPersistenceAcrossAuthStates() async throws {
        // Get initial preferences
        let initialPreferences = authService.userPreferences
        XCTAssertNotNil(initialPreferences)
        
        // Sign out (if authenticated)
        if authService.isAuthenticated {
            authService.signOutSilently()
            
            try await waitForCondition(timeout: 2.0) {
                return !self.authService.isAuthenticated
            }
        }
        
        // Preferences should still exist after sign out
        XCTAssertNotNil(authService.userPreferences)
        
        // Basic structure should be maintained
        XCTAssertEqual(authService.userPreferences?.familySize, initialPreferences?.familySize)
    }
}

// MARK: - Test Helper Extensions

extension AuthenticationService {
    /// Expose validation methods for testing
    func isValidEmail(_ email: String) -> Bool {
        return self.isValidEmail(email)  // Calls private method
    }
    
    func isValidPassword(_ password: String) -> Bool {
        return self.isValidPassword(password)  // Calls private method
    }
} 